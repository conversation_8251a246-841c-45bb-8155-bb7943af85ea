# 项目上下文传递指令

## 背景说明

当前对话已达到较长长度，需要开启新对话继续项目开发。新对话中的AI助手将无法访问本次对话的历史记录，因此需要完整的项目上下文传递。

## 核心任务

请作为项目交接专家，为新对话中的AI助手准备一份完整的项目状态报告，确保项目能够无缝衔接。

## 具体要求

### 1. 项目概览

- 项目名称、目标和当前阶段

- 技术栈和架构概述

- 主要功能模块说明

### 2. 文件清单（必须完整列出）

**已修改的文件：**

- 列出所有在本次对话中创建或修改的文件

- 标注每个文件的主要变更内容

- 说明文件间的依赖关系

**参考文件：**

- 列出所有作为参考或配置的相关文件

- 说明这些文件在项目中的作用

### 3. 当前进度状态

- 已完成的功能点

- 正在进行的任务

- 待解决的问题或bug

- 下一步计划

### 4. 关键决策记录

- 重要的技术选型决策

- 架构设计要点

- 需要特别注意的约束条件

### 5. 环境和依赖

- 开发环境配置要求

- 必要的依赖包或工具

- 数据库结构或配置信息

## 输出格式

请以结构化的Markdown格式输出，确保新对话中的AI助手能够快速理解项目全貌并立即投入工作。