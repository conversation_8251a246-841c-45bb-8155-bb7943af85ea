---
tags:
  - 提示词
  - AI交接
  - 工作流程
  - Obsidian
  - 笔记模板
  - 标准化
---

# 04-截图转笔记标准化提示词

## 🎯 工作流程概述

当用户提供截图内容并要求创建Obsidian笔记时，请按照以下标准化流程操作：

## 📋 第一步：内容分析

> [!important] 内容提取要求
> 1. **完整阅读**截图中的所有文字内容
> 2. **识别主题**：确定内容的核心主题和分类
> 3. **提取要点**：找出关键信息、数据、时间线、步骤等
> 4. **保持原意**：保留原作者的语言风格和表达方式
> 5. **注意细节**：包括数字、时间、人名、公司名等准确信息

## 📝 第二步：笔记结构设计

### 🏗️ 标准结构模板

```markdown
---
tags:
  - [根据内容确定相关标签]
---

# [编号]-[笔记标题]

## 🎯 [核心观点/背景介绍]

> [!info/warning/tip] [小节标题]
> [核心内容摘要]

## 📋 [主要内容区域]

### [子标题1]

> [!example/note/important] [具体要点]
> [详细内容]

### [子标题2]

> [!success/abstract] [具体要点]
> [详细内容]

## 💡 [关键洞察/总结]

> [!quote/tip] [总结性内容]
> [核心要点列表]
```

## 🎨 第三步：Obsidian格式规范

### Callouts使用指南

| Callout类型 | 使用场景 | 示例 |
|------------|----------|------|
| `[!info]` | 一般信息说明 | 背景介绍、基本概念 |
| `[!tip]` | 建议和技巧 | 学习方法、操作建议 |
| `[!warning]` | 警告和注意事项 | 常见错误、避坑指南 |
| `[!important]` | 重要信息 | 核心要点、关键数据 |
| `[!example]` | 举例说明 | 具体案例、实际操作 |
| `[!success]` | 成功经验 | 最佳实践、正面案例 |
| `[!note]` | 补充说明 | 额外信息、备注 |
| `[!quote]` | 引用和总结 | 原文引用、个人感悟 |
| `[!abstract]` | 摘要总结 | 概括性内容 |
| `[!question]` | 疑问讨论 | 常见问题、互动内容 |

### 格式要求

> [!tip] 格式标准
> - **粗体**：突出关键词汇、重要概念
> - 编号列表：有序步骤、时间线
> - 无序列表：要点归纳、特性列举
> - 表格：对比数据、结构化信息
> - 引用块：原文内容、重要声明

## 🏷️ 第四步：标签系统

### 标签分类原则

> [!important] 标签设计规范
> 1. **主题标签**：内容的核心领域（如：Java、学习方法、面试准备）
> 2. **类型标签**：内容的性质（如：经验分享、技术教程、工具使用）
> 3. **应用标签**：使用场景（如：求职、学习、项目开发）
> 4. **技术标签**：涉及的具体技术（如：SpringBoot、MySQL、Redis）
> 5. **状态标签**：内容状态（如：进行中、已完成、待更新）

### 标签数量控制

> [!note] 标签建议
> - 每个笔记控制在**8-12个标签**之间
> - 优先选择**高频使用**的标签
> - 避免过于细分的标签
> - 保持标签的**一致性**和**可维护性**

## 📊 第五步：质量检查

### 内容完整性检查

> [!abstract] 检查清单
> - [ ] 截图内容是否完整转录
> - [ ] 重要信息是否突出显示
> - [ ] 结构层次是否清晰
> - [ ] Callouts使用是否合适
> - [ ] 标签是否准确全面
> - [ ] 格式是否符合Obsidian规范

### 文件命名规范

> [!tip] 命名标准
> - 使用**编号+主题**的格式：`001-主题名称.md`
> - 编号与目录内现有文件保持连续
> - 主题名称简洁明确，避免特殊字符
> - 使用中文命名，符合项目整体风格

## 🔄 第六步：后续维护

### 关联建设

> [!success] 建设建议
> - 在相关笔记中添加**双向链接**
> - 创建**MOC（Map of Content）**整理同类内容
> - 定期**更新标签**和**重新分类**
> - 根据使用情况**优化结构**

## 💼 实际应用示例

### 学习方法类笔记

```markdown
---
tags:
  - 学习方法
  - SDT学习法
  - 费曼技巧
  - 实践导向
  - 效率提升
---

# 007-SDT学习法-看做教

## 🎯 核心观点

> [!warning] 学习的两个极端问题
> - 具体问题描述

## 📋 SDT 学习公式

> [!tip] SDT = See + Do + Teach
> 公式说明
```

### 技术经验类笔记

```markdown
---
tags:
  - Java
  - 后端开发
  - 学习路线
  - 面试准备
  - 技术栈
---

# 002-Java后端速成路线

## 🎯 核心理念

> [!info] 路线设计思路
> 设计思路说明

## 📚 详细学习计划

### 第1阶段：基础准备
> [!example] 具体步骤
> 详细内容
```

## 🎯 关键成功要素

> [!quote] 核心原则
> 1. **忠实还原**：准确转录截图内容，保持原意
> 2. **结构化呈现**：使用清晰的层次结构组织信息
> 3. **格式统一**：遵循Obsidian标准，保持风格一致
> 4. **标签精准**：选择恰当标签，便于检索和关联
> 5. **持续优化**：根据使用反馈不断改进格式和结构

---

> [!tip] 使用提示
> 此提示词适用于所有截图转笔记的场景，请根据具体内容调整细节，但始终遵循这个基本框架和质量标准。 