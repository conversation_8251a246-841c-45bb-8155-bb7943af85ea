---
tags:
  - type/summary
  - topic/career
aliases: ["职业思考汇总"]
creation-date: {{date:YYYY-MM-DD}}
---

# 职业思考阅读汇总

本文档汇总了关于职业发展、求职与个人成长的相关阅读笔记。

---

## [我就是不想 grow - 一份 career 不发展挣扎记录](https://blog.douchi.space/fight-career-growth-trap/?utm_source=related#gsc.tab=0)

> [!abstract] 摘要
> 作者回顾了自己职业生涯中各个阶段对于"成长"和"晋升"的复杂心态，从顺其自然到被迫遵从，再到最终主动选择抵抗传统的职业发展陷阱（Career Growth Trap），坚持做一名满足于现状的资深工程师。

### 我的想法
这篇文章深刻探讨了个人价值与外部职业期望之间的冲突，对那些质疑传统"爬梯子"式职业发展模式的人来说，提供了宝贵的精神共鸣和另一种选择的可能性。

---

## [被裁记](https://blog.douchi.space/layoff/#gsc.tab=0)

> [!abstract] 摘要
> 作者分享了在2024年初被美国科技公司裁员的亲身经历，回顾其职业生涯，并记录了被裁后的心路历程与后续安排。

### 我的想法
这篇文章真实记录了科技行业从业者面对裁员的复杂心境与现实考量，对于规划职业路径、思考工作意义以及处理职场变动具有很好的参考价值。

---

## [2024 浪潮退去平台期的找工总结](https://blog.douchi.space/job-hunting-2024/#gsc.tab=0)

> [!abstract] 摘要
> 作者在被裁五个月后，于2024年重新投入求职市场。文章详细对比了2021年与2024年求职市场的巨大差异，分享了在简历、面试、心态管理等方面的经验教训与深刻反思。

### 我的想法
这篇总结对当前技术行业的求职困境有非常具体和真实的描绘，不仅提供了实用的建议，更引发了关于职业预期和个人价值的深入思考，对求职者和职场人士都有启发。

---

## [美国码农前半段职业发展道路（career ladder）]({https://blog.douchi.space/tags/career/#gsc.tab=0})

> [!abstract] 摘要
> 本文科普了软件工程师的职业阶梯（Career Ladder），旨在帮助从业者了解标准的职业发展路径，以减少信息差带来的焦虑或被误导的可能。

### 我的想法
可作为理解科技公司职级体系的基础读物。

---

## [工作十年了]({待补充链接})

> [!abstract] 摘要
> 作者在从事软件开发十年之际，回顾了自己并不追求极致成长（"当 day job 来干"）的职业历程，分享了在多家不同类型公司工作的个人感受。

### 我的想法
提供了一个资深但非典型"卷王"的工程师视角，对思考长期职业定位有参考价值。

---

## [（没有干货）一些草台班子码农求职话术]({待补充链接})

> [!abstract] 摘要
> 以相对轻松和调侃的方式，分享了作者作为经验丰富的"划水"工程师，在草台班子文化中打拼的经验和求职话术。

### 我的想法
从侧面反映了一些软件行业的现实，轻松有趣。 