---
tags:
  - 外企求职
  - 面试经验
  - 求职时间线
  - 面试记录
  - 程序员
  - 裁员
  - Leetcode
  - 简历优化
  - 求职攻略
  - 面试准备
---

# 03-外企求职一个月经验分享

## 🎯 背景介绍

> [!info] 求职背景
> **外企被裁程序员找工一个月(p2 timeline)**
> 
> 得知不续约的通知后，五一假期结束就开始陆续投简历，面了一些公司，很多挂在第一轮。有些是自己刷门槛试准备不充分注的，有些是匹配度不够或者跟我的bg有关，也有一些小插曲，pdd二面记错时间，记成了两个小时...

## 📋 求职经验总结

> [!tip] 四大核心建议
> 下面分享一下我的面试经验：

### 1️⃣ Leetcode准备

> [!example] Leetcode策略
> **国内的企业我只遇到了字节和pdd考了手撕，面试时间基本在一个小时，社招难度不会太大，重点关注hot 100和经典150的easy和medium，数组、链表、二叉树是重中之重，一些外企也会考察算法题，具体难度大于国内，目前我大通过Rakuten，现场share屏幕，打开leetcode，考察一道的没有刷过的题。**

### 2️⃣ 简历优化

> [!success] 简历建议
> **自己写完后最好让AI帮你修改，看重突出自己使用什么技术解决了什么问题，并且需要对简历上写的技术点非常熟悉，深挖工作中的技术点，自己要在想不到，可求助同事或者AI。**

### 3️⃣ 面试技巧

> [!important] 面试要点
> **第一，经历过的大部分面试，都会要求介绍个部分：①项目的上下游链路，②项目中最有挑战的点/难点，这两个部分都是可以提前准备的，尤其是项目中的难点，面试官会根据你说的难点，跟你讨论中的实现，需要提点准备。第二，项目中的一些关键指标怎么了解，以及实际工作中对某个问题的排查思路。另外，大部分偏理论没有学习新的技术，需要体现你对最新技术事件的关注，比如大模型，AI Agent，MCP等**

### 4️⃣ 人脉网络

> [!note] 人脉价值
> **几乎所有公司都会问，网上相关文章很多，理解性抱着，实在记不全也不要太push自己，了解一点比一点都不了解好，工作中常用的最好记住。**

## 📊 详细面试记录

> [!abstract] 面试时间线
> **过程虽然过程最好都录音，然后转文字，面试完review不熟悉的点，面试多了，基本上你简历上的东西都已经被问得差不多了，接下来的面试会更加游刃有余。**

| 日期 | 企业 | 流程 | 状态 |
|------|------|------|------|
| 5.7 | 字节-飞书项目 | 一面 | 挂 |
| 5.12 | 拼多多-Temu | 一面 | 通过 |
| 5.13 | 蚂蚁-平台工程 | 一面 | KPI |
| 5.14 | 字节-客服平台工程 | 一面 | 挂 |
| 5.16 | 拼多多-Temu | 二面 | 挂 |
| 5.19 | 乐刻运动 | 一面 | 通过 |
| 5.19 20:00 | 嘉银科技-Agent | 一面 | 通过 |
| 5.20 16:00 | 阿里 | 一面(电话面) | KPI |
| 5.21 14:00 | 嘉银科技-Agent | 二面 | 通过 |
| 5.21 19:30 | 蚂蚁-端云架构研发专家-支付宝技术 | 一面 | 挂 |
| 5.21 21:00 | 嘉银科技-Agent | 三面(HR面) | 通过 |
| 5.22 15:00 | 阿里云-领羊-Agent | 一面 | 挂 |
| 5.22 17:30 | 网易云音乐-AIGC | 一面 | 通过 |
| 5.26 14:00 | 乐刻运动 | 二面(主管+HR面) | ? |
| 5.27 10:30 | Rakuten | 一面(coding轮) | 通过 |
| 5.27 19:30 | 美团-营销平台 | 一面 | 挂 |
| 5.28 15:00 | 网易云音乐-AIGC | 二面(主管面) | ? |
| 5.30 14:00 | 字节-BI产品 | 一面 | ? |
| 6.4 14:00 | Mercury | 一面 | 取消(二三四面必须线下) |
| 6.5 16:00 | Rakuten | 二面(coding轮) | |

## 💡 关键洞察

> [!quote] 后续感悟
> **后面有时间我会慢慢整理分享这些公司的面试题。**

> [!tip] 求职建议总结
> - 算法准备重点关注经典题目，外企难度更高
> - 简历需要突出技术解决方案和成果
- 面试要准备项目难点和技术深度问题
- 保持对新技术的关注和学习
- 面试过程要录音复盘
- 多面试积累经验，后续会更加从容

## 🗨️ 社区互动

> [!info] 项目相关讨论
> **问：项目上下游链路是啥**
> 
> **答：假如你的项目有三个模块，模块A访问模块B，模块B访问模块C，你负责的是模块B，上游就是模块A，下游是模块C**

> [!question] 常见疑问
> **问：我之前被问到的是如果给你一个系统你怎么设计，或者你的系统是怎么设计的，模块上下游是问题，整个业务是怎么打通的？**
> 
> **答：是的，问的是跟其他系统怎么对接的吗** 