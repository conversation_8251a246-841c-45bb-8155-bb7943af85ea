# 面试准备完整攻略

## 前期准备

### 自我介绍

一分钟即可，我是按照学校、工作经历、重点项目以及产出、个人优点（owner意识、上进心）这个顺序去介绍自己。

### 简历

简历内容不需要很多，突出亮点即可，例如连续多次好绩效，核心项目owner。我的简历只有一页，一半的内容是教育经历、工作经历和专业技能，剩下的都是项目经历，每个项目按照工作内容+工作成果进行描述。

### 八股文

- **学习网站**：[codehot](https://codehot.cn/markdown/must_read)
- **对于校招**：由于没有项目经验或者实习经验难以支撑，只能重点考察八股，计网、操作系统和java基础等都是要准备的，而且考的很细。
- **对于社招来说**：主要考察redis、mysql以及消息队列，而且是结合项目去问的。因为我的项目涉及很多消息队列和mysql，问的最多的就是如何保证消息队列消息不丢失和重复消费问题，怎么分库分表的，遇到过数据倾斜嘛。

### 算法题

- **学习网站**：codetop和leetcode前100道热题
- **准备策略**：准备最近大厂高频的题和热题就够了，不要浪费时间去刷每日一题，有针对性地复习。目前社招面试过程中遇到的算法题都是在热题里出现的。

### 场景题

1. **设计题**：秒杀系统，商城系统，微信朋友圈，微博，短域名系统
2. **海量数据**：url黑名单、词频统计
3. **面试的部门在项目中实际遇到的难题**：比如我遇到了线程池实现父子线程可能带来的问题，应用无限扩容的瓶颈是什么

## 项目介绍

介绍项目时候，若面试官让完整介绍一个项目，可以按照下面的思路去介绍，一定要有条理性。其中，难点和如何解决是重中之重，每次都会问，回答这个问题的时候，最好要体现自己的思考，比如当时有两种技术方案，你出于什么考虑选择了现在的方案。

### 项目介绍思路

1. **介绍产品**：有助于面试官更好了解技术细节
2. **为什么要做这件事**
3. **在里面做了什么工作**
4. **有什么难点，是怎么解决的**
5. **结果产出是什么**

---

## 相关链接

- [[PRJ-求职项目-2025]] - 项目梳理和技术实现
- [[PRJ-求职八股-2025]] - 技术八股文准备
- [[PRJ-力扣刷题-2025]] - 算法题练习
- [这些年背过的面试题](https://mp.weixin.qq.com/mp/appmsgalbum?__biz=MzIzOTU0NTQ0MA==&action=getalbum&album_id=3335716281446285313&scene=126&uin=&key=&devicetype=iMac+Mac14%2C3+OSX+OSX+14.5+build(23F79)&version=13080a10&lang=zh_CN&nettype=WIFI&ascene=78&fontScale=100)-主包推荐的场景题八股，来源阿里内网

## 标签

#面试准备 #求职 #技术面试 #项目介绍 #八股文 #算法题 