---
tags:
  - Java
  - 后端开发
  - 面试准备
  - 学习路线
  - SpringBoot
  - 算法
  - 八股文
  - LeetCode
  - 求职
  - 技术栈
  - MySQL
  - Redis
---

# 02-Java后端速成路线

## 🎯 核心理念

> [!warning] 关于学习路线的常见问题
> 好多人老问学习路线，要不就是问"能不能学完"，说实话这些问题没啥意义，我感觉我给答案都是自我安慰，能学完的人从不会怀疑自己。

> [!info] 路线设计思路
> 我感觉我那篇突击方方法论里边写知识框架学的很清楚了。我哥们看完甚至说那一篇都够了😱。但是好多人问我还是再写下我的路线呢，不过我不保证能复刻，因为通过评论区问的很多问题，我感觉大部分读者的能力客观说可能不如我，而且自己搜索能力几乎为0，啥都要指望有人喂到嘴边。

## 📚 详细学习计划

> [!tip] 个人学习风格
> 总的来说为什么我学的快，跟我个人习惯有关，我本硕都不怎么上课，就是直接做作业，需要哪块学哪块。感觉直接做作业学的更快更好不需要听课。所以我很少看视频，我就觉得视频讲的很慢，老是说一堆废话，我喜欢直接看书/网页。

### 第1阶段：Java基础

> [!example] 1. Java基本语法面向对象（2天）
> **本科科班学过，但是说实话，本科我上那门课之前自己随便网上找了本很简单的java教材看了两天就把基础语法看完了，那学期我也没怎么去上过**
> 
> 之后就比较激进了，我根本没有学java se啥的，直接做springboot的项目，springboot我也没特意学，就是直接做项目。

### 第2阶段：实战项目

> [!success] 2. 以springboot为框架，带redis和mysql的项目
> **网上随便找一个，2周左右搞完，要是抄都懒得抄直接看一遍那更快了，一周。**
> 
> 说实话学CS最恶心的就是配环境，我自己也很过庆，但也没办法，想做项目就不得不配，比如maven啥的，这个就是动手做，我的习惯就是啥都不会硬从网上查查查，反正最后乱七八糟都能搞好😅

### 第3-7阶段：八股文准备

## 🔍 MySQL和Java重点详解

> [!info] 关于学习重点的说明
> 之前看评论区问题发现mysql和java学的重点不太对，和面试有点偏差，这篇写一下这两块重点是什么。

> [!warning] 突击学习的本质
> 首先突击的本质是你一天学了十个小时跟别人十天每天学一小时一样，而不是你一天学一小时顶别人十天每天学一小时，不要老想投机取巧。

> [!note] 3. MySQL八股文一周
> **主要来源：小林coding/极客时间mysql45讲/牛客网面经/哪些不会的知识点查网上博客**
> 
> ### MySQL面试重点详解
> 
> > [!example] MySQL学习建议
> > MySQL面试中非常爱考，我建议学扎实。在我实习面试时我就看的小林coding（免费的，非打广告...），然后不太理解的地方可以上网找技术文章补充，我觉得这个也就别走捷径了，可以差不多全看一遍，也不难（我当时看了两天基本全部看完）。
> 
> > [!important] MySQL核心考点
> > 然后比较常考的是**索引**（B+树结构，聚簇非聚簇，索引失效等）**事物隔离级别**以及怎么实现，**事物的ACID特性**，还有**几种日志**（undo log, redo log, binlog），就是**重点是这些底层原理，而不是sql语句语法**。

> [!note] 4. Redis八股文2天
> **小林coding/牛客网面经**

> [!note] 5. 计算机网络八股文一周
> **我科班的本来就会所以只回忆了一天，不会的话看王道考研或者自顶向下大黑书+面经感觉要一周**

> [!note] 6. 操作系统八股文一周
> **本来学过而且看面经感觉java后端考得很少懒得看了所以我自己找暑期实习阶段就看了看面经，我自己学的时候看的操作系统导论，这书硬看我当时看了一周左右**

> [!note] 7. Java语言八股文一周
> **javaguide+综合面经+哪不会上网查哪，重点jvm/juc**
> 
> ### Java面试重点详解
> 
> > [!important] Java核心考点
> > **Java重点是各种锁/juc/jvm/集合类，可以结合javaguide+面经+网上技术文章准备。**
> > 
> > 举例：**synchronized底层原理，锁升级，和reentrantlock的区别/hashmap的扩容机制/jvm内存区域/常见垃圾回收器等**。
> > 
> > **javaweb, SSM框架怎么用基本不考，spring可能会偶尔考，但考的不是很多。我自己根本没学javaweb也没影响我用springboot做项目。**

### 第8阶段：算法准备

> [!important] 8. LeetCode三周
> **重点算法：哈希，双指针，常见线性dp树上dp，背包dp，dp滚动数组空间优化，BFS，DFS和回溯，迪杰斯特拉求最短路（图论要会自己建图），二分，二叉树，链表和LRU，并查集，快速排序与快速选择，堆的应用，栈，单调栈，单调队列**
> 
> 上述算法hot100里基本都有，边刷边理解这些算法的本质，不会的立马看答案，不要自己傻想。

### 第9阶段：扩展学习（可选）

> [!abstract] 9. 如果还有时间
> **网上搜索CMU/UCB/斯坦福的课，重点关注数据库/分布式系统/计算机网络/操作系统等，基本上都是造轮子课。但我感觉这步比较耗时间，收益也不是那么大。**

## 💡 关键洞察

> [!quote] 个人心得
> 题外话，其实我自己感觉这种给人提高自信的爽文或者学习路线并不能改变一个人，谁和谁方法都不一样。因为我本硕常常极限突击，所以我开始前从来没问东问西，我就觉得我能学完。
> 
> 还有我发现更的真正的知识点如网络redis和力扣几个算法看的人并不多，说实话看一堆爽文并无意义，面试需要的具体知识点学到才有用。

> [!tip] 学习心态建议
> - 我这里给出我自己的能力下的可能最快速度
> - 你们可以自己衡量
- 不要问东问西，直接动手做
- 重点学习面试具体需要的知识点 