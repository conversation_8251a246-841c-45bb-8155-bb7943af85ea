# 聊聊简历系列（五） - 如何梳理项目

> 作者：尼布斯 Nimbus
> 原文链接：跳槽日记 day22: 聊聊简历系列（五）

本文是回答评论区朋友咨询的帖子：如何梳理自己的项目？

> [!abstract] 灵感来源：开发者大会的架构分享
> 不知道朋友们有没有去过线下的开发者大会或者看过大厂的系统架构博文。文章开头一般都会有场景介绍。在介绍里，会讲到系统的历史债务与痛点，再画一张历史业务架构图，随之讲解决方案，给出新的架构图，之后便是改造历程，一路艰辛，最终完成了本次架构升级，扛住了峰值 QPS 过千方的系统压力。

今天先讲这个一个小点，关于架构梳理。

---

## 四种主流的架构划分

主流的架构划分一般有 4 种：**业务架构**、**技术架构**、**应用架构**、**数据架构**。

### 1. 业务架构

> [!tip] 什么是业务架构？
> 所谓的业务架构，是系统蓝图，主要描述的是：
> - **做什么**（目的）
> - **为谁做**（客户）
> - **怎么做**（方法）

比如你在一家电商公司，那公司的业务目的就是方便他买卖东西，客户就是商家和买家，方法就是打通供应链，打通商家与客户信息桥梁。

> [!question] 思考点
> 我们写项目简历之前可以思考怎样用浅显易懂和简短的语言，让面试官理解你们系统的整体业务目的，为什么要做这个项目。

### 2. 技术架构

再讲技术架构，技术架构是我们最熟悉的架构，阐述了系统的基础设施和工具链：部署环境（上云、自建）、微服务架构（单体架构）、技术栈。

> [!info] 面试策略
> 在这一步，我们就可以**埋下钩子**，引导面试官提出你熟悉的技术栈问题。

### 3. 应用架构

第三个是应用架构，大概讲一下你的系统拆分或者你的模块拆分，比如订单、支付、库存、履约等子系统或模块。

> [!info] 面试策略
> 在这一步，开始进入项目场景的沟通，面试官大概率会问一些延伸出来的场景题，或者针对系统薄弱点，提出连环炮问题。

### 4. 数据架构

> [!tip] 作者观点：数据架构即数据链路
> 第四个架构，我发现比较少人讲，对于我个人来说，可能更倾向于聊“**数据链路**”这个方向。
>
> 数据链路依附于业务流程，比如订单的**正向链路**（下单、支付、履约）与**逆向链路**（退单、退款、退货履约），订单数据的流转、钱款的流转、货物信息的流转，抓住核心链路，我们就理解了系统的脉络，写起简历才能有的放矢。

---

> [!summary] 总结：道与术
> 我觉得要想写出项目亮点，得先捋清楚上面四个部分，这是“**道**”的部分。你可以试着把上面几个架构都在心里过一遍，文字描述+画出架构图，成功后，你就有了一个好的开始了。

---
Tags: #尼布斯 #简历优化 