# 跳槽日记day28: 聊聊简历系列（七）

> [!info]
> hello, 朋友们，下班了，今天聊一点自己的思考，关于“如何梳理简历项目亮点”这个问题引出的思考。
> 
> 许多朋友私信我：如何梳理自己简历项目的亮点？
> 
> 我在前面的帖子里回答了这个问题中“道”的部分，也就是4A架构（对的，其实这种架构分类有自己的名字）的思考模式。再继续聊“术”的部分前，我想再聊聊一些自己的思考，作为引子。

> [!note] XY问题
> 在日常工作中、生活中，我们会常遇到“XY问题”，简单来说就是：
> 当某人想解决问题X，却误以为要先解决Y，结果追问Y时离X越来越远——这就是XY问题。
> 相当于病人说“医生我要买创可贴”（Y），而真实需求是“我动脉在喷血”（X）。
> 
> 其实这种现象在使用AI时也大量存在，有一些人觉得AI很智障，有时候答非所问。有些时候可能其实是提问者本身犯了类似的错误（我没有否认AI幻觉的客观存在～），在提问时简化或者误给了一些前文信息，没有抓到问题的本质，导致AI理解错了输入，自然给不了提问者想要的答案。

> [!question]
> 回到问题本身：“如何梳理简历项目亮点”。
> 
> 这个问题其实可以拆成两个问题：
> ·如何梳理项目
> ·什么是项目亮点
> 
> 为什么要这样拆分？我在思考问题的时候，喜欢拆分，因为大脑的栈很小，如果不拆分，很容易就把自己搅糊涂。就像有些朋友刚刷递归算法题的时候，不懂递归方法的定义，死命在脑海里模拟计算机递归栈，结果越想越糊涂。
> 
> 当然，有许多朋友很聪明，习惯性整体性思考，我觉得思考方式没有对错，适合自己的就是最好的。

> [!abstract]
> 再回过头来（我思维老是跑火车），我们其实大致回答了第一个问题：如何梳理项目。
> 
> 第二个问题：那什么才算是项目亮点呢？
> 我觉得这个问题也是内核。
> 想明白这个问题，我们才能真正梳理出（总结出）项目的亮点，写在简历上，并在最终面试时自信地说出来。

---

### 评论区

> **昼夜本色:**
> 老师好，有个问题，作为校招生，自己做的项目可能很难有开源项目那么高的性能，那如果面试官问你这个项目跟同领域的开源项目相比有什么优点怎么办呢？感觉平常只是学了项目中的技术点，却没办法创新优化😩
>> **尼布斯Nimbus (作者):**
>> 朋友你好，我觉得这个问题很常见呢。如果是我的话，我可能会从下面几个方面来选择性回答：
>> 1. 同领域的开源项目，相对比较重，提供了非常多的兼容性功能，但是我们目前的业务只需要其中某几个功能，所以我们选择了自研
>> 2. 开源版本多变，版本变更以及兼容性上不太好控制
>> 3. 开源项目在某些场景下不太能满足我们的需求，二次定开的成本较高，反而比自研更高。

#尼布斯 #简历优化
