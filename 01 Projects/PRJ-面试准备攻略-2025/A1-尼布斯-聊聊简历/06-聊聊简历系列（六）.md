# 跳槽日记day23: 聊聊简历系列（六）

> [!info]
> 朋友们，今天聊聊程序员如何形成自己（写简历）的方法论，欢迎关注~
> 
> 最近给私信我的读者朋友们看简历，其中有一位朋友的简历让我印象深刻。上一篇文章也是来源于我在看过他简历后的思考，欢迎私聊~

> [!note]
> 这位朋友名校本硕，在大厂内部转过组。
> 
> 令我印象深刻的点，不是他的履历，而是他的简历项目内容。
> 你在他的简历上甚少看到技术性的描述。在简历中零零散散地散落着几个后端开发相关的名词“Java、微服务、任务流转、Agent”，除此之外，没了，余下的内容全是业务描述。
> 
> 和我交流的时候，他很坦诚，说之前也找人帮忙看过简历，别人说他的简历看起来像是产品简历，不像技术简历。
> 
> 其实我在看完他的简历之后，也有些类似的疑惑。
> 因为他的简历逻辑不太连贯，而且有许多泛化的名词、互联网黑话，“能力、奠定基础、沉淀、赋能……”，而且没写技术栈！
> 
> 通读一遍，比较难抓住重点。所以又逐字逐句地分析了一遍，才大致理清了他负责的内容。

> [!question]
> 我觉得可能是他还没有找到写简历的方法论。
> 
> 方法论是可以培养的，比如学习头部公司同领域“最佳实践”。
> 自己的项目或许简单，但中、大厂在相同领域肯定有系统化的解决方案。研究他们的公开经验（技术博客），能学习到更优秀的架构。
> 
> 他山之石，可以攻玉。

> [!abstract] 具体步骤
> 具体来说，我觉得可以按照以下步骤：
> 
> 1.  **搜**：聚焦微信、阿里云、淘宝、得物、滴滴、字节等中、大厂的技术公众号。
> 2.  **读/学**：关注其“表达框架”：如何结合业务场景、技术架构（名词）、数据量级、痛点解法？每日读几篇。
> 3.  **问/思**：遇到可借鉴点（尤其不懂的），咨询同事（组内/上下游）。“三人行必有我师”，不同视角常带来启发。
> 4.  **化用**：将学到的体系化思维和表达内化，融入简历。本质是对项目进行“技术性解构与重构”。

> [!example] 举个例子
> 举个🌰一个内容审核系统，核心业务就是读取数据后调用第三方的审核API，看看是否违规，不违规直接设置用户可见，你如果写到简历上，会发现没有什么可以写的，流程只有3步，读数据，调第三方接口，回写数据。
>
> 看过大厂实践后，可能就会豁然开朗，核心流程变成了“海量数据预处理（脱敏/压缩）”、“多模态（图、文、音视频）审核引擎（准确率与响应速度）”、“异常复审机制”、“审核结果对接内部二方系统进行通知联动”。
> 
> 站在巨人的肩膀上，架构完整性和技术深度就出来了。

---

### 评论区

> **如琉璃般飘落:**
> 最近在理简历上的项目，感觉梳理一个项目要好久啊。主要是我觉得其中面试官会穿插问很多问题，考虑的点很多，所以总感觉项目整理的不够完善，是我过度分析了，还是说这是正常的过程？（主要是一周了都还没整理完一个项目🙈）
>> **尼布斯Nimbus (作者):**
>> 如果平时注意积累其实还好(๑•̀ㅂ•́)✧如果平常积累比较少的话，我觉得需要花这么多时间是正常的～赶时间就边面边准备
>
>> **尼布斯Nimbus (作者):**
>> 理论上面试官也不会问你好多个项目的，最核心的两到三个项目捋清楚就打败面试官了[棒R]

> **如琉璃般飘落:**
> 回复 尼布斯Nimbus：好的好的，还有啊，就是我在准备面试的时候就很容易就一个问题点延伸出去，因为我对面试官会考察到什么程度没有概念，所以在看一个技术点的时候就会连带把这个技术点中其他我也不了解的一起查资料学习，就导致一个知识点会学习很久（可能是我比较菜吧😭），我是应该以大体进度为主，只了解到面试题问到的内容，还是需要对面试时的知识点扣细节了解更多呢？
>> **尼布斯Nimbus (作者):**
>> 回复 如琉璃般飘落：平常自己学习就深度优先，面试准备的话，也选深度优先，但递归归栈不用太深，我觉得得四五层够了，以mq为例：demo（官网或者类demo示例）、原理（比如mq的架构、事务消息是如何实现的）、常见线上问题（消息积压、顺序性、消息丢失）、源码（时间轮源码、延时消息源码）、造轮子（写一个单体的mini mq），…，我觉得第一轮先到第三层就行，源码就只看最核心的（写在简历上的），造轮子看你兴趣爱好，没多大必要，但是是亮点，打败面试官(๑•̀૭•́)و

#尼布斯 #简历优化
