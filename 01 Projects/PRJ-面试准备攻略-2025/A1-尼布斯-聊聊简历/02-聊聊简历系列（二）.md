# 聊聊简历系列（二）

> 作者：尼布斯 Nimbus
> 原文链接：跳槽日记 day18: 聊聊简历系列（二）

简历润色与作假的分界线在哪里 (part 2)

---

书接上回，我们继续聊简历。

## 简历中的危险信号

1.  **先答复评论区**
    > [!tip] 回顾：关于“全是技术向，没有业务支撑”
    > 上篇文章评论区有朋友问：全是业务的话面试官不会不感兴趣吗？
    >
    > **作者解答**：
    > 尽量结合业务说技术点优化，比如，写了“基于 redis，优化查询”，可以基于你的业务系统，改为“针对某某系统（电商、物流、供应链、营销之类，你真实负责的业务系统）的某个问题，设计了某某多级缓存架构，然后列具体的技术点实现，最后实现了什么优化结果（核心链路接口耗时降低到多少毫秒以内）”，这样你就可以更好地准备面试的点，因为你给了面试官绳索，“按图索骥”减少面试官的思考，你也轻松一些，要准备的八股内容就更有针对性一些。

2.  **谦虚一些，不要全是“精通”**
    - 简历上满眼的“精通 JVM”、“精通 Dubbo”、“精通 Netty”，可能会让人觉得是误写（作假）。
    - 大多数人精力有限，“精通”都是误写（作假）。

    > [!example] 如何描述熟练度？（以 Dubbo 为例）
    > **不要写**: 精通 Dubbo。
    >
    > **可以这样写**（根据自己实际情况修改）：
    > “熟悉 Dubbo 框架，有单机系统拆分并升级微服务架构系统经验，有框架二次开发经验，阅读过 Dubbo 框架核心链路源码，包括 RPC 请求的分层流转链路、集群容错和路由策略等……”，细化你的熟悉内容，给面试官真实感，而不是写“精通”。

3.  **入职过的公司技术栈完全一致**
    - 比如面试者入职过五家公司，mq 全是 rocketmq，或者全是 kafka，RPC 全是 Dubbo，db 全是 MySQL + mongodb，可能会存疑。
    - 因为技术选型是有区分度的，理论上极少概率是一模一样的技术栈。
    - 这会让我怀疑面试者是更熟悉这一套，所以简历做了某些“包装”。当然，具体情况，我会在面试的时候再细问，如果他能自圆其说，我也不会打上负面面评。

4.  **学历作假**
    - 比如名校独立学院，写本部。
    - 我个人倾向于真诚大于等于技术能力和学历。比如，如果候选人 a 同学学历相比 b 同学略微劣势，但面试中前者相对踏实，简历“优化”点少一些，我大概率会考虑前者。

---
Tags: #尼布斯 #简历优化 