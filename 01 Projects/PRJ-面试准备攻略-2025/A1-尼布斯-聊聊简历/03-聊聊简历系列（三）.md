# 聊聊简历系列（三） - 简历格式

> 作者：尼布斯 Nimbus
> 原文链接：跳槽日记 day19-20: 聊聊简历系列（三）

虽然有点老生常谈，我相信朋友们基本都知道，但我为了整个简历系列的完整性，还是写了这个帖子。以下是我想要分享的一些关于简历格式的要点：

---

## 简历格式要点

1.  **简历名称与格式**
    - **命名规范**：一定耍包括姓名、求职意向（岗位）、“简历”。
    - **分隔符**：信息之间用下划线分隔，不要有空格。
    - **文件格式**：强制使用 PDF。
    - > [!example] 示例
    >  `神里绫华_Java后端开发_简历.pdf`

2.  **抬头必要信息**
    - **必须**：姓名、电话、邮箱。
    - **加分项**：如果有优质博客网站和 GitHub，请贴上。

3.  **简历颜色**
    - 我个人推荐**白底黑字**。

4.  **技术名词大小写**
    - 注意检查规范。如果不确认，请把要写到简历上的每个专业术语都查一遍。

5.  **内容模块**
    - **大致包括**：个人信息、教育经历、技术栈、开源项目、工作经历、项目经历。
    - > [!tip] 个人建议
    > 我个人喜欢把项目经历写到工作经历里面去，省得面试官还要匹配公司与项目。
    - **补充信息**：可以根据求职公司和岗位进行调整（比如你求职的是国际电商，你可以把外语等级写上）。

6.  **一页纸还是多页纸？**
    - > [!question] 一页还是多页？
    >  我喜欢一页，但对于一些项目经验特别丰富的朋友来说，两页我觉得问题也不大，尽量别超过两页。

7.  **放不放头像？**
    - **不放**。

8.  **教育经历与工作经历**
    - **顺序**：倒叙。
    - **格式**：时间格式需要统一、对齐。

9.  **其他细节**
    - **标点符号**：分支符号不要太突兀，比如太大。
    - **中文基础语法**：比如“的”、“得”、“地”之类，保险一点就写完问问 D 老师。
    - **校招**：可以加上在校的荣誉和优秀的绩点。

---

> [!summary] 总结
> 我觉得简历的核心还是在于**内容**，如何在一页纸上表现出你的优势，朴实的、技术扎实的技术者，不太需要华丽的格式。
>
> 写完简历，不如反转一下角色，如果我是面试官，我该如何看待这份简历？

---
Tags: #尼布斯 #简历优化 