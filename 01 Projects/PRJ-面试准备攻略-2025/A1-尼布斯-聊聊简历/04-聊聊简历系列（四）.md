# 聊聊简历系列（四） - 垂直度

> 作者：尼布斯 Nimbus
> 原文链接：跳槽日记 day21: 聊聊简历系列（四）

在正文开始之前我想先讲个职场很火的词——“垂直度”，其实就是“专业深耕”。

---

## 什么是简历的“垂直度”？

> [!abstract] 什么是垂直度？
> 曾经有个很火的梗，大意是卖桃子的小贩找不到卖梨子的工作，后来逐渐发展成“卖长毛桃子的小贩找不到卖短毛桃子的工作”。
>
> 作为求职者，其实还蛮弱势的，但我们不能忽视依赖“垂直度”的筛选机制。

### 1. 技术垂直度：增加技术细节与深度

我发现大多数人技术栈部分的介绍几乎都一样：
- 熟练 Java 语言，熟悉 JVM，有线上调优经验；
- 熟练使用 mysql 关系型数据库；
- 熟练使用 redis、hbase 等；
- 熟练使用 kafka、rabbitmq 等消息中间件；
- 熟练使用 Dubbo 微服务框架；
- 熟练使用 Netty 等网络框架……

> [!example] 如何修改才能体现技术垂直度？
> **修改前：**
> - 熟练 Java 语言，熟悉 JVM，有线上调优经验；
> - 熟练使用 MySQL，关系型数据库；
>
> **修改后：**
> - **熟悉 Java 语言**，阅读过 jdk17 核心源码，包括常用集合包、常用并发包、线程池、并发工具类等核心类，有并发编程实战经验。
> - **熟练使用 MySQL**，深入研究过 InnoDB 存储引擎原理，熟悉锁机制、索引原理，有线上系统 SQL 优化经验，有分库分表经验。
>
> **核心**：增加了技术栈的细节与深度，不是泛泛而谈。

### 2. 业务垂直度：让技术为业务服务

> [!tip] 核心观点
> 但我其实还没说到这篇帖子想说的核心点。我们回到“垂直度”上，写简历，要求尽可能垂直，前面说的是大家都会考虑的“**技术垂直**”，还有一个点叫“**业务垂直**”。

没有业务支撑的技术都是空中楼阁。**业务就是技术，甚至在某种意义上比“技术”更“技术”**。你见过 40 岁的程序员吗？我见过，甚至很多。他们的大多数特点就是在某一业务领域深耕，扯远了，回头继续讲。

> [!example] 如何增加“业务垂直度”？
> 我写一句话你就懂了。
>
> **3 年供应链系统开发经验，深入供应链-物流一线系统开发，有百万行复杂业务系统重构经验，对长链路系统的数据一致性问题有深入理解和生产实践经验。**
>
> 朋友们，你们可以试试，在你投递业务相关的岗位时，在技术栈开头加上类似的一句话，你的简历投递成功率会增加巨多~

---
Tags: #尼布斯 #简历优化 