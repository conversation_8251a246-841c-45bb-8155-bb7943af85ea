# 聊聊简历系列（一）

> 作者：尼布斯 Nimbus
> 原文链接：跳槽日记 day17: 聊聊简历系列（一）

从技术面试官角度聊聊简历：什么样的后端开发简历大概率会被忽略。

## 什么样的简历会让人感觉作假？

> [!question] 核心问题
> 我的 Mac 里存了一千多份简历，是某些 SS 网站上收的，也有部分是 hr 或者 +1 leader 发我的。简历看多了，我会略过许多找不到亮点的简历，也会略过一些比较夸张（作假）的简历（就是我个人主观感觉似乎水分太大了）。那什么简历会让我有这种作假嫌疑的感觉呢？

1.  **技术栈太新了**
    - 全系列最新的技术栈，但在网上能找到的资料只有一些大厂在尝鲜的博文，会有存疑。
    - 如果进入到面试环节，会尤其注意面试者相应技术栈的使用时间节点，考察他为什么采用这套全新的技术栈，风险性如何控制的，如何从旧版本平滑迁移到新版本上。如果他对很多东西很模糊，那可能就会在面试评语上记下我的怀疑点。

2.  **技术栈与招聘 JD 不符**
    - 比如简历上写的技术栈全是 rocketmq，但候选人所在公司的招聘上全是 kafka，存疑项加一。我们懂一些技术的 HR 会看这个。

3.  **全是技术向，没有业务支持**
    - 比如写“基于 redis 缓存，优化查询性能”、“基于消息队列，解耦系统”、“优化 MySQL 查询，性能提升 1000%”。这些问题在校招生的简历上出现得比较多，没有业务支持的技术点，列起来像空中楼阁。很容易就能问出来。
    > [!tip] 如何结合业务说技术优化？
    > **作者解答**：尽量结合业务说技术点优化。
    >
    > 比如，写了“基于 redis，优化查询”，可以基于你的业务系统，改为“针对某某系统（电商、物流、供应链、营销之类，你真实负责的业务系统）的某个问题，设计了某某多级缓存架构，然后列具体的技术点实现，最后实现了什么优化结果（核心链路接口耗时降低到多少毫秒以内）”。
    >
    > - **好处**：这样你就可以更好地准备面试的点，因为你给了面试官绳索，“按图索骥”减少面试官的思考，你也轻松一些，要准备的八股内容就更有针对性一些。
    > - **核心**：大抵上还是按照 STAR 法则。

4.  **项目业务与所在公司不匹配**
    - 比如一个主营业务与电商业务毫无关系的公司，面试者简历上却写了就职期间负责了一个千万级 dau 的电商系统。

5.  **不了解所负责业务的细节**
    - 经常看到有人写负责公司数据的分库分表，但有些候选人对基础的表结构说不清楚。

6.  **有 jvm 优化调优经验，但细节模糊**
    - 写了有系统优化经验，但是细节记得比较模糊，事前事中事后，不能串起来。

7.  **写出路过复杂业务系统，但无深刻问题处理经验**
    - 没有遇到过什么记忆深刻的线上问题，没有处理过死锁问题，没有经历过慢 SQL，没有经历过 cpu 打满，没有经历过线上调用二方、三方接口抖动问题……
    - 以上问题单一拿出来没经历过，没什么问题，但要全都说没经历过，会有些存疑，面试者的复杂业务系统经验是否真实，有多少是面试者自己负责的。

---
Tags: #尼布斯 #简历优化 