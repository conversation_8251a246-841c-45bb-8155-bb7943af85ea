# 跳槽日记day29-30：聊聊简历系列（八）

## 什么是项目亮点（上）？

hello，朋友们～一周过半，今天接着前文继续聊：

> [!question] 核心问题
> 什么是项目亮点（上）？

## 项目亮点的定义

> [!note] 简单来说
> 所谓的"项目亮点"是指能一下抓住面试官眼球的内容。

## 简历的最终目标

> [!success] 写简历的最终目的
> 写简历的最终目的就是拿到offer。那为什么要给我发offer？因为招我进去能麻溜地干活，也就是说面试官最感兴趣的就是我解决问题（干活）的能力。

## 如何体现解决问题的能力

> [!important] 核心思路
> 所以我的简历上要做的就是尽情展现我解决问题的能力，也就是我们寻找的项目的亮点。

有了方向，我们就可以把做的内容往这个方向上套。

## 实际案例分析

### 案例一：微服务架构系统

> [!example] 普通描述 vs 亮点描述
> 
> **普通描述：**
> 比如我的简历上写系统是微服务架构的系统。这体现了我解决问题的能力了吗？不太能。
> 
> **亮点描述：**
> 但如果我说因为服务代码量，太过庞大，业务耦合严重，所以我参与做了服务拆分，这就能体现我解决问题的能力，写上去，这就是亮点。

### 案例二：分布式锁

> [!example] 技术应用的正确表达
> 
> **错误表达：**
> 再比如我说用了分布式锁，这体现我解决问题的能力了吗？不能、（尴尬）/
> 
> **正确表达：**
> 但如果我说为了解决系统某个业务的并发争用与数据一致性问题，用了分布式锁，这就体现了解决问题的能力，写上去，这也是亮点。

## 面试官的考验

> [!warning] 技术深度考验
> 当然，亮点有大有小，有些太过常见的亮点，写的人太多了，面试官也便觉得普通了。这时候就需要考验你技术的深度了：
> 
> "看你用了这个，你读过它的源码吗？聊一下他的底层实现吧"。

---

#尼布斯 #简历优化
