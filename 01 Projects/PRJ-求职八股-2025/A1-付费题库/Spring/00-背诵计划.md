### 一、Spring 面试题分类（按重要性 + 考察频率）

#### 【核心高频】（基础必考，20 题）

1. 介绍一下 Spring 的 IOC
2. 介绍一下 Spring 的 AOP
3. 为什么 Spring 不建议使用基于字段的依赖注入？
4. Spring Bean 的初始化过程是怎么样的？
5. Spring 的事务传播机制有哪些？
6. @Autowired 和 @Resource 的关系？
7. BeanFactory 和 FactoryBean 的关系？
8. Spring 中如何开启事务？
9. 什么是 Spring 的循环依赖问题？
10. Spring 事务失效可能是哪些原因？
11. SpringMVC 是如何将不同的 Request 路由到不同 Controller 中的？
12. SpringBoot 是如何实现自动配置的？
13. Spring Bean 的生命周期是怎么样的？
14. Spring 中注入 Bean 有几种方式？
15. Spring 中的 Bean 作用域有哪些？
16. @PostConstruct、init-method 和 afterPropertiesSet 执行顺序
17. Spring 默认支持循环依赖吗？如果发生如何解决？
18. Spring 中用到了哪些设计模式？
19. SpringBoot 和 Spring 的区别是什么？
20. 介绍下 @Scheduled 的实现原理以及用法

#### 【次核心】（原理 / 场景重要，20 题）

21. Spring 6.0 和 SpringBoot 3.0 有什么新特性？
22. Spring 在业务中常见的使用方式
23. 如何统计一个 Bean 中的方法调用次数？
24. Spring 中 shutdownhook 作用是什么？
25. Spring 的 AOP 在什么场景下会失效？
26. 在 Spring 中如何使用 Spring Event 做事件驱动？
27. 为什么不建议直接使用 Spring 的 @Async？
28. 三级缓存是如何解决循环依赖的问题的？
29. SpringBoot 如何做优雅停机？
30. Spring 中的事务事件如何使用？
31. Spring 中 @Service、@Component、@Repository 等注解区别是什么？
32. SpringBoot 的启动流程是怎么样的？
33. 如何在 Spring 启动过程中做缓存预热？
34. @Lazy 注解能解决循环依赖吗？
35. Spring 中的 Bean 是线程安全的吗？
36. Spring 中如何实现多环境配置？
37. 如何自定义一个 starter？
38. 为什么 SpringBoot 3 中移除了 spring.factories？
39. Spring 的事务在多线程下生效吗？为什么？
40. 如何根据配置动态生成 Spring 的 Bean？

#### 【拓展】（深度 / 场景化，12 题）

41. Spring 的 @Autowired 能用在 Map 上吗？
42. Spring 创建 Bean 有几种方式？
43. 同时使用 @Transactional 与 @Async 时，事务会不会生效？
44. 知道 Spring Task 吗，和 XXL-JOB 有啥区别？
45. SpringBoot 和传统的双亲委派有什么不一样吗？
46. 有什么情况会导致一个 bean 无法被初始化么？
47. SpringMVC 中如何实现流式输出？
48. Spring 6.0 的新特性细节（如 AOT、Jakarta EE 迁移）
49. 统计 Bean 方法调用次数的具体实现（AOP 示例）
50. AOP 失效的详细场景（如内部调用、final 方法）
51. @Async 的正确使用方式（线程池配置 + 异常处理）
52. Spring 三级缓存的细节（三个缓存的作用时机）

### 二、艾宾浩斯遗忘曲线学习计划（5 天核心周期 + 长期复习）

#### **Day 1（新内容学习）**

- **上午**：核心高频 20 题（逐个梳理答案逻辑，背诵核心要点）。
- **下午**：次核心 20 题（理解原理，整理 “场景 + 结论” 框架）。
- **晚上**：拓展 12 题（聚焦与核心关联的内容，如 “三级缓存细节”“Bean 无法初始化”）。

#### **Day 2（首次复习，间隔 1 天）**

- **核心高频**：随机抽问，口述答案（重点检查 “事务传播机制”“Bean 生命周期”）。
- **次核心**：快速回顾，标记难点（如 “AOP 失效场景”“@Async 问题”）。
- **拓展**：浏览要点，强化 “SpringMVC 流式输出”“多线程事务失效”。

#### **Day 4（间隔复习，第 1+3 天）**

- **核心高频**：默写关键概念（如 “IOC vs DI”“@Autowired 和 @Resource 区别”）。
- **次核心**：攻克标记难点（如 “三级缓存解决循环依赖的步骤”“自定义 starter 流程”）。
- **拓展**：挑选 5 题深度复习（如 “Bean 无法初始化的排查”“Spring Task vs XXL-JOB”）。

#### **Day 7（周期复习，第 1+6 天）**

- **核心高频**：模拟面试，串联知识（如 “IOC→Bean 生命周期→循环依赖→三级缓存”）。
- **次核心**：关联知识点（如 “AOP 失效→@Async 问题→多线程事务失效”）。
- **拓展**：回顾跨分类关联（如 “三级缓存→循环依赖→Bean 初始化失败”）。

#### **Day 15（长期复习，第 1+14 天）**

- **核心高频**：全面复盘，确保细节精准（如 “BeanFactory 和 FactoryBean 的本质区别”）。
- **次核心 + 拓展**：聚焦高频关联（如 “自动配置→自定义 starter→Boot3 变化”“事务失效→多线程→@Async 冲突”）。

### 三、学习技巧

1. **答案提炼**：每个问题整理 “3 个核心要点 + 逻辑链”（如事务失效：代理问题、异常捕获、传播机制、多线程）。
2. **关联记忆**：将同类问题串联（如 “IOC→依赖注入→字段注入问题→构造器注入优势”）。
3. **输出检验**：用自己的话复述答案，或写思维导图，检验逻辑连贯性。