# 如何让面试官觉得你的八股“深不见底”

> [!quote] 面试提问的本质，是根据你简历上写的点来提问的。
> 你简历上写了 MVCC、事务，面试官就有很大概率去问。如果对某些点很了解，可以直接在简历上**加粗**，面试官就知道你很熟，会重点深入考察。这样，面试就变成了开卷考试。

---

## 核心策略：广度优先，再深度优先

> [!info] 核心学习策略
> 1.  **广度优先 (Breadth-First)**：刚开始学习时，对各类基础八股（MySQL, Redis, MQ, JUC, JVM 等）都要有所涉猎。因为面试官可能从任何一个点开始提问。
> 2.  **深度优先 (Depth-First)**：在有了广度的基础上，寻找某一个你熟悉且常考的点，把它当作你的 **“杀手锏”**，深入学习，越深越好。

### 如何运用“杀手锏”？

> [!tip] 引导面试，主动出击
> 你的目标是：无论面试官从哪个点开始问，你都能通过引导，将话题带到你准备得最深入、最牛逼的那个“杀手锏”上。一旦进入你的主场，就是你肆意发挥的时间，用最短的时间给面试官留下“深不见底”的印象。

---

## 真实案例：用“分布式锁”征服面试官

> [!example] 一次真实的中厂面试经历
> -   **背景**：当时作者的八股能力一般，但只有一个点学得非常深入：**并发和锁，尤其是分布式锁**。
> -   **过程**：在面试中，他成功将面试官引导到分布式锁的话题上，然后开始了他长达 **20分钟** 的“吟唱”，内容包括：
    -   `SETNX` 与看门狗机制
    -   可重入的多种实现方式 (`HSETNX`)
    -   主从架构的锁丢失问题
    -   RedLock (连锁) 机制及其缺陷
> -   **结果**：面试官听得连连点头。在反问环节，面试官给出了极高的评价：“你按照自己的路往下走就可以了，建议以后可以朝架构师方向发展。”
> -   **启示**：用一个极深的知识点，让面试官误以为你对所有东西都了解得这么深入。

---

## 八股文划重点：知识网络的边界

> [!note] 不同规模公司的八股文考察重点

### 中小厂 Java 八股文重点

-   **Java 基础**: `HashMap` 原理, `ArrayList` 原理
-   **Spring**: 特性 (IOC, AOP), 自动装配, Bean 生命周期
-   **多线程**: 基础概念
-   **MySQL**: 事务, 索引
-   **Redis**: 基本数据结构, 缓存持久化 (RDB/AOF), 淘汰机制
-   **JUC**: 锁, 线程池, `ThreadLocal`, `ConcurrentHashMap`
-   **MQ**: 基本概念, 消息可靠性, 死信队列, 延迟队列
-   **设计模式**: 单例, 工厂
-   **数据结构**: 栈, 队列, 哈希表

> [!warning] 注意：Spring 和设计模式通常不是中小厂面试的绝对重点，而是加分项。重点在于 **MySQL, Redis, MQ**。有余力再深入 JUC 和 JVM。

### 中大厂 Java 八股文重点

> 包含中小厂的全部重点，并在此基础上深化和扩展。

-   **MySQL**: 索引, 事务, **MVCC**, 日志 (redo/undo/binlog), 存储引擎, SQL 优化
-   **Redis**:
    -   **底层原理**: 各种数据结构的底层实现
    -   **核心机制**: 持久化, 淘汰策略
    -   **分布式应用**: 分布式锁, 分布式会话
    -   **高可用**: 主从复制, 集群, 哨兵
    -   **分布式理论**: CAP, BASE, Raft, 一致性哈希
-   **MQ**: 消息可靠性, 死信/延迟队列, **消息堆积问题, 幂等性, 顺序性**
-   **JUC**: **JMM (Java内存模型)**, 锁 (各种锁), 线程池, `ThreadLocal`, **CAS, AQS, `volatile`**, 并发工具类
-   **JVM**: 内存结构, **垃圾回收 (各种回收器)**, **类加载机制**, **性能调优与问题排查 (Arthas, MAT)**
-   **数据结构**: 栈, 队列, 哈希表, **树, 堆, 深搜 (DFS), 广搜 (BFS)**
-   **操作系统**: 用户态/内核态, 进程/线程, 死锁, 内存分配, 虚拟内存
-   **计算机网络**: TCP/IP 协议栈, 网络分层模型, ARP, IP, TCP, UDP, DNS, HTTP

> [!warning] 注意：微服务相关（服务注册、网关、限流等）了解概念即可，通常不是八股考察的重点。中大厂的核心重点在于 **MySQL, Redis, MQ, JVM, JUC**。

---

## 最终技巧

> [!done] 简历加粗，开卷考试！
> 再次强调，面试的起点是你的简历。把你最有信心的“杀手锏”技术点 **加粗**，主动引导面试官来问你准备最充分的部分。 

#猪神