# 为什么你背八股像受刑，而别人是享受？

> [!quote] 你是不是背八股时...
> -   纯死记硬背，咋背都背不过？
> -   知其然，不知其所以然？
> -   一和面试官聊就磕磕巴巴，讲不到重点？
>
> **因为你从未站在设计者的角度去思考：他们为什么要这么设计？**

---

## 换个角度看八股：以 MQ 消息可靠性为例

> [!example] 面试题：如何保证 MQ 消息的可靠性？

### 1. 发现问题：消息在哪些环节会丢失？

保证消息可靠性，就是保证它不丢失。那么，我们首先要分析它在哪些环节可能会丢失：

1.  **生产者 -> MQ**：生产者发送消息到 MQ 的过程中。
2.  **MQ 自身**：MQ 内部存储时，如宕机、重启导致内存消息丢失。
3.  **MQ -> 消费者**：MQ 将消息投递给消费者，但消费者还未处理就宕机了。

### 2. 解决问题：针对性地设计方案

针对以上三个环节，我们可以找到对应的解决方案：

1.  **生产者侧**：开启 **生产者的 ACK (确认) 机制**。MQ 成功收到消息后，回复一个 ACK 给生产者。
2.  **MQ 自身**：
    -   开启 **持久化机制**，将消息存入磁盘。
    -   搭建 **MQ 集群**，保证高可用。
3.  **消费者侧**：开启 **消费者的 ACK 机制**。消费者成功处理完消息后，再给 MQ 发送 ACK，此时 MQ 才真正删除消息。如果消费者异常，MQ 会重发消息。

---

## 核心心法：抽象与推广

> [!info] 编程的本质就是抽象。
> 将一个具体问题的解决方案，抽象成一个通用的设计模式，并推广到其他场景中。

### 场景一：数据持久化

-   **具体问题**：如何保证 MQ 内存中的消息不丢失？
-   **解决方案**：持久化到磁盘。
-   **抽象场景**：**如何让内存中的数据持久化，防止丢失？**
-   **推广应用**：
    -   **Redis**：如何保证 Redis 数据不丢失？ -> `RDB` / `AOF` 持久化机制，主从集群。
    -   **MySQL**：数据写入内存 `Buffer Pool` 后，如果没刷到磁盘就宕机了怎么办？ -> `redo log`。

> [!tip] 站在设计者角度思考：为什么 MySQL 用 `redo log`？
> -   直接刷 `Buffer Pool` 到磁盘是 **随机 I/O** (磁头需要到处寻道)，非常慢。
> -   写 `redo log` 是 **顺序 I/O** (在文件末尾追加日志)，非常快。
> 通过 `redo log` 这种设计，MySQL 实现了高性能的写入和崩溃恢复能力。现在再问你“为什么随机I/O比顺序I/O慢”，你是不是就能理解了？

### 场景二：ACK 确认机制

-   **具体问题**：如何保证消费者成功处理了 MQ 的消息？
-   **解决方案**：消费者的 ACK 机制。
-   **抽象场景**：**当 A 服务给 B 服务发送数据，如何确保 B 处理完后，A 再进行下一步操作（如删除数据）？**
-   **推广应用**：
    -   **TCP**：TCP 的可靠传输，不就是靠 ACK 机制吗？（MQ 的 ACK 很可能就是学习了 TCP 的设计）
    -   **业务开发**：当我们自己的业务遇到类似场景，是不是也可以引入 ACK + 超时重传的机制来解决？

---

## 八股文的终极意义

> [!done] 八股最开始就不是用来“背”的，而是用来“学”的。

-   **场景题怎么答？** 你真正理解的每一个八股，都是你场景题的解决方案。
-   **背八股的意义是什么？**
    -   我们业务中常用的 Redis 缓存策略是 **旁路缓存 (Cache-Aside)**，适用于读多写少。
    -   而 MySQL 的 `Buffer Pool` 是一种 **写回缓存 (Write-Back)**，适用于写多读少。
    -   在我们的业务中，如果遇到类似在线文档编辑这种“写多读少”的场景，是不是就可以借鉴 `Write-Back` 的设计思想呢？

**学习这些计算机前辈的优秀设计思想，并应用到我们未来的开发和业务中，这才是“八股”真正的意义所在。** 

#猪神