---
tags:
  - 面经记录
  - 大厂面试
  - 后端开发
  - 技术面试
  - Java后端
---

# 001-某大厂后端技术面经

## 🏢 基本信息
- **公司**: 某大厂
- **岗位**: Java后端开发工程师
- **面试轮次**: 技术面试
- **面试日期**: 2024年
- **面试形式**: 在线面试

## 📋 面试题目

### 项目经验类

> [!question] 询问我工作的原因
> **题目描述**: 了解候选人的工作动机和职业规划
> **考察要点**: 职业态度、工作动机
> **标签**: #项目经验 #Behavior面试 #高频题 #未开始

> [!question] 介绍智能客服项目，你主要负责哪块？遇到过什么困难？
> **题目描述**: 详细介绍负责的项目模块和遇到的技术挑战
> **考察要点**: 项目经验、问题解决能力、技术深度
> **标签**: #项目经验 #智能客服 #高频题 #未开始

> [!question] 智能客服回答的指标有哪些？怎么评估出来的？
> **题目描述**: 了解智能客服系统的评估体系和指标设计
> **考察要点**: 业务理解、指标设计、数据分析
> **标签**: #项目经验 #业务理解 #中频题 #未开始

> [!question] 你们这个模块的流量大概是多少？
> **题目描述**: 了解系统的并发量和性能要求
> **考察要点**: 系统规模、性能认知
> **标签**: #项目经验 #系统设计 #高频题 #未开始

> [!question] 这个模块中，你最关注哪个点？
> **题目描述**: 了解候选人的技术关注点和深度思考
> **考察要点**: 技术敏感度、关键问题识别
> **标签**: #项目经验 #技术深度 #中频题 #未开始

### 基础技术类

> [!question] 讲一讲HashMap的数据结构？链表变红黑树，红黑树退化成链表
> **题目描述**: HashMap底层实现原理，重点是链表和红黑树的转换机制
> **考察要点**: Java集合框架、数据结构、性能优化原理
> **标签**: #基础技术 #Java集合 #数据结构 #高频题 #未开始

> [!question] 有用过Redis吗？讲一讲zset的底层结构
> **题目描述**: Redis有序集合的底层实现原理
> **考察要点**: Redis数据结构、跳表原理
> **标签**: #框架组件 #Redis #数据结构 #高频题 #未开始

> [!question] 红黑树和跳表的区别是什么？
> **题目描述**: 比较两种数据结构的特点和适用场景
> **考察要点**: 数据结构理解、性能分析
> **标签**: #基础技术 #数据结构 #中频题 #未开始

> [!question] 一次RPC的过程
> **题目描述**: 详细描述远程过程调用的完整流程
> **考察要点**: 分布式系统、网络通信、序列化
> **标签**: #分布式系统 #RPC #高频题 #未开始

> [!question] 应用层有哪些协议？
> **题目描述**: 计算机网络应用层协议的了解
> **考察要点**: 计算机网络基础
> **标签**: #基础技术 #网络协议 #中频题 #未开始

> [!question] http和https的区别
> **题目描述**: HTTP和HTTPS协议的区别和安全机制
> **考察要点**: 网络安全、加密原理
> **标签**: #基础技术 #网络协议 #网络安全 #高频题 #未开始

### 数据库类

> [!question] select * from table where a = 1 and b = 2 and c = 3 怎么建索引？如果a是状态，b是时间，c是用户id，怎么建索引？如果是select * from table where a = 1 or b = 2 or c = 3 怎么建索引？
> **题目描述**: 复合索引的设计原则，AND和OR查询的索引优化策略
> **考察要点**: MySQL索引原理、查询优化、索引设计
> **标签**: #框架组件 #MySQL #索引优化 #高频题 #未开始

### 算法类

> [!question] 非算法题：有1000个苹果，10个盒子，把所有苹果装到这10个盒子里，保证一个客户不管买多少个苹果(1-1000)都可以通过若干盒子的搭配得到所需的苹果数，怎么装？
> **题目描述**: 逻辑思维题，考察数学思维和问题分解能力
> **考察要点**: 逻辑思维、数学建模、问题分解
> **标签**: #算法题 #逻辑思维 #中频题 #未开始

> [!question] 算法题：leetcode 61 旋转链表
> **题目描述**: LeetCode经典链表操作题目
> **考察要点**: 链表操作、指针操作、边界处理
> **标签**: #算法题 #链表 #LeetCode #中频题 #未开始

### 深入技术讨论

> [!question] 有了解我们的工作强度吗？对base地上海可以接受吗？
> **题目描述**: 了解工作意愿和地理位置接受度
> **考察要点**: 工作适应性、地理接受度
> **标签**: #Behavior面试 #工作意愿 #中频题 #未开始

> [!question] 介绍一下你们的项目，客户群体是哪些？
> **题目描述**: 深入了解项目背景和业务场景
> **考察要点**: 业务理解、项目背景
> **标签**: #项目经验 #业务理解 #高频题 #未开始

> [!question] 讲讲这个项目里面你觉得印象比较深的最近的一些需求
> **题目描述**: 了解最近的工作内容和技术挑战
> **考察要点**: 工作内容、技术应用
> **标签**: #项目经验 #技术实践 #中频题 #未开始

> [!question] 浏览器上发起一个请求，然后到后端能够响应这个请求。你觉得这个过程中它都涉及到哪些过程？
> **题目描述**: 完整的HTTP请求响应流程，从前端到后端
> **考察要点**: 全栈理解、网络原理、系统架构
> **标签**: #系统设计 #网络原理 #高频题 #未开始

> [!question] TCP和HTTP他们两个协议的关系是什么样的？
> **题目描述**: TCP和HTTP协议的分层关系
> **考察要点**: 网络协议栈、协议关系
> **标签**: #基础技术 #网络协议 #高频题 #未开始

> [!question] 在Java技术栈里面想要暴露一个HTTP的接口，一般是用什么样的方式暴露一个HTTP接口？其中涉及到哪些技术？
> **题目描述**: Java Web开发中HTTP接口的实现方式
> **考察要点**: Java Web开发、Spring框架、HTTP实现
> **标签**: #框架组件 #Java Web #Spring #高频题 #未开始

### Spring框架相关

> [!question] 你知道哪些网关？
> **题目描述**: 了解API网关的知识和使用经验
> **考察要点**: 微服务架构、网关技术
> **标签**: #分布式系统 #网关 #微服务 #中频题 #未开始

> [!question] 你怎么理解线程安全问题？
> **题目描述**: 并发编程中的线程安全概念和解决方案
> **考察要点**: 并发编程、线程安全、同步机制
> **标签**: #基础技术 #并发编程 #线程安全 #高频题 #未开始

> [!question] 在Java里面它的内存模型是什么样的？
> **题目描述**: Java内存模型(JMM)的理解
> **考察要点**: JVM原理、内存模型、并发原理
> **标签**: #基础技术 #JVM #内存模型 #高频题 #未开始

> [!question] 堆里面的一个对象什么候会被回收掉？
> **题目描述**: Java垃圾回收机制和对象生命周期
> **考察要点**: JVM垃圾回收、对象生命周期
> **标签**: #基础技术 #JVM #垃圾回收 #高频题 #未开始

> [!question] 在Java里面一个类什么时候会被加载？
> **题目描述**: Java类加载机制和时机
> **考察要点**: JVM类加载、类加载时机
> **标签**: #基础技术 #JVM #类加载 #中频题 #未开始

> [!question] AOP是如何实现的？
> **题目描述**: Spring AOP的实现原理
> **考察要点**: Spring框架、AOP原理、动态代理
> **标签**: #框架组件 #Spring #AOP #中频题 #未开始

> [!question] 动态代理JDK proxy和cglib这两个有什么差别？
> **题目描述**: Java动态代理的两种实现方式比较
> **考察要点**: 代理模式、JDK vs CGLIB
> **标签**: #基础技术 #代理模式 #Spring #中频题 #未开始

> [!question] 在spring里面一个bean什么时候会被初始化？
> **题目描述**: Spring Bean的生命周期和初始化时机
> **考察要点**: Spring容器、Bean生命周期
> **标签**: #框架组件 #Spring #Bean生命周期 #中频题 #未开始

> [!question] 如果在bean初始化的过程中，我们想额外做一些定制化的业务逻辑，spring提供了哪些方式？
> **题目描述**: Spring Bean初始化的扩展点和定制化方法
> **考察要点**: Spring扩展机制、生命周期回调
> **标签**: #框架组件 #Spring #生命周期 #中频题 #未开始

### Redis深入

> [!question] 你最常用的Redis的数据结构是什么？
> **题目描述**: Redis数据结构的使用经验
> **考察要点**: Redis应用、数据结构选择
> **标签**: #框架组件 #Redis #数据结构 #高频题 #未开始

> [!question] 有了解过zset吗？底层是什么数据结构？
> **题目描述**: Redis有序集合的底层实现
> **考察要点**: Redis底层原理、跳表结构
> **标签**: #框架组件 #Redis #跳表 #中频题 #未开始

> [!question] 跳表的时间复杂度是多少？说说为什么是O(logN)
> **题目描述**: 跳表的时间复杂度分析
> **考察要点**: 数据结构分析、算法复杂度
> **标签**: #基础技术 #数据结构 #算法复杂度 #中频题 #未开始

### MySQL深入

> [!question] 说一下MySQL的索引
> **题目描述**: MySQL索引的类型、原理和使用
> **考察要点**: 数据库索引、查询优化
> **标签**: #框架组件 #MySQL #索引 #高频题 #未开始

> [!question] 联合索引是一个什么样的结构？它在查找一个数据的时候是一个什么样的流程？
> **题目描述**: 复合索引的存储结构和查询流程
> **考察要点**: 索引原理、B+树结构、查询优化
> **标签**: #框架组件 #MySQL #联合索引 #中频题 #未开始

> [!question] 算法题：LeetCode 6 Z字形变换
> **题目描述**: LeetCode字符串处理题目
> **考察要点**: 字符串处理、模拟算法
> **标签**: #算法题 #字符串 #LeetCode #中频题 #未开始

## 💡 面试总结

> [!tip] 关键洞察
> **重点考察领域**:
> - Java基础和JVM原理（高频）
> - Spring框架深度理解
> - Redis和MySQL底层原理
> - 项目经验和系统设计思维
> - 网络协议和分布式系统基础
> 
> **难度评估**: 中高级，需要深入理解底层原理
> 
> **个人表现反思**: 
> - 需要加强JVM和并发编程知识
> - Redis和MySQL底层原理需要深入学习
> - 项目经验描述需要更加系统化 