---
tags:
  - 面经项目
  - 项目设计
  - 八股文
  - 面试准备
  - 知识管理
---

# 00-面经项目设计方案

## 🎯 项目目标

> [!info] 核心目标
> 通过收集20-50篇目标岗位面经，建立系统化的知识库，支持：
> - 📚 分类学习和知识整理
> - 🔥 高频题目识别和重点复习  
> - 📊 个人掌握状态跟踪
> - 💼 实际面试经历记录

## 🏗️ 项目结构设计


## 🏷️ 标签系统设计

### 一级分类标签
- `#基础技术` - 编程语言、数据结构、计算机网络、操作系统
- `#框架组件` - 数据库、缓存、消息队列、Web框架
- `#分布式系统` - 一致性算法、分布式缓存、分布式锁
- `#系统设计` - 架构设计、高并发方案
- `#算法题` - LeetCode、编程题
- `#项目经验` - 项目介绍、技术难点
- `#Behavior面试` - 软技能、行为面试

### 二级分类标签
- `#MySQL` `#Redis` `#Spring` `#JVM` `#网络协议`
- `#事务隔离` `#索引优化` `#缓存策略` `#垃圾回收`

### 状态管理标签
- `#未开始` - 还未学习的题目
- `#一轮复习` - 初步了解，需要深入
- `#已掌握` - 基本掌握，可以回答
- `#灵活运用` - 深度理解，可以扩展

### 频率标签
- `#高频题` - 出现频率很高的题目
- `#中频题` - 偶尔出现的题目
- `#面试遇到` - 个人面试中实际遇到的题目

## 📋 标准化模板

### 面经记录模板
```markdown
---
tags:
  - 面经记录
  - [公司名称]
  - [岗位类型]
  - [面试轮次]
---

# 001-[公司]-[岗位]面经

## 🏢 基本信息
- **公司**: 
- **岗位**: 
- **面试轮次**: 
- **面试日期**: 
- **面试形式**: 

## 📋 面试题目

### [技术分类1]
> [!question] [题目1]
> **题目描述**: 
> **考察要点**: 
> **标签**: #[分类] #[子分类] #[频率] #[状态]

### [技术分类2]
> [!question] [题目2]
> **题目描述**: 
> **考察要点**: 
> **标签**: #[分类] #[子分类] #[频率] #[状态]

## 💡 面试总结
> [!tip] 关键洞察
> - 重点考察领域
> - 难度评估
> - 个人表现反思
```


## 🔄 简化维护策略

### 1. 自动化标签管理
> [!tip] 标签使用策略
> - 每个题目**必须包含**：分类标签 + 状态标签
> - **可选包含**：频率标签 + 面试遇到标签
> - 使用Obsidian标签面板快速筛选和统计

### 2. 看板式管理
> [!success] 主控制面板功能
> - 使用Dataview插件自动统计各状态题目数量
> - 按分类显示学习进度
> - 高频题目自动汇总
> - 面试遇到题目追踪

### 3. 渐进式完善
> [!note] 维护原则
> - **第一阶段**：快速记录面经，只打基础标签
> - **第二阶段**：按需整理知识点，补充详细标签
> - **第三阶段**：根据学习进度更新状态标签

## 📊 实际应用示例

### 示例题目标记
```markdown
> [!question] HashMap的数据结构？链表变红黑树，红黑树退化成链表
> **考察要点**: Java集合框架，数据结构转换机制
> **标签**: #框架组件 #Java集合 #高频题 #已掌握

> [!question] Redis的zset底层结构
> **考察要点**: Redis数据结构实现原理
> **标签**: #框架组件 #Redis #中频题 #一轮复习 #面试遇到
```

### 高频题目自动识别
- 当同一个知识点在多个面经中出现时，自动标记为 `#高频题`
- 使用Obsidian搜索功能快速定位所有高频题目
- 优先复习高频题目，提高复习效率
