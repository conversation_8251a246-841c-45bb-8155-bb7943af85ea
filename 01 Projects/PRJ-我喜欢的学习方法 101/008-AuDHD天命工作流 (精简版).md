# AuDHD天命工作流 (精简版)

## 一、核心问题：内在驱动力的冲突

许多项目停滞不前，源于两种强大的内在驱动力之间的冲突：一种是追求详尽规划、力求完美的“规划”倾向；另一种是渴望立即动手、探索新奇可能性的“探索”倾向。

当“规划”倾向坚持必须先有完美蓝图才能行动时，“探索”倾向则试图将项目引向任何看起来有趣的方向。这两种力量的对抗导致了行动上的瘫痪，项目无法有效推进。

本工作流的根本目的，就是调和这一内在冲突。

## 二、核心原则：以实体为蓝图

本工作流的核心原则是：**项目的具体实体，就是其唯一的规划蓝图。**

这意味着，从项目开始的那一刻起，我们唯一需要维护和关注的，就是那个具体的、可操作的、持续演进的项目本身。所有独立于项目实体之外的抽象设计文档、大纲、流程图等都应被禁止。

这一原则通过消除“抽象规划”这一冲突发生的根源，迫使两种驱动力从对立转向合作，共同作用于一个具体、稳定的现实之上。

## 三、工作协议：四个阶段的循环

本工作流通过一个由四个阶段构成的、不断循环的协议来运作。其目标并非“完成”项目，而是确保项目能够持续、健康地“进行”。

这是一个循环：完成第四阶段后，应回到第二阶段，再继续第三、第四阶段，周而复始。

### 第一阶段：创造种子

这是项目的起点，且只执行一次。

**任务：** 以最快、最简单的方式，创造出项目最核心的、功能闭环的最小可行版本。我们称之为“种子”。这个“种子”必须是一个能独立运作的实体。

**关键点：** 此阶段，速度压倒一切。目标是创造出一个“活的”东西，标志着项目从0到1。

### 第二阶段：建立支架

在为项目增加任何新功能之前，必须先为这个新功能建立一个“支架”。

**任务：** 在当前的项目实体中，为未来的新功能创造一个可见但没有实际功能的占位符或框架。

**关键点：** 此阶段的核心作用，是将未来的不确定性，转化为当前的可预测性。它为下一步的工作提供了清晰、具体的目标，极大地降低了关于“下一步做什么”的焦虑。

### 第三阶段：填充支架

有了稳定的现有结构和一个待填充的“支架”后，任务将变得极其清晰和聚焦。

**任务：** 让上一阶段建立的“支架”真正地实现其功能。任务不再是宏大而模糊的“增加一个新功能”，而是一个界限明确、目标收敛的工程问题。

**关键点：** 在这个阶段，“探索”倾向可以发挥创意，而“规划”倾向则负责保障质量，确保新功能被干净、自洽地整合进现有结构中。

### 第四阶段：整理结构

在成功增加一个新功能后，必须强制性地进入整理和优化的阶段。

**任务：** 在此阶段，严格禁止增加任何新功能或新支架。唯一允许做的，是“打扫卫生”和“加固地基”——即对现有项目进行重构、优化和清理，确保其健康和稳定。

**关键点：** 这个阶段完全服务于项目的长期健康。它像是系统的维护期，能确保项目在不断迭代中保持稳固。完成此阶段后，项目便进入一个更稳定的新版本，可以开始下一轮的“建立支架”。

## 四、协议的演化与扩展

当项目变得日益复杂时，此协议也需要相应地“演化”。

- **从“建立支架”到“定义接口”：** 对于复杂的系统互联，简单的占位符不再足够。需要升级为预先定义好新旧系统之间交互的“接口”或“契约”。
    
- **从“整理结构”到“定期重构”：** 对于大型项目，临时的整理和优化需要升级为制度化的、有固定周期的“重构期”，专门用于偿还“技术债”或“创意债”，防止系统因混乱累积而崩溃。
    

## 五、必须接受的代价

采用此工作流，需要接受并支付以下三种“战略性成本”：

1. **代价一：放弃对“全局最优”的幻想。** 这种自下而上的演化方式，其最终结构很可能不是理论上最完美的。必须接受“足够好”，而非追求“完美”。
    
2. **代价二：接受持续的“维护成本”。** 必须将“整理”和“重构”作为核心实践，这意味着一部分宝贵的时间将被用于维护，而非创造新东西。
    
3. **代价三：拥抱“方向的不确定性”。** 项目的最终形态是在过程中逐渐“涌现”的。必须学会在相当长的一段时间里，接受对最终目标的不确定性，并信任过程本身。
    

## 六、与敏捷开发的区别

此工作流看似与“敏捷开发”相似，但其核心驱动力完全不同。

- **敏捷开发** 的核心驱动力，是应对 **外部市场** 的不确定性，是一种项目管理策略。
    
- **本工作流** 的核心驱动力，是应对 **内部认知** 的冲突，是一种自我调节策略。
    

因此，本协议可以看作是在常规迭代方法之上，增加了一个高度个人化的、以调和内部认知系统为目的的“安全层”。