https://www.xiaohongshu.com/explore/688468850000000011003ec4?xsec_token=ABoXAIXIrWxWkY-BAa0oN8lmhThulpBlxGlpFS7bQWDvQ=&xsec_source=pc_user
# 豆包背书法总结

给豆包打电话，你不说它就说话==可以点击打断== 【图3】，但我觉得有点麻烦

感觉输入指令的效果不是很好，你说话停下来了，豆包就开始说话了。（输入一个不要讲话的指令，它要说一句话）【图2】

我试着让它不说话的方法：

## 1️⃣ 自己一直说，不给它说话的机会

（我背书也是这个习惯，一般会提前把知识点思考理解一遍，背书的时候就不用在停下来理解了，背书这过程中的突然出现的知识点之间的链接，或自顾自话的，我都会说出来，可能会因为思考走错话题了，但不会停下来。）这也是我豆包背书的时候不说话的原因

## 2️⃣ 手动点击停止说话

（但是我觉得有点打扰状态）

我是怎么使用豆包背书的：

比如：大纲题，了解背景要理解的知识点，老师讲完课后，复盘一遍（重头到尾看一遍，一些知识点，定义，特征我会举例子说给ai听【==因为理解的差异指的是一个词在各地方言不同，我理解的正确么？==】

ai就会给一个答案【==不全面 不对 对的==】【图4】就这样一点一点弄清楚。

## 过个过程看掌握的情况，时间看长短

### 2️⃣ 第二天上午，我先背昨天大的知识点，复习一遍

大概四五十分钟

### 3️⃣ 接着会背昨天新的知识点

（大框架→一板块的一个知识点→细节）一个板块背完，一遍读一遍背，全程有声音，我会重复一遍背诵【这时候就是一直和ai保持电话联系，我会一直输出，一直背话，不会的看课本，一旦停下来ai就会说话，对我来说就到了一个非常好的监督作用，我不说话可能是走神了】这个过程大概半小时，点赞！—— 做伴学习

### 4️⃣ 整个背完之后，我会重新梳理一遍框架，把书放在一边，对着分模块问背一遍，哪地方卡了我就会问一下ai，比如说，我背丁玄的记忆存储的知识点，后面提取的知识点我忘记了

我就会问【==可以提醒我一下这个课的定义是什么吗？==】

它开始扩展【==下一个知识点是什么？==】

【==好了别说了，我自己背，或者我开始背==】，这样可以打断ai说话。

3.4 背诵的整个过程一个半小时（有时候内容特别多就—点其他时间，我自己背两个半小时是极限了）

### 5️⃣ 全部背完之后，我就自己弄一个框架，把要背的知识点的题目填进去，接着把练习题做了，练习题虽看不清楚的标出来，错误的选项如果是知识点漏洞，我就抄到笔记本上，第二天复习的时候先背。

### 6️⃣ 第二天早上，我会先把昨天的重新梳理

背书有一遍一大概三四十分钟，如果背书有问题的地方【==首部从错误词语——就是不会从前面住背，后面跟着住的，效果是——开始的好，【背部顺序：错误不要想着已经会背了就从这个地方开始背背好的知识点】==最后要过一遍框架，不让已己忘记重要的。整个例子：【==长时记忆温度就彩图回忆==】，忘记了具体三点，【==背书时间四个板块擦除，长时记忆是时间记忆，长时记忆，这个知识点要在长时记忆，长时记忆的知识点有哪二个知识点，第一第二第三==】
