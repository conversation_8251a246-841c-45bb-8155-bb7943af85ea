# 007-SDT学习法-看做教

## 🎯 核心观点

> [!warning] 学习的两个极端问题
> - **要么学的课程要用没有**，浪费了一堆💰，最后啥也没搞着，变成了纯韭菜
> - **要么很本看不完**，就跟背词书第一页的abandon一样，都得你abandon了

> [!danger] 避免边学边tm学习
> 你一定认识一些人，当他们准备千点大事，比如做自媒体，或者做个项目，会看各式各样的课，刷很多的视频，然后发现要么学的课程理论用没有，浪费了一堆💰，最后啥也没搞着，变成了纯韭菜。

## 📋 SDT 学习公式

> [!tip] SDT = See + Do + Teach
> 分享一个公式，叫做SDT

### 1️⃣ See（看一遍别人怎么做）

> [!example] 大师教学的心态
> 类似于很多大师在教人的时候，会说「看好了，我只教一次」。
> 
> 那么，你也得抱着「看好了，我只学一次」的念头。

> [!note] 看的要求
> - 这就好比，你明天要考期末考试了，书还没看，但是所有过的书，你都只能看一次，看一页，撕一页，一杯咖啡，一个下午，看完这，这样你反而能充分让自己已经一种钟情境的。

### 2️⃣ Do（做一遍）

> [!success] 实践的重要性
> 就跟你看美妆视频一样，眼睛学会了，手没学会，太正常了，在游泳池边劳边了多少人游泳，永远学不会游泳，你需要跳到游泳里，大量的练习，来反复看检测自己哪里还不够熟练，再接着，就是

### 3️⃣ Teach（教一遍）

> [!info] 教学相长
> **不是因为学者喜欢教人，而是因为教人很容易才去教的学习方法是教学方法里学习率最高的。**
> 
> 那么怎么教人呢？与作是一种教人，找一个朋友分享也是一种教，简而言之，把输入变成输出的结果。

> [!quote] 费曼学习法的升级
> 类似于你是 Deepseek，你看了一个课，看了一本书，这本书对你输入了很乘积的词'就是常说总结给我感到了现的理解，比如思考过程，并且给出思考结果。
> 
> 核心来说，不要通过学习来学习，而是通过 doing 来 learning。

## 💡 关键洞察

> [!important] 钢琴只有88个琴键
> 钢琴只有88个琴键，而有的人希望通过找到不存在的第89个琴键，来弹通过车，比如看各种视频，又或者是看各种各样的帖子，更多时候，其实无限拖延了真正的行动落地的时间。

> [!abstract] 行动状态vs学习状态  
> **实际上，只存在两个状态：往前走一步，和原地不动，不存在准备走出去这个状态。**
> 
> 不要相信懒力不喜欢学上，相信 nike ——— Just do it。 