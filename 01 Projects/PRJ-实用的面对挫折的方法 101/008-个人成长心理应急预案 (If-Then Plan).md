

**核心原则：停止思考，立即行动**  
当负面情绪或困境来临时，思考是陷阱，行动是解药。本预案旨在为您提供一套“自动驾驶”的应急指令，用最小的能量，打破负循环，夺回掌控感。

---

## 1. 如果：在时间块内无法产出，陷入“写不出/写不好/无法专注”的瘫痪状态…
**那么 (立刻执行)：**
- **物理中断**：立刻站起来，离开座位。去喝一杯水，或者走到窗边做三个深呼吸。
- **标准归零**：告诉自己“我的目标不是‘写好’，而是‘写出来’”。打开番茄钟，只设定15分钟，专注这15分钟即可。
- **更换媒介**：如果电脑前写不出，就拿起纸笔；如果代码卡住了，就用伪代码或画流程图。

---

## 2. 如果：总想修改计划，陷入“过度规划”的焦虑…
**那么 (立刻执行)：**
- **想法捕捉**：把“修改计划”的想法写在便利贴上。
- **宣布规则**：大声对自己说：“现在是执行时间，不是规划时间。”
- **信任承诺**：相信我们共同制定的计划，承诺至少完整执行一周再做评判。

---

## 3. 如果：有突发事件（如朋友邀约）打乱计划，感觉“全完了”…
**那么 (立刻执行)：**
- **执行交换**：问自己“这件事我真的想去吗？”如果想，就用一个不那么核心的模块（如“创意切换”或“机动时间”）去交换，而非“破坏”计划。
- **快速回归**：活动结束后，不要内疚。看一眼日程表，立刻执行下一个时间块的任务。

---

## 4. 如果：被强烈的“挫败感”淹没（回忆失败、担心未来）…
**那么 (立刻执行)：**
- **情绪标注**：冷静地对自己说：“OK，‘挫败感’来了。我看见它了。它只是一个访客，不是我。”
- **两分钟胜利**：从任务清单里，找出一件最最最简单的事（如改一个错别字），立刻去做，用微小的胜利夺回控制感。
- **切断输入**：立刻关闭让你产生挫败感的帖子或社交媒体。戴上耳机，听一首能给你力量的歌。

---

## 5. 如果：开始纠结技术方向（如 Java vs AI），感觉选错了…
**那么 (立刻执行)：**
- **决策延期**：在日历上，翻到一个月后，创建一个事件，名字叫“技术方向深度研究日”。
- **宣布当前任务**：告诉自己“在那个日期前，我的唯一任务，是完成当前的求职计划。”
- **肯定当下**：认识到，无论未来方向如何，现在所学的扎实基础都是通用的宝贵财富。

---

## 6. 如果：因“公开学习 (Build in Public)”而焦虑，怕错过又怕没时间…
**那么 (立刻执行)：**
- **重新定义**：告诉自己“‘公开学习’不等于写长篇博客，可以只是在GitHub上提交一次代码，或发一条三句话的学习笔记。”
- **任务排序**：清晰地告诉自己“第一优先级：拿到Offer。第二优先级：构建影响力。”
- **降低标准**：如果觉得写不出来或写不好，就把标准降到最低。记住：完成比完美重要，持续比爆款重要。

---

## 7. 如果：美好的“职业愿景”（幻想）浮现在脑海中…
**那么 (立刻执行一个三步流程)：**
1. **拥抱与感受** (1分钟)：闭上眼睛，尽情地去感受那个成功的瞬间，为自己的“情绪油箱”加满油。
2. **追问与具体化** (2分钟)：立刻追问自己一个具体问题，把幻想拉到现实。例如，幻想“和面试官聊得投机” -> 追问“我们具体在聊哪个技术点？”
3. **逆向工程与关联** (2分钟)：根据上一步的答案，立刻把它和你今天的计划关联起来。例如，答案是“聊了秒杀架构” -> 关联“那我今天下午就核心梳理这个架构。”

[[003-永动机学习术及高中时间管理]]