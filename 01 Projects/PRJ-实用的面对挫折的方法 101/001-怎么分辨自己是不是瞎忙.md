# 渡过空转期，比任何技术突破都难

> **原文**
> 
> 刚毕业开始工作的那段时间，我几乎每天都在焦虑。早到晚走，实验跑了一堆，想着不怕辛苦勤能补拙。但现实并不买账，出不了成果，产品做了一半被也cancel了。有一次1on1我直接问了我 manager “我适不适合做科研？”，他没有正面回答我，转头给我的CR发了个comment。“Don’t sweat it when nothing’s happening. Real skills grow quietly.” 这两天实习生neurips估计赶不上了，竟然问了我类似的问题，让我一下子回想起来这个答案。
> 
> 关键是，到底怎样分辨自己是不是瞎忙？我一般有这几个标准： 
> 
> ▪️是否有哪怕一点点进步？就算是实验全失败了，但是做了代码的refactor也算。 
> 
> ▪️能不能解释失败？失败中的熵含量往往都更高，关键是能不能说清why not。
> 
> ▪️规划是不是更清晰？从“什么都试”到“知道哪些方向不值得再碰”的聚焦。 
> 
> 命中两条就可以继续卷，不然就好好复盘一下，别再错误的路上跑废了。慢慢的我养成了一些我觉得还不错的习惯帮自己度过这段时期：
> 
> 🟢 写Failure Log，解决问题就划掉。 
> 
> 🟢 每周五block 30min做总结。跟组里的principle学的，可以清楚的认识到这周做了/没做什么，以及下周做什么。 
> 
> 🟢 设置退出节点。RL最关键的就是exploration vs exploitation，有时候选择比努力更重要。 
> 
> 这些方法听起来都很小，但能给你一种“我在掌握节奏”的感觉，特别重要。比较反直觉的一点是，成长曲线从来不是光滑的，它更像step function。停滞期并不是浪费时间，而是知识结构重组、认知升级。Be patient and get prepared；耐得住寂寞，别急着焦虑，更不要轻易否定自己。

#推进progress #管理预期 #找到bottleneck #退出节点 #平台期 

---

## 我的思考和体会

博主身份是 google ai 研究员，这篇笔记的主题是“不要无意义输出”。

此处“无意义”指的是设置**退出节点**后，就需要及时退出；并非不鼓励主动创作。

以及给出了分辨瞎忙的几个标准：
>- 能找到最微小的进步
>- 存在可以解释的失败
>- 有了更清晰的规划

度过平台期的好习惯：
>- 写失败日志，解决问题就划掉
>- 每周五安排 30 分钟专用时间做总结：这周做了什么/没做什么/下周做什么
>- 设置退出节点

---

## 文中出现的英文短语解析

*   **manager**: 经理，主管。
*   **CR (Code Review)**: 代码审查。指在软件开发过程中，检查代码的过程，目的是发现和修复错误，提升代码质量。
*   **comment**: 评论，留言。
*   **“Don’t sweat it when nothing’s happening. Real skills grow quietly.”**: “当事情没有进展时，不要过分焦虑。真正的能力是在这个过程中悄无声息地积累起来的。” 这句话旨在安慰人不必为暂时的停滞而过度忧虑，强调真正的成长往往发生在默默努力的过程中。
*   **NeurIPS (Conference on Neural Information Processing Systems)**: 神经信息处理系统大会，是机器学习和计算神经科学领域的顶级学术会议。
*   **refactor**: 代码重构。指在不改变代码外在行为的前提下，对代码做出修改，以改进程序的内部结构。
*   **why not**: 为什么不行。在这里指对失败原因的深入分析和理解。
*   **Failure Log**: 失败日志。记录失败的尝试、原因和学到的教训，是一种有效的学习和反思工具。
*   **block 30min**: 预留或安排出30分钟的专用时间。
*   **principle (Principal Engineer)**: 首席工程师。公司中的高级技术职位，通常负责技术决策和指导团队。
*   **RL (Reinforcement Learning)**: 强化学习。机器学习的一个分支，智能体通过与环境的互动来学习如何做出决策以获得最大的累积奖励。
*   **exploration vs exploitation**: 探索与利用。强化学习中的一个经典权衡问题，即在“尝试新的、可能更好的选择（探索）”和“坚持使用当前已知的最优选择（利用）”之间找到平衡。
*   **step function**: 阶梯函数。函数图像呈阶梯状，表示成长或进展不是平滑连续的，而是一段时期的平台期后突然跃升。
*   **Be patient and get prepared**: 保持耐心，做好准备。鼓励在等待机会或突破时，积极积累和准备。

---
