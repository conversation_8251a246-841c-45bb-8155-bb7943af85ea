---
tags: fitness, exercise, tracker
---

# 30天居家锻炼记录表

## 计划概述
- **开始日期**: 2025-07-22
- **结束日期**: 2025-08-21
- **每日目标**:
  - 60分钟 "追溯普拉提" 视频跟练（背部、腰部、腿部）
  - 30分钟 户外跑步

## 30天记录

| 日期         | 普拉提 | 跑步  | 时长(分钟) | 备注              |
| ---------- | --- | --- | ------ | --------------- |
| 2025-07-22 | Y   | N   | 30     | 完成背部训练，背部疼痛有所缓解 |
| 2025-07-23 |     | Y   | 25     | 今天有点不想跑，但是跑了！   |
| 2025-07-24 |     |     |        |                 |
| 2025-07-25 |     |     |        |                 |
| 2025-07-26 | Y   |     | 7      |                 |
| 2025-07-27 | Y   | Y   | 60     |                 |
| 2025-07-28 |     |     |        |                 |
| 2025-07-29 |     |     |        |                 |
| 2025-07-30 |     |     |        |                 |
| 2025-07-31 |     |     |        |                 |
| 2025-08-01 |     |     |        |                 |
| 2025-08-02 |     |     |        |                 |
| 2025-08-03 |     |     |        |                 |
| 2025-08-04 |     |     |        |                 |
| 2025-08-05 |     |     |        |                 |
| 2025-08-06 |     |     |        |                 |
| 2025-08-07 |     |     |        |                 |
| 2025-08-08 |     |     |        |                 |
| 2025-08-09 |     |     |        |                 |
| 2025-08-10 |     |     |        |                 |
| 2025-08-11 |     |     |        |                 |
| 2025-08-12 |     |     |        |                 |
| 2025-08-13 |     |     |        |                 |
| 2025-08-14 |     |     |        |                 |
| 2025-08-15 |     |     |        |                 |
| 2025-08-16 |     |     |        |                 |
| 2025-08-17 |     |     |        |                 |
| 2025-08-18 |     |     |        |                 |
| 2025-08-19 |     |     |        |                 |
| 2025-08-20 |     |     |        |                 |
| 2025-08-21 |     |     |        |                 |

## 完成情况统计

```dataviewjs
try {
  // 获取当前文件内容
  const currentFile = dv.current();
  const content = await dv.io.load(currentFile.file.path);

  if (!content) {
    dv.paragraph("无法读取文件内容");
  } else {
    const rows = content.split('\n').filter(line => line.match(/^\| 2025-\d\d-\d\d/));

    // 统计数据
    let totalDays = 0;
    let pilatesCompleted = 0;
    let runningCompleted = 0;
    let totalMinutes = 0;

    rows.forEach(row => {
      const columns = row.split('|').map(col => col.trim());
      if (columns.length >= 6) { // 表格有6列（包括首尾空列）
        const pilates = columns[2];
        const running = columns[3];
        const duration = parseInt(columns[4]) || 0;
        
        if (pilates === 'Y' || running === 'Y') totalDays++;
        if (pilates === 'Y') pilatesCompleted++;
        if (running === 'Y') runningCompleted++;
        totalMinutes += duration;
      }
    });

    // 显示统计结果
    dv.paragraph(`- **已完成天数**: ${totalDays}/30 (${Math.round(totalDays/30*100)}%)`);
    dv.paragraph(`- **普拉提完成次数**: ${pilatesCompleted}/30 (${Math.round(pilatesCompleted/30*100)}%)`);
    dv.paragraph(`- **跑步完成次数**: ${runningCompleted}/30 (${Math.round(runningCompleted/30*100)}%)`);
    dv.paragraph(`- **总锻炼时间**: ${totalMinutes}分钟`);
  }
} catch (error) {
  dv.paragraph("读取文件时出错: " + error.message);
}
```

## 每周完成情况

```dataviewjs
try {
  // 获取当前文件内容
  const currentFile = dv.current();
  const content = await dv.io.load(currentFile.file.path);

  if (!content) {
    dv.paragraph("无法读取文件内容");
  } else {
    const rows = content.split('\n').filter(line => line.match(/^\| 2025-\d\d-\d\d/));

    // 按周分组
    const weekData = {};
    for (let i = 1; i <= 5; i++) {
      weekData[i] = {days: 0, pilates: 0, running: 0, minutes: 0};
    }

    rows.forEach(row => {
      const columns = row.split('|').map(col => col.trim());
      if (columns.length >= 6) { // 表格有6列（包括首尾空列）
        const date = columns[1];
        const dateObj = new Date(date);

        // 计算从开始日期(2025-07-22)算起的天数，然后计算周数
        const startDate = new Date('2025-07-22');
        const daysDiff = Math.floor((dateObj - startDate) / (1000 * 60 * 60 * 24));
        const weekNumber = Math.floor(daysDiff / 7) + 1;

        const pilates = columns[2];
        const running = columns[3];
        const duration = parseInt(columns[4]) || 0;

        if (weekNumber >= 1 && weekNumber <= 5) {
          if (pilates === 'Y' || running === 'Y') weekData[weekNumber].days++;
          if (pilates === 'Y') weekData[weekNumber].pilates++;
          if (running === 'Y') weekData[weekNumber].running++;
          weekData[weekNumber].minutes += duration;
        }
      }
    });

    // 创建表格
    const tableRows = [];
    for (let i = 1; i <= 5; i++) {
      tableRows.push([
        `第${i}周`,
        weekData[i].days,
        weekData[i].pilates,
        weekData[i].running,
        weekData[i].minutes
      ]);
    }

    dv.table(["周次", "锻炼天数", "普拉提次数", "跑步次数", "总时长(分钟)"], tableRows);
  }
} catch (error) {
  dv.paragraph("读取文件时出错: " + error.message);
}
```

## 锻炼完成情况热力图

```dataviewjs
const currentFile = dv.current();
const content = await dv.io.load(currentFile.file.path);
const rows = content.split('\n').filter(line => line.match(/^\| 2025-\d\d-\d\d/));
const data = [];

rows.forEach(row => {
  const columns = row.split('|').map(col => col.trim());
  if (columns.length >= 6) {
    const date = columns[1];
    const pilates = columns[2];
    const running = columns[3];

    const dateObj = new Date(date);
    const startDate = new Date('2025-07-01');
    const endDate = new Date('2025-08-31');

    if (dateObj >= startDate && dateObj <= endDate) {
      let value = 0;
      if (pilates === 'Y' && running === 'Y') {
        value = 2;
      } else if (pilates === 'Y' || running === 'Y') {
        value = 1;
      }

      data.push({
        date: date,
        value: value
      });
    }
  }
});

const config = {
  fromDate: '2025-07-22',
  toDate: '2025-08-21',
  data: data,
  graphType: 'calendar',
  cellStyleRules: [
    { color: '#ebedf0', min: 0, max: 1 },
    { color: '#9be9a8', min: 1, max: 2 },
    { color: '#40c463', min: 2, max: 3 }
  ],
  showCellRuleIndicators: true,
  startOfWeek: 1
};

renderContributionGraph(this.container, config);
```

