# 统一项目梳理模板

> **目标**：30分钟快速启动 + 深度技术分析的完整项目梳理方案
> **方法**：分阶段填写 + AI Agent深度协作
> **原则**：行动优先，深度跟进，数据支撑

---

## ⚡ 阶段1：快速启动（30分钟）

### 🎯 项目基础信息（10分钟填写）

**项目选择**：从以下5个项目中选择1个最有信心的
- [ ] 拼团项目（高并发）- 分布式锁、缓存、消息队列
- [ ] 服务治理（系统优化）- 性能调优、监控、问题排查  
- [ ] 双token鉴权（架构安全）- OAuth2.0、JWT、安全设计
- [ ] 风险账号验证（业务风控）- 实时计算、规则引擎
- [ ] 营销管理平台（高吞吐）- 异步处理、任务调度

**我选择的项目**：____

**项目概况**：
- **项目名称**：____
- **项目类型**：高并发/系统优化/架构安全/业务风控/高吞吐
- **个人角色**：主导设计/核心开发/技术负责人
- **项目周期**：____个月
- **团队规模**：____人

**一句话亮点**：
"我____（角色）设计并实现了____（项目），解决了____（问题），支撑了____（规模）的业务，取得了____（成果）"

### 📊 核心数据快速收集（10分钟）

**P0级数据（面试必须有）**：
- **QPS**：峰值____/秒，平均____/秒
- **响应时间**：TP99 ____ms，平均____ms
- **并发用户**：峰值____人，日活____万
- **优化效果**：从____提升到____（提升____%）

**数据收集方法**：
- Skywalking：应用性能 → 服务概览 → 截图保存
- 腾讯云监控：云监控 → 云服务器监控 → 导出数据
- 数据库查询：统计业务关键指标

### 🛠️ 技术栈确认（5分钟）

**我的技术栈**：
- [ ] 微服务框架：Spring Cloud / Dubbo / 其他：____
- [ ] 消息队列：RocketMQ / Kafka / 其他：____
- [ ] 数据库：MySQL + Redis
- [ ] 监控工具：Skywalking + 腾讯云监控

### 🎤 3分钟项目介绍（5分钟准备）

**第1分钟：项目背景和价值**
"这个项目是为了解决____问题，当时的业务背景是____，面临的核心挑战是____"

**第2分钟：技术方案和亮点**  
"我们采用了____架构，核心技术栈包括____，主要解决了____技术难点"

**第3分钟：个人贡献和成果**
"我在项目中主要负责____，最终取得的效果是____（具体数据）"

---

## 🚀 阶段2：深度分析（使用AI Agent协作）

### 🤖 AI Agent深度协作指导

**第一轮：技术方案分析**
```
AI Agent，请深度分析这个项目：

【项目完整信息】
项目名称：____
项目类型：____（高并发/系统优化/架构安全/业务风控/高吞吐）
个人角色：____
技术栈：____
业务背景：____
核心数据：QPS____，响应时间____ms，用户规模____万
技术难点：____
解决方案：____

【请你深度分析】
1. 技术方案的核心优势和创新点
2. 可能的面试追问点和标准答案
3. 相关技术原理的深度解释
4. 项目亮点的最佳表达方式
5. 与同类方案的对比优势
6. 可能的改进方向和扩展思路
```

**第二轮：面试追问准备**
```
AI Agent，请帮我准备面试追问：

【技术难点详情】
难点1：____（问题背景、技术挑战、业务影响）
解决方案：____（技术选型、实现思路、关键代码）
效果数据：____（性能提升、问题解决程度）

【请你深度分析】
1. 每个技术方案的核心优势和创新点
2. 可能的面试追问点和深度技术问题
3. 相关技术原理的底层实现机制
4. 与业界主流方案的对比分析
5. 方案的扩展性和改进空间
6. 如何体现个人的技术深度和思考能力
```

---

## 📋 阶段3：完整数据收集

### P1级数据（面试加分项）
- **用户规模**：____万用户，增长率____%
- **业务量**：日均____单/次，峰值____单/次
- **转化率**：____%，同比提升____%
- **成功率**：____%，异常处理____%

### P2级数据（技术深度体现）
- **系统资源**：CPU使用率____%，内存使用率____%
- **数据库性能**：连接池使用率____%，慢查询____%
- **缓存命中率**：____%，缓存穿透率____%
- **消息队列**：堆积量____，处理速度____条/秒

### 数据收集详细方法
1. **Skywalking监控**：
   - 登录地址：____
   - 查看路径：应用性能 → 服务概览 → 选择时间范围
   - 导出数据：截图保存关键指标

2. **腾讯云监控**：
   - 控制台路径：云监控 → 云服务器监控
   - 关键指标：CPU、内存、网络、磁盘
   - 导出方式：图表截图 + 数据导出

3. **数据库查询示例**：
   ```sql
   -- 业务数据统计
   SELECT COUNT(*) as total_orders,
          AVG(amount) as avg_amount
   FROM orders
   WHERE create_time >= '2024-01-01';
   ```

---

## 🛠️ 阶段4：技术方案深度分析

### 完整技术栈
- **后端框架**：____（版本____，选择原因：____）
- **数据库**：____（分库分表策略：____）
- **缓存**：____（缓存策略：____，一致性方案：____）
- **消息队列**：____（消息模型：____，可靠性保证：____）
- **监控**：____（监控指标：____，告警策略：____）
- **注册中心**：____（服务发现机制：____）
- **配置中心**：____（配置管理策略：____）
- **网关**：____（路由策略：____，限流策略：____）

### 系统架构设计
```
详细系统架构图：
[用户] → [CDN] → [负载均衡] → [API网关]
                                    ↓
[服务A] ← [注册中心] → [服务B] → [服务C]
   ↓           ↓           ↓
[缓存]     [消息队列]    [数据库]
   ↓           ↓           ↓
[监控系统] ← [日志系统] → [配置中心]
```

**架构设计亮点**：
- 高可用设计：____
- 扩展性考虑：____
- 性能优化：____
- 安全性保障：____

### 核心技术亮点深度分析

#### 技术亮点1：____
- **应用场景**：____
- **技术挑战**：____
- **解决方案**：____
- **技术原理**：____
- **实现细节**：____
- **性能表现**：____
- **可能追问**：____
- **标准回答**：____

#### 技术亮点2：____
- **应用场景**：____
- **技术挑战**：____
- **解决方案**：____
- **技术原理**：____
- **实现细节**：____
- **性能表现**：____
- **可能追问**：____
- **标准回答**：____

#### 技术亮点3：____
- **应用场景**：____
- **技术挑战**：____
- **解决方案**：____
- **技术原理**：____
- **实现细节**：____
- **性能表现**：____
- **可能追问**：____
- **标准回答**：____

---

## 🎯 阶段5：核心技术难点深度剖析

### 技术难点1：____
- **问题背景**：____（业务场景、技术现状、面临挑战）
- **问题分析**：____（根因分析、影响评估、解决紧迫性）
- **方案调研**：____（技术选型对比、方案优劣分析）
- **解决方案**：____（具体实现方案、技术架构设计）
- **实现细节**：____（关键代码逻辑、配置参数、部署方案）
- **效果验证**：____（测试方法、性能数据、业务指标）
- **经验总结**：____（技术收获、踩坑经验、改进思路）

**面试追问准备**：
- Q: 为什么选择这个方案？A: ____
- Q: 还有其他解决方案吗？A: ____
- Q: 如何保证方案的可靠性？A: ____

### 技术难点2：____
- **问题背景**：____
- **问题分析**：____
- **方案调研**：____
- **解决方案**：____
- **实现细节**：____
- **效果验证**：____
- **经验总结**：____

**面试追问准备**：
- Q: ____？A: ____
- Q: ____？A: ____
- Q: ____？A: ____

### 技术难点3：____
- **问题背景**：____
- **问题分析**：____
- **方案调研**：____
- **解决方案**：____
- **实现细节**：____
- **效果验证**：____
- **经验总结**：____

**面试追问准备**：
- Q: ____？A: ____
- Q: ____？A: ____
- Q: ____？A: ____

---

## 🎤 阶段6：面试表达深度准备

### 深度技术追问准备

#### 架构设计类
1. **为什么选择这个技术架构？**
   答：基于____业务特点，考虑到____技术要求，对比了____方案，最终选择____因为____

2. **如果重新设计会如何改进？**
   答：基于现在的经验，我会在____方面进行优化，比如____，预期能够____

3. **系统的扩展性如何保证？**
   答：在设计时考虑了____，采用了____模式，支持____扩展，具体实现是____

#### 性能优化类
4. **性能优化的具体措施有哪些？**
   答：主要从____几个方面优化：1)____，效果是____；2)____，效果是____

5. **如何定位和解决性能瓶颈？**
   答：使用____工具监控，发现____瓶颈，通过____方法定位，最终通过____解决

#### 高可用性类
6. **如何保证系统的高可用性？**
   答：从____几个层面保证：1)____设计，2)____机制，3)____策略，可用性达到____

7. **遇到的最大技术挑战是什么？**
   答：最大挑战是____，难点在于____，我的解决思路是____，最终通过____解决

#### 技术深度类
8. **这个技术方案的核心原理是什么？**
   答：核心原理是____，基于____理论，实现机制是____，关键点在于____

9. **与业界主流方案相比有什么优势？**
   答：对比了____方案，我们的优势在于____，适用场景是____，性能表现____

10. **如果并发量增加10倍怎么处理？**
    答：会从____几个方面应对：1)____，2)____，3)____，预期能够支撑____

---

## ✅ 完成度检查清单

### 阶段1：快速启动（30分钟）
- [ ] 项目选择已确定
- [ ] 项目基础信息已填写
- [ ] 核心数据已收集
- [ ] 技术栈已确认
- [ ] 3分钟项目介绍已准备

### 阶段2：AI深度分析
- [ ] 已通过AI深度分析技术方案
- [ ] 已获得AI的面试追问预测
- [ ] 已优化技术表达的专业性
- [ ] 已补充相关技术原理知识

### 阶段3：完整数据收集
- [ ] P0/P1/P2级数据已完整收集
- [ ] 数据来源和收集方法已明确
- [ ] 优化效果对比已量化

### 阶段4：技术方案深度
- [ ] 完整技术栈已梳理（包含版本和选择原因）
- [ ] 系统架构图已绘制（包含设计亮点）
- [ ] 3个核心技术亮点已深度分析
- [ ] 每个亮点都有实现细节和性能数据

### 阶段5：技术难点剖析
- [ ] 3个核心难点已深度剖析
- [ ] 每个难点都有完整的解决过程
- [ ] 面试追问点已充分准备
- [ ] 技术原理已深度理解

### 阶段6：面试表达优化
- [ ] 10个深度技术追问已准备标准答案
- [ ] 表达逻辑已优化（STAR法则）
- [ ] 技术深度已充分体现

---


## 📖 5个项目梳理使用指南

### 🎯 项目梳理优先级建议

**第一优先级（必须深度梳理）**：
1. **拼团项目（高并发）** - 最能体现技术深度
2. **服务治理（系统优化）** - 最能体现解决问题能力

**第二优先级（重点梳理）**：
3. **双token鉴权（架构安全）** - 体现架构设计能力

**第三优先级（快速梳理）**：
4. **风险账号验证（业务风控）** - 体现业务理解
5. **营销管理平台（高吞吐）** - 体现数据处理能力

### 📋 各项目梳理重点

#### 01-拼团项目-高并发
**重点关注阶段**：阶段4（技术方案）+ 阶段5（技术难点）
**核心亮点**：
- 分布式锁的实现和优化
- 缓存一致性解决方案
- 消息队列的可靠性保证
- 高并发下的数据一致性

**AI协作重点**：
```
请深度分析拼团项目的高并发解决方案：
1. 分布式锁的选型和实现细节
2. 缓存穿透、击穿、雪崩的解决方案
3. 消息队列的幂等性保证
4. 数据库分库分表策略
5. 系统限流和降级机制
```

#### 02-服务治理-优化
**重点关注阶段**：阶段3（数据收集）+ 阶段6（面试表达）
**核心亮点**：
- 性能监控和问题定位
- 系统优化的量化效果
- 稳定性保障机制
- 运维自动化实践

**AI协作重点**：
```
请深度分析服务治理项目的优化方案：
1. 性能瓶颈的定位方法和工具
2. 系统优化的具体措施和效果
3. 监控告警体系的设计
4. 故障处理和恢复机制
5. 可用性提升的技术手段
```

#### 03-双token鉴权-架构安全
**重点关注阶段**：阶段4（技术方案）+ 阶段6（面试表达）
**核心亮点**：
- OAuth2.0协议的深度理解
- JWT的安全性设计
- 认证授权架构设计
- 安全漏洞防护

**AI协作重点**：
```
请深度分析双token鉴权的安全架构：
1. OAuth2.0各种授权模式的适用场景
2. JWT的安全性考虑和最佳实践
3. token刷新机制的设计
4. 安全威胁的防护措施
5. 与传统session认证的对比
```

#### 04-风险账号验证-业务风控
**重点关注阶段**：阶段1（快速启动）+ 阶段2（AI分析）
**核心亮点**：
- 实时风控规则引擎
- 机器学习模型应用
- 业务风险识别
- 数据驱动决策

**AI协作重点**：
```
请深度分析风险账号验证的风控方案：
1. 风险识别规则的设计思路
2. 实时计算架构的技术选型
3. 机器学习模型的应用场景
4. 风控效果的评估指标
5. 业务价值的量化体现
```

#### 05-营销管理平台-高吞吐
**重点关注阶段**：阶段1（快速启动）+ 阶段3（数据收集）
**核心亮点**：
- 异步处理架构设计
- 任务调度系统实现
- 大数据量处理优化
- 系统扩展性设计

**AI协作重点**：
```
请深度分析营销管理平台的高吞吐方案：
1. 异步处理的架构设计
2. 任务调度的可靠性保证
3. 大数据量的处理策略
4. 系统性能的优化措施
5. 扩展性的技术实现
```

### ⏰ 时间分配
#### 10天时间分配
- **Day 1-2**：拼团项目（深度梳理）
- **Day 3-4**：服务治理项目（深度梳理）
- **Day 5-6**：双token鉴权项目（深度梳理）
- **Day 7**：风险账号验证项目（重点梳理）
- **Day 8**：营销管理平台项目（重点梳理）
- **Day 9-10**：整体优化 + 面试练习

### 🎯 成功标准

#### 超越标准（10天达成）
- [ ] 能介绍5个项目的技术亮点
- [ ] 能设计完整的技术架构方案
- [ ] 能应对各种深度技术追问

---

**🚀 立即开始：设置30分钟计时器，选择一个项目，从阶段1开始填写！**

**⚡ 效率提示：完成阶段1后立即请求AI Agent深度分析，基于分析结果继续后续阶段！**
