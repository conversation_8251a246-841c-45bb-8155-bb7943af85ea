我尝试概括本文标题但是失败了，这个标题很有水平

本文认为 STAR 最核心要回答的是”你是怎么选这条路的“。这个问题隐含的思维过程，可以通过 AI 来找到盲点，辅助训练，以及通过follow up的问题challenge自己知识技能上的不足。

> [!success] 不是把所有细节讲一遍，而是讲清楚：这是我选的路，我知道代价，我准备好了负责。

>[!cite] 为什么我们的故事没办法打动面试官？
我们都知道 STAR 模板：Situation、Task、Action、Result。大多数人准备行为面试，都是从这四个词开始的。
一边翻 JD，一边回想项目经历，有没有挑战？有没有成果？能不能套进STAR模版？ 
有时候我们甚至写了十几个故事，格式整齐、细节清晰、每个都像能得分。 
但真正坐到面试席上，话讲完了，对面的人点点头，说一句 “OK，那我们看下一个问题吧”。
你知道他没记住你。甚至连你讲的是什么问题，他可能都没印象。 
不是你不努力，是你讲的方式打不进他的信任系统。
我们以为是 STAR 模板出了问题，但其实不是。问题出在：我们只讲了事情的线，没讲出推理的弯。
很多人用 STAR，只是讲了“我参与过什么”，背景、任务、做了什么、取得了什么结果。但对面听的人，想知道的是：“你是怎么选这条路的？”
不是我们做过多少事，而是在碰到问题的时候，我们是怎么一步步想清楚、定下来、说服别人、承担后果。 
最近面试准备中，我找出一个更系统的练法：不止是让 AI 改润色，更要让AI帮我找到自己看不到的盲点，整理没有体现出来的思维过程，同时通过follow up的问题challenge自己知识技能上的不足。
我把自己写的故事丢进 AI 工具，加上简历和我心仪的工作的JD，首先请它从JD的角度出发润色我的故事，然后开始模拟面试官有可能追问的问题： 
你当时有哪些选项？为什么选这个？ 风险是什么？你评估了吗？ 有没有 tradeoff？有没有后备方案？ 
接着再让AI帮我做几件事： 把内容口语化，适合自然说出口。千万不要假设面试官能听懂你的所有术语，不管是业务术语还是技术术语。别用术语把面试官压垮了。 
提供一个三分钟版本，一个 30 秒版本 帮我写一句适合放进简历的结果提炼语句。这个操作真的太好用了，我的简历马上显得干货满满，充满诚意。 总结一个故事可以用来回答哪几个Behavior问题，回答的角度侧重点应该有哪些 
把故事更上一层楼，改进成应对bar raiser的面试官版本 （这个操作可以看到更高一层的视角和思考的方式） 
不是把所有细节讲一遍，而是讲清楚：这是我选的路，我知道代价，我准备好了负责。 
图里面有我准备的几个Prompts，大家也可以多总结你自己的提示词。 让它陪你一轮轮打磨。是的，起码打磨六到七轮。 
整个过程中，我们会更了解自己。而这比打动面试官更重要。

[为什么我们准备了十个 STAR 故事，却还是没办法打动面试官？](https://www.xiaohongshu.com/explore/6857a42f000000001202e43d?xsec_token=ABsGhYuByAN4TEUh4oKtiTWudsdyyDZViOSNbJke1piUk=&xsec_source=pc_user&source=web_user_page)

>我发现了。加上弯才能凸显你的 soft skill，你的 scope，你的 ownership。不然其实这些事谁都能干，但是你描述出的 story 是构成你的风味。
![[Pasted image 20250723113432.png]]


>这个案例对比让我联想到前司云服务迁移，当时 VP 复盘会就讲了一个力排众议的故事。当然他不需要说服任何人。

![[Pasted image 20250723113442.png]]


## 使用提示词打磨六到七轮（？）
宾语加了个问号，因为我不知道原文指的是打磨提示词还是简历/BQ

>不知道怎么写方法论的小朋友看到了吗？
>
>这就是一个典型的方法论应用
>
>用我的话来说（下述操作隐去 ai 主语）：
>1. 改简历
>2. 根据项目提问
>3. 预判项目中不真实的地方
>4. 将当前 STAR story 里 action 改写为 decision-making，体现你的决策过程
>5. 压力面
>6. 提高自己面试的岗位，看到更高层的视角和思考的方式

![[Pasted image 20250723114049.png]]

![[Pasted image 20250724155154.png]]

![[Pasted image 20250724155202.png]]

![[Pasted image 20250724155210.png]]