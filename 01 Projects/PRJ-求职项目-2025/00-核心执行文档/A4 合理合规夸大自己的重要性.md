> [能夸大自己意味着能夸大整个组的impact](https://www.xiaohongshu.com/explore/670b358c0000000024017189?xsec_token=AB2HE4z-V1r7qhSlgML0Syj_8BmP9E6J_ztqF_sRDgKqA=&xsec_source=pc_user&source=web_user_page)

合理合规夸大自己的重要性：
自己 - 我组 - 老板 - 老板升职加薪

怎么看出来一个人做到了合理合规夸大自己？
从简历和面试能看出来

**表面**：为什么升职的那个人看起来好水？

**深层**：技术在software engineer 里不稀缺，但是 how to show your work 稀缺。
> show your work
> write -> tell -> sell  a story
> 但是很难找到能做presentation, 能在一众matrix 里找到能证明自己工作的measurement，并且说服别人自己做的most重要、自己组做的most重要的人。

从奢侈品方面也是如此，卖的是背后的不是，而不只是产品。

>[!bug] ”诚信“一词，往negative 说，叫不知道 the big scope。
>因为不知道自己project在整体里的占比，不知道整体scope有多大，所以难以给出衡量。
>
>只能描述自己做过的。
>
>对没做过的、别人在做的、组里的目标、公司的目标、自己在做的东西对组的影响、对公司的影响没有认识，才只能只描述自己做过的。
>
>而没从公司角度考虑过。
>
>所以很难在简历中给出一个数字的measurement来证明自己的成绩。

---

你可能说我就一个螺丝钉 考虑那么多干啥。<font color='red'>拿到工作后 你考虑比螺丝钉还小都行 但是简历和面试时你就得心怀天下。</font> 比如你从小就想改变世界 长大后发现科技most能改变世界 又对某个领域特别感兴趣/受到过伤害 等personal story 才想终身奋斗其中。听起来特不着边际对吧 但是mei国人就吃这一套。你想想你申请master是不是也是这个路子。

同样两个人面试 技能使用都差不多 但是一个提到自己对整个行业的分析 一个没有。那你选谁。intern new grad 招进来是要培养的。技能能培养 C++原来不会 能教教他 给他一段时间去学 <font color='red'>但是自己对行业的热爱</font> 这是没办法培养的 更像是一个这个人根上的软实力，<font color='red'>这是需要你在简历和面试去尽情展现的。</font>

#### 山高了一看还有更山高
![[Pasted image 20250723111109.png]]