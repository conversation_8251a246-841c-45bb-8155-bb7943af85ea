## 如何用 AI 准备 behavior question
> 建议对象：new grade
> 
> 收集理由：bq 接近于我在准备的项目面的场景题。核心要点是在精力、资源和时间的限制下，展示对这三者的把控以及成功完成任务的能力，且由于岗位是程序员，最好在回答中加入技术上的权衡（trade off）分析。学习 up 主利用 ai 添加技术上的权衡内容是必要的。**要注意的是视频主要针对 new grade，而我准备的 senior，所以在scope 和 challenge 方面要检验**（类似：生成后可询问 “这个符合 ng intern 水平 不要假”）
> 
> 链接：[程序员bq一定往技术上靠](https://www.xiaohongshu.com/explore/67f33f2b000000001c03138e?xsec_token=AB-hgfKYFwXCbBDaYCyj8KgSqWObY7SO6vZ0geYv-LOio=&xsec_source=pc_collect)

#### 和 ai 交流的具体方法
>[!note] 问题 1：我是程序员，我做了一个 chrome extension 关于 todolist task management，链接用户的 google calendar，这个的系统设计是什么

ai 会给出一些 feature，虽然博主只是演示但这也很糙，基本上如果是复杂功能 ai 给出的回答可能和实际南辕北辙。

对我来说就是将相关文档和源码吐给 ai 让他分析，后面可能要解决多个项目同时分析的问题。

>[!note] 问题 2：涉及到哪些 trade off？

选择ai 回复的一个 trade off，继续提问。

>[!note] 问题 3：我是程序员，我在准备 bq 面试，根据这个（选取的 trade off）帮我写成一个我最 proud 的project，要用 star +learning

这个项目不是一顺百顺做下来的，肯定还有一些 trade out 分析

>[!note] 问题 4：我还想再加一些 challenge，帮我从精力、资源和时间入手

答案非常长，选择两项（但是是针对 new grad）