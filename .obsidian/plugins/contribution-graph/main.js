/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var KS=Object.create;var Fl=Object.defineProperty;var QS=Object.getOwnPropertyDescriptor;var JS=Object.getOwnPropertyNames;var XS=Object.getPrototypeOf,eC=Object.prototype.hasOwnProperty;var Rn=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),tC=(t,e)=>{for(var n in e)Fl(t,n,{get:e[n],enumerable:!0})},iy=(t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of JS(e))!eC.call(t,o)&&o!==n&&Fl(t,o,{get:()=>e[o],enumerable:!(r=QS(e,o))||r.enumerable});return t};var B=(t,e,n)=>(n=t!=null?KS(XS(t)):{},iy(e||!t||!t.__esModule?Fl(n,"default",{value:t,enumerable:!0}):n,t)),nC=t=>iy(Fl({},"__esModule",{value:!0}),t);var Gd=Rn(Ln=>{"use strict";Object.defineProperty(Ln,"__esModule",{value:!0});require("obsidian");var nr=class extends Error{},Ed=class extends nr{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}},wd=class extends nr{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}},Sd=class extends nr{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}},Do=class extends nr{},fu=class extends nr{constructor(e){super(`Invalid unit ${e}`)}},St=class extends nr{},In=class extends nr{constructor(){super("Zone is an abstract class")}},j="numeric",gn="short",It="long",du={year:j,month:j,day:j},o0={year:j,month:gn,day:j},bx={year:j,month:gn,day:j,weekday:gn},i0={year:j,month:It,day:j},s0={year:j,month:It,day:j,weekday:It},a0={hour:j,minute:j},l0={hour:j,minute:j,second:j},u0={hour:j,minute:j,second:j,timeZoneName:gn},c0={hour:j,minute:j,second:j,timeZoneName:It},f0={hour:j,minute:j,hourCycle:"h23"},d0={hour:j,minute:j,second:j,hourCycle:"h23"},m0={hour:j,minute:j,second:j,hourCycle:"h23",timeZoneName:gn},p0={hour:j,minute:j,second:j,hourCycle:"h23",timeZoneName:It},h0={year:j,month:j,day:j,hour:j,minute:j},y0={year:j,month:j,day:j,hour:j,minute:j,second:j},g0={year:j,month:gn,day:j,hour:j,minute:j},D0={year:j,month:gn,day:j,hour:j,minute:j,second:j},Rx={year:j,month:gn,day:j,weekday:gn,hour:j,minute:j},v0={year:j,month:It,day:j,hour:j,minute:j,timeZoneName:gn},E0={year:j,month:It,day:j,hour:j,minute:j,second:j,timeZoneName:gn},w0={year:j,month:It,day:j,weekday:It,hour:j,minute:j,timeZoneName:It},S0={year:j,month:It,day:j,weekday:It,hour:j,minute:j,second:j,timeZoneName:It},wo=class{get type(){throw new In}get name(){throw new In}get ianaName(){return this.name}get isUniversal(){throw new In}offsetName(e,n){throw new In}formatOffset(e,n){throw new In}offset(e){throw new In}equals(e){throw new In}get isValid(){throw new In}},md=null,gi=class extends wo{static get instance(){return md===null&&(md=new gi),md}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:n,locale:r}){return x0(e,n,r)}formatOffset(e,n){return na(this.offset(e),n)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return e.type==="system"}get isValid(){return!0}},uu={};function Nx(t){return uu[t]||(uu[t]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:t,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),uu[t]}var Mx={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function Ix(t,e){let n=t.format(e).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(n),[,o,i,s,a,l,u,f]=r;return[s,o,i,a,l,u,f]}function Ax(t,e){let n=t.formatToParts(e),r=[];for(let o=0;o<n.length;o++){let{type:i,value:s}=n[o],a=Mx[i];i==="era"?r[a]=s:ue(a)||(r[a]=parseInt(s,10))}return r}var ru={},Dn=class extends wo{static create(e){return ru[e]||(ru[e]=new Dn(e)),ru[e]}static resetCache(){ru={},uu={}}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch(n){return!1}}constructor(e){super(),this.zoneName=e,this.valid=Dn.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:n,locale:r}){return x0(e,n,r,this.name)}formatOffset(e,n){return na(this.offset(e),n)}offset(e){let n=new Date(e);if(isNaN(n))return NaN;let r=Nx(this.name),[o,i,s,a,l,u,f]=r.formatToParts?Ax(r,n):Ix(r,n);a==="BC"&&(o=-Math.abs(o)+1);let m=Eu({year:o,month:i,day:s,hour:l===24?0:l,minute:u,second:f,millisecond:0}),c=+n,y=c%1e3;return c-=y>=0?y:1e3+y,(m-c)/(60*1e3)}equals(e){return e.type==="iana"&&e.name===this.name}get isValid(){return this.valid}},_g={};function Lx(t,e={}){let n=JSON.stringify([t,e]),r=_g[n];return r||(r=new Intl.ListFormat(t,e),_g[n]=r),r}var Cd={};function xd(t,e={}){let n=JSON.stringify([t,e]),r=Cd[n];return r||(r=new Intl.DateTimeFormat(t,e),Cd[n]=r),r}var Td={};function Px(t,e={}){let n=JSON.stringify([t,e]),r=Td[n];return r||(r=new Intl.NumberFormat(t,e),Td[n]=r),r}var Fd={};function Bx(t,e={}){let{base:n,...r}=e,o=JSON.stringify([t,r]),i=Fd[o];return i||(i=new Intl.RelativeTimeFormat(t,e),Fd[o]=i),i}var ea=null;function Vx(){return ea||(ea=new Intl.DateTimeFormat().resolvedOptions().locale,ea)}function Wx(t){let e=t.indexOf("-x-");e!==-1&&(t=t.substring(0,e));let n=t.indexOf("-u-");if(n===-1)return[t];{let r,o;try{r=xd(t).resolvedOptions(),o=t}catch(a){let l=t.substring(0,n);r=xd(l).resolvedOptions(),o=l}let{numberingSystem:i,calendar:s}=r;return[o,i,s]}}function Hx(t,e,n){return(n||e)&&(t.includes("-u-")||(t+="-u"),n&&(t+=`-ca-${n}`),e&&(t+=`-nu-${e}`)),t}function zx(t){let e=[];for(let n=1;n<=12;n++){let r=Z.utc(2009,n,1);e.push(t(r))}return e}function Ux(t){let e=[];for(let n=1;n<=7;n++){let r=Z.utc(2016,11,13+n);e.push(t(r))}return e}function ou(t,e,n,r){let o=t.listingMode();return o==="error"?null:o==="en"?n(e):r(e)}function jx(t){return t.numberingSystem&&t.numberingSystem!=="latn"?!1:t.numberingSystem==="latn"||!t.locale||t.locale.startsWith("en")||new Intl.DateTimeFormat(t.intl).resolvedOptions().numberingSystem==="latn"}var _d=class{constructor(e,n,r){this.padTo=r.padTo||0,this.floor=r.floor||!1;let{padTo:o,floor:i,...s}=r;if(!n||Object.keys(s).length>0){let a={useGrouping:!1,...r};r.padTo>0&&(a.minimumIntegerDigits=r.padTo),this.inf=Px(e,a)}}format(e){if(this.inf){let n=this.floor?Math.floor(e):e;return this.inf.format(n)}else{let n=this.floor?Math.floor(e):Vd(e,3);return We(n,this.padTo)}}},kd=class{constructor(e,n,r){this.opts=r,this.originalZone=void 0;let o;if(this.opts.timeZone)this.dt=e;else if(e.zone.type==="fixed"){let s=-1*(e.offset/60),a=s>=0?`Etc/GMT+${s}`:`Etc/GMT${s}`;e.offset!==0&&Dn.create(a).valid?(o=a,this.dt=e):(o="UTC",this.dt=e.offset===0?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)}else e.zone.type==="system"?this.dt=e:e.zone.type==="iana"?(this.dt=e,o=e.zone.name):(o="UTC",this.dt=e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone);let i={...this.opts};i.timeZone=i.timeZone||o,this.dtf=xd(n,i)}format(){return this.originalZone?this.formatToParts().map(({value:e})=>e).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){let e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map(n=>{if(n.type==="timeZoneName"){let r=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...n,value:r}}else return n}):e}resolvedOptions(){return this.dtf.resolvedOptions()}},Od=class{constructor(e,n,r){this.opts={style:"long",...r},!n&&C0()&&(this.rtf=Bx(e,r))}format(e,n){return this.rtf?this.rtf.format(e,n):sT(n,e,this.opts.numeric,this.opts.style!=="long")}formatToParts(e,n){return this.rtf?this.rtf.formatToParts(e,n):[]}},ye=class{static fromOpts(e){return ye.create(e.locale,e.numberingSystem,e.outputCalendar,e.defaultToEN)}static create(e,n,r,o=!1){let i=e||Ie.defaultLocale,s=i||(o?"en-US":Vx()),a=n||Ie.defaultNumberingSystem,l=r||Ie.defaultOutputCalendar;return new ye(s,a,l,i)}static resetCache(){ea=null,Cd={},Td={},Fd={}}static fromObject({locale:e,numberingSystem:n,outputCalendar:r}={}){return ye.create(e,n,r)}constructor(e,n,r,o){let[i,s,a]=Wx(e);this.locale=i,this.numberingSystem=n||s||null,this.outputCalendar=r||a||null,this.intl=Hx(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=o,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=jx(this)),this.fastNumbersCached}listingMode(){let e=this.isEnglish(),n=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return e&&n?"en":"intl"}clone(e){return!e||Object.getOwnPropertyNames(e).length===0?this:ye.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,e.defaultToEN||!1)}redefaultToEN(e={}){return this.clone({...e,defaultToEN:!0})}redefaultToSystem(e={}){return this.clone({...e,defaultToEN:!1})}months(e,n=!1){return ou(this,e,_0,()=>{let r=n?{month:e,day:"numeric"}:{month:e},o=n?"format":"standalone";return this.monthsCache[o][e]||(this.monthsCache[o][e]=zx(i=>this.extract(i,r,"month"))),this.monthsCache[o][e]})}weekdays(e,n=!1){return ou(this,e,b0,()=>{let r=n?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},o=n?"format":"standalone";return this.weekdaysCache[o][e]||(this.weekdaysCache[o][e]=Ux(i=>this.extract(i,r,"weekday"))),this.weekdaysCache[o][e]})}meridiems(){return ou(this,void 0,()=>R0,()=>{if(!this.meridiemCache){let e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[Z.utc(2016,11,13,9),Z.utc(2016,11,13,19)].map(n=>this.extract(n,e,"dayperiod"))}return this.meridiemCache})}eras(e){return ou(this,e,N0,()=>{let n={era:e};return this.eraCache[e]||(this.eraCache[e]=[Z.utc(-40,1,1),Z.utc(2017,1,1)].map(r=>this.extract(r,n,"era"))),this.eraCache[e]})}extract(e,n,r){let o=this.dtFormatter(e,n),i=o.formatToParts(),s=i.find(a=>a.type.toLowerCase()===r);return s?s.value:null}numberFormatter(e={}){return new _d(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,n={}){return new kd(e,this.intl,n)}relFormatter(e={}){return new Od(this.intl,this.isEnglish(),e)}listFormatter(e={}){return Lx(this.intl,e)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}},pd=null,rt=class extends wo{static get utcInstance(){return pd===null&&(pd=new rt(0)),pd}static instance(e){return e===0?rt.utcInstance:new rt(e)}static parseSpecifier(e){if(e){let n=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(n)return new rt(wu(n[1],n[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${na(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${na(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,n){return na(this.fixed,n)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return e.type==="fixed"&&e.fixed===this.fixed}get isValid(){return!0}},bd=class extends wo{constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}};function Nr(t,e){if(ue(t)||t===null)return e;if(t instanceof wo)return t;if($x(t)){let n=t.toLowerCase();return n==="default"?e:n==="local"||n==="system"?gi.instance:n==="utc"||n==="gmt"?rt.utcInstance:rt.parseSpecifier(n)||Dn.create(t)}else return Eo(t)?rt.instance(t):typeof t=="object"&&"offset"in t&&typeof t.offset=="function"?t:new bd(t)}var kg=()=>Date.now(),Og="system",bg=null,Rg=null,Ng=null,Mg=60,Ig,Ie=class{static get now(){return kg}static set now(e){kg=e}static set defaultZone(e){Og=e}static get defaultZone(){return Nr(Og,gi.instance)}static get defaultLocale(){return bg}static set defaultLocale(e){bg=e}static get defaultNumberingSystem(){return Rg}static set defaultNumberingSystem(e){Rg=e}static get defaultOutputCalendar(){return Ng}static set defaultOutputCalendar(e){Ng=e}static get twoDigitCutoffYear(){return Mg}static set twoDigitCutoffYear(e){Mg=e%100}static get throwOnInvalid(){return Ig}static set throwOnInvalid(e){Ig=e}static resetCaches(){ye.resetCache(),Dn.resetCache()}};function ue(t){return typeof t=="undefined"}function Eo(t){return typeof t=="number"}function vu(t){return typeof t=="number"&&t%1===0}function $x(t){return typeof t=="string"}function Gx(t){return Object.prototype.toString.call(t)==="[object Date]"}function C0(){try{return typeof Intl!="undefined"&&!!Intl.RelativeTimeFormat}catch(t){return!1}}function Yx(t){return Array.isArray(t)?t:[t]}function Ag(t,e,n){if(t.length!==0)return t.reduce((r,o)=>{let i=[e(o),o];return r&&n(r[0],i[0])===r[0]?r:i},null)[1]}function Zx(t,e){return e.reduce((n,r)=>(n[r]=t[r],n),{})}function Di(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function tr(t,e,n){return vu(t)&&t>=e&&t<=n}function qx(t,e){return t-e*Math.floor(t/e)}function We(t,e=2){let n=t<0,r;return n?r="-"+(""+-t).padStart(e,"0"):r=(""+t).padStart(e,"0"),r}function Rr(t){if(!(ue(t)||t===null||t===""))return parseInt(t,10)}function yo(t){if(!(ue(t)||t===null||t===""))return parseFloat(t)}function Bd(t){if(!(ue(t)||t===null||t==="")){let e=parseFloat("0."+t)*1e3;return Math.floor(e)}}function Vd(t,e,n=!1){let r=10**e;return(n?Math.trunc:Math.round)(t*r)/r}function aa(t){return t%4===0&&(t%100!==0||t%400===0)}function ta(t){return aa(t)?366:365}function mu(t,e){let n=qx(e-1,12)+1,r=t+(e-n)/12;return n===2?aa(r)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function Eu(t){let e=Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,t.second,t.millisecond);return t.year<100&&t.year>=0&&(e=new Date(e),e.setUTCFullYear(t.year,t.month-1,t.day)),+e}function pu(t){let e=(t+Math.floor(t/4)-Math.floor(t/100)+Math.floor(t/400))%7,n=t-1,r=(n+Math.floor(n/4)-Math.floor(n/100)+Math.floor(n/400))%7;return e===4||r===3?53:52}function Rd(t){return t>99?t:t>Ie.twoDigitCutoffYear?1900+t:2e3+t}function x0(t,e,n,r=null){let o=new Date(t),i={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(i.timeZone=r);let s={timeZoneName:e,...i},a=new Intl.DateTimeFormat(n,s).formatToParts(o).find(l=>l.type.toLowerCase()==="timezonename");return a?a.value:null}function wu(t,e){let n=parseInt(t,10);Number.isNaN(n)&&(n=0);let r=parseInt(e,10)||0,o=n<0||Object.is(n,-0)?-r:r;return n*60+o}function T0(t){let e=Number(t);if(typeof t=="boolean"||t===""||Number.isNaN(e))throw new St(`Invalid unit value ${t}`);return e}function hu(t,e){let n={};for(let r in t)if(Di(t,r)){let o=t[r];if(o==null)continue;n[e(r)]=T0(o)}return n}function na(t,e){let n=Math.trunc(Math.abs(t/60)),r=Math.trunc(Math.abs(t%60)),o=t>=0?"+":"-";switch(e){case"short":return`${o}${We(n,2)}:${We(r,2)}`;case"narrow":return`${o}${n}${r>0?`:${r}`:""}`;case"techie":return`${o}${We(n,2)}${We(r,2)}`;default:throw new RangeError(`Value format ${e} is out of range for property format`)}}function Su(t){return Zx(t,["hour","minute","second","millisecond"])}var Kx=["January","February","March","April","May","June","July","August","September","October","November","December"],F0=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],Qx=["J","F","M","A","M","J","J","A","S","O","N","D"];function _0(t){switch(t){case"narrow":return[...Qx];case"short":return[...F0];case"long":return[...Kx];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}var k0=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],O0=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],Jx=["M","T","W","T","F","S","S"];function b0(t){switch(t){case"narrow":return[...Jx];case"short":return[...O0];case"long":return[...k0];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}var R0=["AM","PM"],Xx=["Before Christ","Anno Domini"],eT=["BC","AD"],tT=["B","A"];function N0(t){switch(t){case"narrow":return[...tT];case"short":return[...eT];case"long":return[...Xx];default:return null}}function nT(t){return R0[t.hour<12?0:1]}function rT(t,e){return b0(e)[t.weekday-1]}function oT(t,e){return _0(e)[t.month-1]}function iT(t,e){return N0(e)[t.year<0?0:1]}function sT(t,e,n="always",r=!1){let o={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},i=["hours","minutes","seconds"].indexOf(t)===-1;if(n==="auto"&&i){let d=t==="days";switch(e){case 1:return d?"tomorrow":`next ${o[t][0]}`;case-1:return d?"yesterday":`last ${o[t][0]}`;case 0:return d?"today":`this ${o[t][0]}`}}let s=Object.is(e,-0)||e<0,a=Math.abs(e),l=a===1,u=o[t],f=r?l?u[1]:u[2]||u[1]:l?o[t][0]:t;return s?`${a} ${f} ago`:`in ${a} ${f}`}function Lg(t,e){let n="";for(let r of t)r.literal?n+=r.val:n+=e(r.val);return n}var aT={D:du,DD:o0,DDD:i0,DDDD:s0,t:a0,tt:l0,ttt:u0,tttt:c0,T:f0,TT:d0,TTT:m0,TTTT:p0,f:h0,ff:g0,fff:v0,ffff:w0,F:y0,FF:D0,FFF:E0,FFFF:S0},Qe=class{static create(e,n={}){return new Qe(e,n)}static parseFormat(e){let n=null,r="",o=!1,i=[];for(let s=0;s<e.length;s++){let a=e.charAt(s);a==="'"?(r.length>0&&i.push({literal:o||/^\s+$/.test(r),val:r}),n=null,r="",o=!o):o||a===n?r+=a:(r.length>0&&i.push({literal:/^\s+$/.test(r),val:r}),r=a,n=a)}return r.length>0&&i.push({literal:o||/^\s+$/.test(r),val:r}),i}static macroTokenToFormatOpts(e){return aT[e]}constructor(e,n){this.opts=n,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,n){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,{...this.opts,...n}).format()}dtFormatter(e,n={}){return this.loc.dtFormatter(e,{...this.opts,...n})}formatDateTime(e,n){return this.dtFormatter(e,n).format()}formatDateTimeParts(e,n){return this.dtFormatter(e,n).formatToParts()}formatInterval(e,n){return this.dtFormatter(e.start,n).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())}resolvedOptions(e,n){return this.dtFormatter(e,n).resolvedOptions()}num(e,n=0){if(this.opts.forceSimple)return We(e,n);let r={...this.opts};return n>0&&(r.padTo=n),this.loc.numberFormatter(r).format(e)}formatDateTimeFromString(e,n){let r=this.loc.listingMode()==="en",o=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",i=(c,y)=>this.loc.extract(e,c,y),s=c=>e.isOffsetFixed&&e.offset===0&&c.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,c.format):"",a=()=>r?nT(e):i({hour:"numeric",hourCycle:"h12"},"dayperiod"),l=(c,y)=>r?oT(e,c):i(y?{month:c}:{month:c,day:"numeric"},"month"),u=(c,y)=>r?rT(e,c):i(y?{weekday:c}:{weekday:c,month:"long",day:"numeric"},"weekday"),f=c=>{let y=Qe.macroTokenToFormatOpts(c);return y?this.formatWithSystemDefault(e,y):c},d=c=>r?iT(e,c):i({era:c},"era"),m=c=>{switch(c){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12===0?12:e.hour%12);case"hh":return this.num(e.hour%12===0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return s({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return s({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return s({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return a();case"d":return o?i({day:"numeric"},"day"):this.num(e.day);case"dd":return o?i({day:"2-digit"},"day"):this.num(e.day,2);case"c":return this.num(e.weekday);case"ccc":return u("short",!0);case"cccc":return u("long",!0);case"ccccc":return u("narrow",!0);case"E":return this.num(e.weekday);case"EEE":return u("short",!1);case"EEEE":return u("long",!1);case"EEEEE":return u("narrow",!1);case"L":return o?i({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return o?i({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return l("short",!0);case"LLLL":return l("long",!0);case"LLLLL":return l("narrow",!0);case"M":return o?i({month:"numeric"},"month"):this.num(e.month);case"MM":return o?i({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return l("short",!1);case"MMMM":return l("long",!1);case"MMMMM":return l("narrow",!1);case"y":return o?i({year:"numeric"},"year"):this.num(e.year);case"yy":return o?i({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return o?i({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return o?i({year:"numeric"},"year"):this.num(e.year,6);case"G":return d("short");case"GG":return d("long");case"GGGGG":return d("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return f(c)}};return Lg(Qe.parseFormat(n),m)}formatDurationFromString(e,n){let r=l=>{switch(l[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},o=l=>u=>{let f=r(u);return f?this.num(l.get(f),u.length):u},i=Qe.parseFormat(n),s=i.reduce((l,{literal:u,val:f})=>u?l:l.concat(f),[]),a=e.shiftTo(...s.map(r).filter(l=>l));return Lg(i,o(a))}},Mt=class{constructor(e,n){this.reason=e,this.explanation=n}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}},M0=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function vi(...t){let e=t.reduce((n,r)=>n+r.source,"");return RegExp(`^${e}$`)}function Ei(...t){return e=>t.reduce(([n,r,o],i)=>{let[s,a,l]=i(e,o);return[{...n,...s},a||r,l]},[{},null,1]).slice(0,2)}function wi(t,...e){if(t==null)return[null,null];for(let[n,r]of e){let o=n.exec(t);if(o)return r(o)}return[null,null]}function I0(...t){return(e,n)=>{let r={},o;for(o=0;o<t.length;o++)r[t[o]]=Rr(e[n+o]);return[r,null,n+o]}}var A0=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,lT=`(?:${A0.source}?(?:\\[(${M0.source})\\])?)?`,Wd=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,L0=RegExp(`${Wd.source}${lT}`),Hd=RegExp(`(?:T${L0.source})?`),uT=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,cT=/(\d{4})-?W(\d\d)(?:-?(\d))?/,fT=/(\d{4})-?(\d{3})/,dT=I0("weekYear","weekNumber","weekDay"),mT=I0("year","ordinal"),pT=/(\d{4})-(\d\d)-(\d\d)/,P0=RegExp(`${Wd.source} ?(?:${A0.source}|(${M0.source}))?`),hT=RegExp(`(?: ${P0.source})?`);function yi(t,e,n){let r=t[e];return ue(r)?n:Rr(r)}function yT(t,e){return[{year:yi(t,e),month:yi(t,e+1,1),day:yi(t,e+2,1)},null,e+3]}function Si(t,e){return[{hours:yi(t,e,0),minutes:yi(t,e+1,0),seconds:yi(t,e+2,0),milliseconds:Bd(t[e+3])},null,e+4]}function la(t,e){let n=!t[e]&&!t[e+1],r=wu(t[e+1],t[e+2]),o=n?null:rt.instance(r);return[{},o,e+3]}function ua(t,e){let n=t[e]?Dn.create(t[e]):null;return[{},n,e+1]}var gT=RegExp(`^T?${Wd.source}$`),DT=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function vT(t){let[e,n,r,o,i,s,a,l,u]=t,f=e[0]==="-",d=l&&l[0]==="-",m=(c,y=!1)=>c!==void 0&&(y||c&&f)?-c:c;return[{years:m(yo(n)),months:m(yo(r)),weeks:m(yo(o)),days:m(yo(i)),hours:m(yo(s)),minutes:m(yo(a)),seconds:m(yo(l),l==="-0"),milliseconds:m(Bd(u),d)}]}var ET={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function zd(t,e,n,r,o,i,s){let a={year:e.length===2?Rd(Rr(e)):Rr(e),month:F0.indexOf(n)+1,day:Rr(r),hour:Rr(o),minute:Rr(i)};return s&&(a.second=Rr(s)),t&&(a.weekday=t.length>3?k0.indexOf(t)+1:O0.indexOf(t)+1),a}var wT=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function ST(t){let[,e,n,r,o,i,s,a,l,u,f,d]=t,m=zd(e,o,r,n,i,s,a),c;return l?c=ET[l]:u?c=0:c=wu(f,d),[m,new rt(c)]}function CT(t){return t.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}var xT=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,TT=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,FT=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function Pg(t){let[,e,n,r,o,i,s,a]=t;return[zd(e,o,r,n,i,s,a),rt.utcInstance]}function _T(t){let[,e,n,r,o,i,s,a]=t;return[zd(e,a,n,r,o,i,s),rt.utcInstance]}var kT=vi(uT,Hd),OT=vi(cT,Hd),bT=vi(fT,Hd),RT=vi(L0),B0=Ei(yT,Si,la,ua),NT=Ei(dT,Si,la,ua),MT=Ei(mT,Si,la,ua),IT=Ei(Si,la,ua);function AT(t){return wi(t,[kT,B0],[OT,NT],[bT,MT],[RT,IT])}function LT(t){return wi(CT(t),[wT,ST])}function PT(t){return wi(t,[xT,Pg],[TT,Pg],[FT,_T])}function BT(t){return wi(t,[DT,vT])}var VT=Ei(Si);function WT(t){return wi(t,[gT,VT])}var HT=vi(pT,hT),zT=vi(P0),UT=Ei(Si,la,ua);function jT(t){return wi(t,[HT,B0],[zT,UT])}var Bg="Invalid Duration",V0={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},$T={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...V0},Yt=146097/400,mi=146097/4800,GT={years:{quarters:4,months:12,weeks:Yt/7,days:Yt,hours:Yt*24,minutes:Yt*24*60,seconds:Yt*24*60*60,milliseconds:Yt*24*60*60*1e3},quarters:{months:3,weeks:Yt/28,days:Yt/4,hours:Yt*24/4,minutes:Yt*24*60/4,seconds:Yt*24*60*60/4,milliseconds:Yt*24*60*60*1e3/4},months:{weeks:mi/7,days:mi,hours:mi*24,minutes:mi*24*60,seconds:mi*24*60*60,milliseconds:mi*24*60*60*1e3},...V0},vo=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],YT=vo.slice(0).reverse();function br(t,e,n=!1){let r={values:n?e.values:{...t.values,...e.values||{}},loc:t.loc.clone(e.loc),conversionAccuracy:e.conversionAccuracy||t.conversionAccuracy,matrix:e.matrix||t.matrix};return new G(r)}function W0(t,e){var r;let n=(r=e.milliseconds)!=null?r:0;for(let o of YT.slice(1))e[o]&&(n+=e[o]*t[o].milliseconds);return n}function Vg(t,e){let n=W0(t,e)<0?-1:1;vo.reduceRight((r,o)=>{if(ue(e[o]))return r;if(r){let i=e[r]*n,s=t[o][r],a=Math.floor(i/s);e[o]+=a*n,e[r]-=a*s*n}return o},null),vo.reduce((r,o)=>{if(ue(e[o]))return r;if(r){let i=e[r]%1;e[r]-=i,e[o]+=i*t[r][o]}return o},null)}function ZT(t){let e={};for(let[n,r]of Object.entries(t))r!==0&&(e[n]=r);return e}var G=class{constructor(e){let n=e.conversionAccuracy==="longterm"||!1,r=n?GT:$T;e.matrix&&(r=e.matrix),this.values=e.values,this.loc=e.loc||ye.create(),this.conversionAccuracy=n?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=r,this.isLuxonDuration=!0}static fromMillis(e,n){return G.fromObject({milliseconds:e},n)}static fromObject(e,n={}){if(e==null||typeof e!="object")throw new St(`Duration.fromObject: argument expected to be an object, got ${e===null?"null":typeof e}`);return new G({values:hu(e,G.normalizeUnit),loc:ye.fromObject(n),conversionAccuracy:n.conversionAccuracy,matrix:n.matrix})}static fromDurationLike(e){if(Eo(e))return G.fromMillis(e);if(G.isDuration(e))return e;if(typeof e=="object")return G.fromObject(e);throw new St(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,n){let[r]=BT(e);return r?G.fromObject(r,n):G.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,n){let[r]=WT(e);return r?G.fromObject(r,n):G.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,n=null){if(!e)throw new St("need to specify a reason the Duration is invalid");let r=e instanceof Mt?e:new Mt(e,n);if(Ie.throwOnInvalid)throw new Sd(r);return new G({invalid:r})}static normalizeUnit(e){let n={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e&&e.toLowerCase()];if(!n)throw new fu(e);return n}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,n={}){let r={...n,floor:n.round!==!1&&n.floor!==!1};return this.isValid?Qe.create(this.loc,r).formatDurationFromString(this,e):Bg}toHuman(e={}){if(!this.isValid)return Bg;let n=vo.map(r=>{let o=this.values[r];return ue(o)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...e,unit:r.slice(0,-1)}).format(o)}).filter(r=>r);return this.loc.listFormatter({type:"conjunction",style:e.listStyle||"narrow",...e}).format(n)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let e="P";return this.years!==0&&(e+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(e+=this.months+this.quarters*3+"M"),this.weeks!==0&&(e+=this.weeks+"W"),this.days!==0&&(e+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(e+="T"),this.hours!==0&&(e+=this.hours+"H"),this.minutes!==0&&(e+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(e+=Vd(this.seconds+this.milliseconds/1e3,3)+"S"),e==="P"&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;let n=this.toMillis();return n<0||n>=864e5?null:(e={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...e,includeOffset:!1},Z.fromMillis(n,{zone:"UTC"}).toISOTime(e))}toJSON(){return this.toISO()}toString(){return this.toISO()}toMillis(){return this.isValid?W0(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;let n=G.fromDurationLike(e),r={};for(let o of vo)(Di(n.values,o)||Di(this.values,o))&&(r[o]=n.get(o)+this.get(o));return br(this,{values:r},!0)}minus(e){if(!this.isValid)return this;let n=G.fromDurationLike(e);return this.plus(n.negate())}mapUnits(e){if(!this.isValid)return this;let n={};for(let r of Object.keys(this.values))n[r]=T0(e(this.values[r],r));return br(this,{values:n},!0)}get(e){return this[G.normalizeUnit(e)]}set(e){if(!this.isValid)return this;let n={...this.values,...hu(e,G.normalizeUnit)};return br(this,{values:n})}reconfigure({locale:e,numberingSystem:n,conversionAccuracy:r,matrix:o}={}){let s={loc:this.loc.clone({locale:e,numberingSystem:n}),matrix:o,conversionAccuracy:r};return br(this,s)}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;let e=this.toObject();return Vg(this.matrix,e),br(this,{values:e},!0)}rescale(){if(!this.isValid)return this;let e=ZT(this.normalize().shiftToAll().toObject());return br(this,{values:e},!0)}shiftTo(...e){if(!this.isValid)return this;if(e.length===0)return this;e=e.map(s=>G.normalizeUnit(s));let n={},r={},o=this.toObject(),i;for(let s of vo)if(e.indexOf(s)>=0){i=s;let a=0;for(let u in r)a+=this.matrix[u][s]*r[u],r[u]=0;Eo(o[s])&&(a+=o[s]);let l=Math.trunc(a);n[s]=l,r[s]=(a*1e3-l*1e3)/1e3}else Eo(o[s])&&(r[s]=o[s]);for(let s in r)r[s]!==0&&(n[i]+=s===i?r[s]:r[s]/this.matrix[i][s]);return Vg(this.matrix,n),br(this,{values:n},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;let e={};for(let n of Object.keys(this.values))e[n]=this.values[n]===0?0:-this.values[n];return br(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid||!this.loc.equals(e.loc))return!1;function n(r,o){return r===void 0||r===0?o===void 0||o===0:r===o}for(let r of vo)if(!n(this.values[r],e.values[r]))return!1;return!0}},pi="Invalid Interval";function qT(t,e){return!t||!t.isValid?Ce.invalid("missing or invalid start"):!e||!e.isValid?Ce.invalid("missing or invalid end"):e<t?Ce.invalid("end before start",`The end of an interval must be after its start, but you had start=${t.toISO()} and end=${e.toISO()}`):null}var Ce=class{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,n=null){if(!e)throw new St("need to specify a reason the Interval is invalid");let r=e instanceof Mt?e:new Mt(e,n);if(Ie.throwOnInvalid)throw new wd(r);return new Ce({invalid:r})}static fromDateTimes(e,n){let r=Js(e),o=Js(n),i=qT(r,o);return i==null?new Ce({start:r,end:o}):i}static after(e,n){let r=G.fromDurationLike(n),o=Js(e);return Ce.fromDateTimes(o,o.plus(r))}static before(e,n){let r=G.fromDurationLike(n),o=Js(e);return Ce.fromDateTimes(o.minus(r),o)}static fromISO(e,n){let[r,o]=(e||"").split("/",2);if(r&&o){let i,s;try{i=Z.fromISO(r,n),s=i.isValid}catch(u){s=!1}let a,l;try{a=Z.fromISO(o,n),l=a.isValid}catch(u){l=!1}if(s&&l)return Ce.fromDateTimes(i,a);if(s){let u=G.fromISO(o,n);if(u.isValid)return Ce.after(i,u)}else if(l){let u=G.fromISO(r,n);if(u.isValid)return Ce.before(a,u)}}return Ce.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds"){if(!this.isValid)return NaN;let n=this.start.startOf(e),r=this.end.startOf(e);return Math.floor(r.diff(n,e).get(e))+(r.valueOf()!==this.end.valueOf())}hasSame(e){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,e):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return this.isValid?this.s>e:!1}isBefore(e){return this.isValid?this.e<=e:!1}contains(e){return this.isValid?this.s<=e&&this.e>e:!1}set({start:e,end:n}={}){return this.isValid?Ce.fromDateTimes(e||this.s,n||this.e):this}splitAt(...e){if(!this.isValid)return[];let n=e.map(Js).filter(s=>this.contains(s)).sort(),r=[],{s:o}=this,i=0;for(;o<this.e;){let s=n[i]||this.e,a=+s>+this.e?this.e:s;r.push(Ce.fromDateTimes(o,a)),o=a,i+=1}return r}splitBy(e){let n=G.fromDurationLike(e);if(!this.isValid||!n.isValid||n.as("milliseconds")===0)return[];let{s:r}=this,o=1,i,s=[];for(;r<this.e;){let a=this.start.plus(n.mapUnits(l=>l*o));i=+a>+this.e?this.e:a,s.push(Ce.fromDateTimes(r,i)),r=i,o+=1}return s}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return this.isValid?+this.e==+e.s:!1}abutsEnd(e){return this.isValid?+e.e==+this.s:!1}engulfs(e){return this.isValid?this.s<=e.s&&this.e>=e.e:!1}equals(e){return!this.isValid||!e.isValid?!1:this.s.equals(e.s)&&this.e.equals(e.e)}intersection(e){if(!this.isValid)return this;let n=this.s>e.s?this.s:e.s,r=this.e<e.e?this.e:e.e;return n>=r?null:Ce.fromDateTimes(n,r)}union(e){if(!this.isValid)return this;let n=this.s<e.s?this.s:e.s,r=this.e>e.e?this.e:e.e;return Ce.fromDateTimes(n,r)}static merge(e){let[n,r]=e.sort((o,i)=>o.s-i.s).reduce(([o,i],s)=>i?i.overlaps(s)||i.abutsStart(s)?[o,i.union(s)]:[o.concat([i]),s]:[o,s],[[],null]);return r&&n.push(r),n}static xor(e){let n=null,r=0,o=[],i=e.map(l=>[{time:l.s,type:"s"},{time:l.e,type:"e"}]),s=Array.prototype.concat(...i),a=s.sort((l,u)=>l.time-u.time);for(let l of a)r+=l.type==="s"?1:-1,r===1?n=l.time:(n&&+n!=+l.time&&o.push(Ce.fromDateTimes(n,l.time)),n=null);return Ce.merge(o)}difference(...e){return Ce.xor([this].concat(e)).map(n=>this.intersection(n)).filter(n=>n&&!n.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} \u2013 ${this.e.toISO()})`:pi}toLocaleString(e=du,n={}){return this.isValid?Qe.create(this.s.loc.clone(n),e).formatInterval(this):pi}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:pi}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:pi}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:pi}toFormat(e,{separator:n=" \u2013 "}={}){return this.isValid?`${this.s.toFormat(e)}${n}${this.e.toFormat(e)}`:pi}toDuration(e,n){return this.isValid?this.e.diff(this.s,e,n):G.invalid(this.invalidReason)}mapEndpoints(e){return Ce.fromDateTimes(e(this.s),e(this.e))}},hi=class{static hasDST(e=Ie.defaultZone){let n=Z.now().setZone(e).set({month:12});return!e.isUniversal&&n.offset!==n.set({month:6}).offset}static isValidIANAZone(e){return Dn.isValidZone(e)}static normalizeZone(e){return Nr(e,Ie.defaultZone)}static months(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null,outputCalendar:i="gregory"}={}){return(o||ye.create(n,r,i)).months(e)}static monthsFormat(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null,outputCalendar:i="gregory"}={}){return(o||ye.create(n,r,i)).months(e,!0)}static weekdays(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null}={}){return(o||ye.create(n,r,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null}={}){return(o||ye.create(n,r,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return ye.create(e).meridiems()}static eras(e="short",{locale:n=null}={}){return ye.create(n,null,"gregory").eras(e)}static features(){return{relative:C0()}}};function Wg(t,e){let n=o=>o.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=n(e)-n(t);return Math.floor(G.fromMillis(r).as("days"))}function KT(t,e,n){let r=[["years",(l,u)=>u.year-l.year],["quarters",(l,u)=>u.quarter-l.quarter+(u.year-l.year)*4],["months",(l,u)=>u.month-l.month+(u.year-l.year)*12],["weeks",(l,u)=>{let f=Wg(l,u);return(f-f%7)/7}],["days",Wg]],o={},i=t,s,a;for(let[l,u]of r)n.indexOf(l)>=0&&(s=l,o[l]=u(t,e),a=i.plus(o),a>e?(o[l]--,t=i.plus(o),t>e&&(a=t,o[l]--,t=i.plus(o))):t=a);return[t,o,a,s]}function QT(t,e,n,r){let[o,i,s,a]=KT(t,e,n),l=e-o,u=n.filter(d=>["hours","minutes","seconds","milliseconds"].indexOf(d)>=0);u.length===0&&(s<e&&(s=o.plus({[a]:1})),s!==o&&(i[a]=(i[a]||0)+l/(s-o)));let f=G.fromObject(i,r);return u.length>0?G.fromMillis(l,r).shiftTo(...u).plus(f):f}var Ud={arab:"[\u0660-\u0669]",arabext:"[\u06F0-\u06F9]",bali:"[\u1B50-\u1B59]",beng:"[\u09E6-\u09EF]",deva:"[\u0966-\u096F]",fullwide:"[\uFF10-\uFF19]",gujr:"[\u0AE6-\u0AEF]",hanidec:"[\u3007|\u4E00|\u4E8C|\u4E09|\u56DB|\u4E94|\u516D|\u4E03|\u516B|\u4E5D]",khmr:"[\u17E0-\u17E9]",knda:"[\u0CE6-\u0CEF]",laoo:"[\u0ED0-\u0ED9]",limb:"[\u1946-\u194F]",mlym:"[\u0D66-\u0D6F]",mong:"[\u1810-\u1819]",mymr:"[\u1040-\u1049]",orya:"[\u0B66-\u0B6F]",tamldec:"[\u0BE6-\u0BEF]",telu:"[\u0C66-\u0C6F]",thai:"[\u0E50-\u0E59]",tibt:"[\u0F20-\u0F29]",latn:"\\d"},Hg={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},JT=Ud.hanidec.replace(/[\[|\]]/g,"").split("");function XT(t){let e=parseInt(t,10);if(isNaN(e)){e="";for(let n=0;n<t.length;n++){let r=t.charCodeAt(n);if(t[n].search(Ud.hanidec)!==-1)e+=JT.indexOf(t[n]);else for(let o in Hg){let[i,s]=Hg[o];r>=i&&r<=s&&(e+=r-i)}}return parseInt(e,10)}else return e}function pn({numberingSystem:t},e=""){return new RegExp(`${Ud[t||"latn"]}${e}`)}var eF="missing Intl.DateTimeFormat.formatToParts support";function me(t,e=n=>n){return{regex:t,deser:([n])=>e(XT(n))}}var tF=String.fromCharCode(160),H0=`[ ${tF}]`,z0=new RegExp(H0,"g");function nF(t){return t.replace(/\./g,"\\.?").replace(z0,H0)}function zg(t){return t.replace(/\./g,"").replace(z0," ").toLowerCase()}function hn(t,e){return t===null?null:{regex:RegExp(t.map(nF).join("|")),deser:([n])=>t.findIndex(r=>zg(n)===zg(r))+e}}function Ug(t,e){return{regex:t,deser:([,n,r])=>wu(n,r),groups:e}}function iu(t){return{regex:t,deser:([e])=>e}}function rF(t){return t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function oF(t,e){let n=pn(e),r=pn(e,"{2}"),o=pn(e,"{3}"),i=pn(e,"{4}"),s=pn(e,"{6}"),a=pn(e,"{1,2}"),l=pn(e,"{1,3}"),u=pn(e,"{1,6}"),f=pn(e,"{1,9}"),d=pn(e,"{2,4}"),m=pn(e,"{4,6}"),c=S=>({regex:RegExp(rF(S.val)),deser:([p])=>p,literal:!0}),v=(S=>{if(t.literal)return c(S);switch(S.val){case"G":return hn(e.eras("short"),0);case"GG":return hn(e.eras("long"),0);case"y":return me(u);case"yy":return me(d,Rd);case"yyyy":return me(i);case"yyyyy":return me(m);case"yyyyyy":return me(s);case"M":return me(a);case"MM":return me(r);case"MMM":return hn(e.months("short",!0),1);case"MMMM":return hn(e.months("long",!0),1);case"L":return me(a);case"LL":return me(r);case"LLL":return hn(e.months("short",!1),1);case"LLLL":return hn(e.months("long",!1),1);case"d":return me(a);case"dd":return me(r);case"o":return me(l);case"ooo":return me(o);case"HH":return me(r);case"H":return me(a);case"hh":return me(r);case"h":return me(a);case"mm":return me(r);case"m":return me(a);case"q":return me(a);case"qq":return me(r);case"s":return me(a);case"ss":return me(r);case"S":return me(l);case"SSS":return me(o);case"u":return iu(f);case"uu":return iu(a);case"uuu":return me(n);case"a":return hn(e.meridiems(),0);case"kkkk":return me(i);case"kk":return me(d,Rd);case"W":return me(a);case"WW":return me(r);case"E":case"c":return me(n);case"EEE":return hn(e.weekdays("short",!1),1);case"EEEE":return hn(e.weekdays("long",!1),1);case"ccc":return hn(e.weekdays("short",!0),1);case"cccc":return hn(e.weekdays("long",!0),1);case"Z":case"ZZ":return Ug(new RegExp(`([+-]${a.source})(?::(${r.source}))?`),2);case"ZZZ":return Ug(new RegExp(`([+-]${a.source})(${r.source})?`),2);case"z":return iu(/[a-z_+-/]{1,256}?/i);case" ":return iu(/[^\S\n\r]/);default:return c(S)}})(t)||{invalidReason:eF};return v.token=t,v}var iF={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function sF(t,e,n){let{type:r,value:o}=t;if(r==="literal"){let l=/^\s+$/.test(o);return{literal:!l,val:l?" ":o}}let i=e[r],s=r;r==="hour"&&(e.hour12!=null?s=e.hour12?"hour12":"hour24":e.hourCycle!=null?e.hourCycle==="h11"||e.hourCycle==="h12"?s="hour12":s="hour24":s=n.hour12?"hour12":"hour24");let a=iF[s];if(typeof a=="object"&&(a=a[i]),a)return{literal:!1,val:a}}function aF(t){return[`^${t.map(n=>n.regex).reduce((n,r)=>`${n}(${r.source})`,"")}$`,t]}function lF(t,e,n){let r=t.match(e);if(r){let o={},i=1;for(let s in n)if(Di(n,s)){let a=n[s],l=a.groups?a.groups+1:1;!a.literal&&a.token&&(o[a.token.val[0]]=a.deser(r.slice(i,i+l))),i+=l}return[r,o]}else return[r,{}]}function uF(t){let e=i=>{switch(i){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}},n=null,r;return ue(t.z)||(n=Dn.create(t.z)),ue(t.Z)||(n||(n=new rt(t.Z)),r=t.Z),ue(t.q)||(t.M=(t.q-1)*3+1),ue(t.h)||(t.h<12&&t.a===1?t.h+=12:t.h===12&&t.a===0&&(t.h=0)),t.G===0&&t.y&&(t.y=-t.y),ue(t.u)||(t.S=Bd(t.u)),[Object.keys(t).reduce((i,s)=>{let a=e(s);return a&&(i[a]=t[s]),i},{}),n,r]}var hd=null;function cF(){return hd||(hd=Z.fromMillis(1555555555555)),hd}function fF(t,e){if(t.literal)return t;let n=Qe.macroTokenToFormatOpts(t.val),r=$0(n,e);return r==null||r.includes(void 0)?t:r}function U0(t,e){return Array.prototype.concat(...t.map(n=>fF(n,e)))}function j0(t,e,n){let r=U0(Qe.parseFormat(n),t),o=r.map(s=>oF(s,t)),i=o.find(s=>s.invalidReason);if(i)return{input:e,tokens:r,invalidReason:i.invalidReason};{let[s,a]=aF(o),l=RegExp(s,"i"),[u,f]=lF(e,l,a),[d,m,c]=f?uF(f):[null,null,void 0];if(Di(f,"a")&&Di(f,"H"))throw new Do("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:r,regex:l,rawMatches:u,matches:f,result:d,zone:m,specificOffset:c}}}function dF(t,e,n){let{result:r,zone:o,specificOffset:i,invalidReason:s}=j0(t,e,n);return[r,o,i,s]}function $0(t,e){if(!t)return null;let r=Qe.create(e,t).dtFormatter(cF()),o=r.formatToParts(),i=r.resolvedOptions();return o.map(s=>sF(s,t,i))}var G0=[0,31,59,90,120,151,181,212,243,273,304,334],Y0=[0,31,60,91,121,152,182,213,244,274,305,335];function Zt(t,e){return new Mt("unit out of range",`you specified ${e} (of type ${typeof e}) as a ${t}, which is invalid`)}function Z0(t,e,n){let r=new Date(Date.UTC(t,e-1,n));t<100&&t>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);let o=r.getUTCDay();return o===0?7:o}function q0(t,e,n){return n+(aa(t)?Y0:G0)[e-1]}function K0(t,e){let n=aa(t)?Y0:G0,r=n.findIndex(i=>i<e),o=e-n[r];return{month:r+1,day:o}}function Nd(t){let{year:e,month:n,day:r}=t,o=q0(e,n,r),i=Z0(e,n,r),s=Math.floor((o-i+10)/7),a;return s<1?(a=e-1,s=pu(a)):s>pu(e)?(a=e+1,s=1):a=e,{weekYear:a,weekNumber:s,weekday:i,...Su(t)}}function jg(t){let{weekYear:e,weekNumber:n,weekday:r}=t,o=Z0(e,1,4),i=ta(e),s=n*7+r-o-3,a;s<1?(a=e-1,s+=ta(a)):s>i?(a=e+1,s-=ta(e)):a=e;let{month:l,day:u}=K0(a,s);return{year:a,month:l,day:u,...Su(t)}}function yd(t){let{year:e,month:n,day:r}=t,o=q0(e,n,r);return{year:e,ordinal:o,...Su(t)}}function $g(t){let{year:e,ordinal:n}=t,{month:r,day:o}=K0(e,n);return{year:e,month:r,day:o,...Su(t)}}function mF(t){let e=vu(t.weekYear),n=tr(t.weekNumber,1,pu(t.weekYear)),r=tr(t.weekday,1,7);return e?n?r?!1:Zt("weekday",t.weekday):Zt("week",t.week):Zt("weekYear",t.weekYear)}function pF(t){let e=vu(t.year),n=tr(t.ordinal,1,ta(t.year));return e?n?!1:Zt("ordinal",t.ordinal):Zt("year",t.year)}function Q0(t){let e=vu(t.year),n=tr(t.month,1,12),r=tr(t.day,1,mu(t.year,t.month));return e?n?r?!1:Zt("day",t.day):Zt("month",t.month):Zt("year",t.year)}function J0(t){let{hour:e,minute:n,second:r,millisecond:o}=t,i=tr(e,0,23)||e===24&&n===0&&r===0&&o===0,s=tr(n,0,59),a=tr(r,0,59),l=tr(o,0,999);return i?s?a?l?!1:Zt("millisecond",o):Zt("second",r):Zt("minute",n):Zt("hour",e)}var gd="Invalid DateTime",Gg=864e13;function su(t){return new Mt("unsupported zone",`the zone "${t.name}" is not supported`)}function Dd(t){return t.weekData===null&&(t.weekData=Nd(t.c)),t.weekData}function go(t,e){let n={ts:t.ts,zone:t.zone,c:t.c,o:t.o,loc:t.loc,invalid:t.invalid};return new Z({...n,...e,old:n})}function X0(t,e,n){let r=t-e*60*1e3,o=n.offset(r);if(e===o)return[r,e];r-=(o-e)*60*1e3;let i=n.offset(r);return o===i?[r,o]:[t-Math.min(o,i)*60*1e3,Math.max(o,i)]}function au(t,e){t+=e*60*1e3;let n=new Date(t);return{year:n.getUTCFullYear(),month:n.getUTCMonth()+1,day:n.getUTCDate(),hour:n.getUTCHours(),minute:n.getUTCMinutes(),second:n.getUTCSeconds(),millisecond:n.getUTCMilliseconds()}}function cu(t,e,n){return X0(Eu(t),e,n)}function Yg(t,e){let n=t.o,r=t.c.year+Math.trunc(e.years),o=t.c.month+Math.trunc(e.months)+Math.trunc(e.quarters)*3,i={...t.c,year:r,month:o,day:Math.min(t.c.day,mu(r,o))+Math.trunc(e.days)+Math.trunc(e.weeks)*7},s=G.fromObject({years:e.years-Math.trunc(e.years),quarters:e.quarters-Math.trunc(e.quarters),months:e.months-Math.trunc(e.months),weeks:e.weeks-Math.trunc(e.weeks),days:e.days-Math.trunc(e.days),hours:e.hours,minutes:e.minutes,seconds:e.seconds,milliseconds:e.milliseconds}).as("milliseconds"),a=Eu(i),[l,u]=X0(a,n,t.zone);return s!==0&&(l+=s,u=t.zone.offset(l)),{ts:l,o:u}}function Qs(t,e,n,r,o,i){let{setZone:s,zone:a}=n;if(t&&Object.keys(t).length!==0||e){let l=e||a,u=Z.fromObject(t,{...n,zone:l,specificOffset:i});return s?u:u.setZone(a)}else return Z.invalid(new Mt("unparsable",`the input "${o}" can't be parsed as ${r}`))}function lu(t,e,n=!0){return t.isValid?Qe.create(ye.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(t,e):null}function vd(t,e){let n=t.c.year>9999||t.c.year<0,r="";return n&&t.c.year>=0&&(r+="+"),r+=We(t.c.year,n?6:4),e?(r+="-",r+=We(t.c.month),r+="-",r+=We(t.c.day)):(r+=We(t.c.month),r+=We(t.c.day)),r}function Zg(t,e,n,r,o,i){let s=We(t.c.hour);return e?(s+=":",s+=We(t.c.minute),(t.c.millisecond!==0||t.c.second!==0||!n)&&(s+=":")):s+=We(t.c.minute),(t.c.millisecond!==0||t.c.second!==0||!n)&&(s+=We(t.c.second),(t.c.millisecond!==0||!r)&&(s+=".",s+=We(t.c.millisecond,3))),o&&(t.isOffsetFixed&&t.offset===0&&!i?s+="Z":t.o<0?(s+="-",s+=We(Math.trunc(-t.o/60)),s+=":",s+=We(Math.trunc(-t.o%60))):(s+="+",s+=We(Math.trunc(t.o/60)),s+=":",s+=We(Math.trunc(t.o%60)))),i&&(s+="["+t.zone.ianaName+"]"),s}var eD={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},hF={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},yF={ordinal:1,hour:0,minute:0,second:0,millisecond:0},tD=["year","month","day","hour","minute","second","millisecond"],gF=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],DF=["year","ordinal","hour","minute","second","millisecond"];function qg(t){let e={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[t.toLowerCase()];if(!e)throw new fu(t);return e}function Kg(t,e){let n=Nr(e.zone,Ie.defaultZone),r=ye.fromObject(e),o=Ie.now(),i,s;if(ue(t.year))i=o;else{for(let u of tD)ue(t[u])&&(t[u]=eD[u]);let a=Q0(t)||J0(t);if(a)return Z.invalid(a);let l=n.offset(o);[i,s]=cu(t,l,n)}return new Z({ts:i,zone:n,loc:r,o:s})}function Qg(t,e,n){let r=ue(n.round)?!0:n.round,o=(s,a)=>(s=Vd(s,r||n.calendary?0:2,!0),e.loc.clone(n).relFormatter(n).format(s,a)),i=s=>n.calendary?e.hasSame(t,s)?0:e.startOf(s).diff(t.startOf(s),s).get(s):e.diff(t,s).get(s);if(n.unit)return o(i(n.unit),n.unit);for(let s of n.units){let a=i(s);if(Math.abs(a)>=1)return o(a,s)}return o(t>e?-0:0,n.units[n.units.length-1])}function Jg(t){let e={},n;return t.length>0&&typeof t[t.length-1]=="object"?(e=t[t.length-1],n=Array.from(t).slice(0,t.length-1)):n=Array.from(t),[e,n]}var Z=class{constructor(e){let n=e.zone||Ie.defaultZone,r=e.invalid||(Number.isNaN(e.ts)?new Mt("invalid input"):null)||(n.isValid?null:su(n));this.ts=ue(e.ts)?Ie.now():e.ts;let o=null,i=null;if(!r)if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(n))[o,i]=[e.old.c,e.old.o];else{let a=n.offset(this.ts);o=au(this.ts,a),r=Number.isNaN(o.year)?new Mt("invalid input"):null,o=r?null:o,i=r?null:a}this._zone=n,this.loc=e.loc||ye.create(),this.invalid=r,this.weekData=null,this.c=o,this.o=i,this.isLuxonDateTime=!0}static now(){return new Z({})}static local(){let[e,n]=Jg(arguments),[r,o,i,s,a,l,u]=n;return Kg({year:r,month:o,day:i,hour:s,minute:a,second:l,millisecond:u},e)}static utc(){let[e,n]=Jg(arguments),[r,o,i,s,a,l,u]=n;return e.zone=rt.utcInstance,Kg({year:r,month:o,day:i,hour:s,minute:a,second:l,millisecond:u},e)}static fromJSDate(e,n={}){let r=Gx(e)?e.valueOf():NaN;if(Number.isNaN(r))return Z.invalid("invalid input");let o=Nr(n.zone,Ie.defaultZone);return o.isValid?new Z({ts:r,zone:o,loc:ye.fromObject(n)}):Z.invalid(su(o))}static fromMillis(e,n={}){if(Eo(e))return e<-Gg||e>Gg?Z.invalid("Timestamp out of range"):new Z({ts:e,zone:Nr(n.zone,Ie.defaultZone),loc:ye.fromObject(n)});throw new St(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,n={}){if(Eo(e))return new Z({ts:e*1e3,zone:Nr(n.zone,Ie.defaultZone),loc:ye.fromObject(n)});throw new St("fromSeconds requires a numerical input")}static fromObject(e,n={}){e=e||{};let r=Nr(n.zone,Ie.defaultZone);if(!r.isValid)return Z.invalid(su(r));let o=Ie.now(),i=ue(n.specificOffset)?r.offset(o):n.specificOffset,s=hu(e,qg),a=!ue(s.ordinal),l=!ue(s.year),u=!ue(s.month)||!ue(s.day),f=l||u,d=s.weekYear||s.weekNumber,m=ye.fromObject(n);if((f||a)&&d)throw new Do("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(u&&a)throw new Do("Can't mix ordinal dates with month/day");let c=d||s.weekday&&!f,y,v,S=au(o,i);c?(y=gF,v=hF,S=Nd(S)):a?(y=DF,v=yF,S=yd(S)):(y=tD,v=eD);let p=!1;for(let k of y){let P=s[k];ue(P)?p?s[k]=v[k]:s[k]=S[k]:p=!0}let h=c?mF(s):a?pF(s):Q0(s),E=h||J0(s);if(E)return Z.invalid(E);let D=c?jg(s):a?$g(s):s,[x,O]=cu(D,i,r),F=new Z({ts:x,zone:r,o:O,loc:m});return s.weekday&&f&&e.weekday!==F.weekday?Z.invalid("mismatched weekday",`you can't specify both a weekday of ${s.weekday} and a date of ${F.toISO()}`):F}static fromISO(e,n={}){let[r,o]=AT(e);return Qs(r,o,n,"ISO 8601",e)}static fromRFC2822(e,n={}){let[r,o]=LT(e);return Qs(r,o,n,"RFC 2822",e)}static fromHTTP(e,n={}){let[r,o]=PT(e);return Qs(r,o,n,"HTTP",n)}static fromFormat(e,n,r={}){if(ue(e)||ue(n))throw new St("fromFormat requires an input string and a format");let{locale:o=null,numberingSystem:i=null}=r,s=ye.fromOpts({locale:o,numberingSystem:i,defaultToEN:!0}),[a,l,u,f]=dF(s,e,n);return f?Z.invalid(f):Qs(a,l,r,`format ${n}`,e,u)}static fromString(e,n,r={}){return Z.fromFormat(e,n,r)}static fromSQL(e,n={}){let[r,o]=jT(e);return Qs(r,o,n,"SQL",e)}static invalid(e,n=null){if(!e)throw new St("need to specify a reason the DateTime is invalid");let r=e instanceof Mt?e:new Mt(e,n);if(Ie.throwOnInvalid)throw new Ed(r);return new Z({invalid:r})}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,n={}){let r=$0(e,ye.fromObject(n));return r?r.map(o=>o?o.val:null).join(""):null}static expandFormat(e,n={}){return U0(Qe.parseFormat(e),ye.fromObject(n)).map(o=>o.val).join("")}get(e){return this[e]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?Dd(this).weekYear:NaN}get weekNumber(){return this.isValid?Dd(this).weekNumber:NaN}get weekday(){return this.isValid?Dd(this).weekday:NaN}get ordinal(){return this.isValid?yd(this.c).ordinal:NaN}get monthShort(){return this.isValid?hi.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?hi.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?hi.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?hi.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];let e=864e5,n=6e4,r=Eu(this.c),o=this.zone.offset(r-e),i=this.zone.offset(r+e),s=this.zone.offset(r-o*n),a=this.zone.offset(r-i*n);if(s===a)return[this];let l=r-s*n,u=r-a*n,f=au(l,s),d=au(u,a);return f.hour===d.hour&&f.minute===d.minute&&f.second===d.second&&f.millisecond===d.millisecond?[go(this,{ts:l}),go(this,{ts:u})]:[this]}get isInLeapYear(){return aa(this.year)}get daysInMonth(){return mu(this.year,this.month)}get daysInYear(){return this.isValid?ta(this.year):NaN}get weeksInWeekYear(){return this.isValid?pu(this.weekYear):NaN}resolvedLocaleOptions(e={}){let{locale:n,numberingSystem:r,calendar:o}=Qe.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:n,numberingSystem:r,outputCalendar:o}}toUTC(e=0,n={}){return this.setZone(rt.instance(e),n)}toLocal(){return this.setZone(Ie.defaultZone)}setZone(e,{keepLocalTime:n=!1,keepCalendarTime:r=!1}={}){if(e=Nr(e,Ie.defaultZone),e.equals(this.zone))return this;if(e.isValid){let o=this.ts;if(n||r){let i=e.offset(this.ts),s=this.toObject();[o]=cu(s,i,e)}return go(this,{ts:o,zone:e})}else return Z.invalid(su(e))}reconfigure({locale:e,numberingSystem:n,outputCalendar:r}={}){let o=this.loc.clone({locale:e,numberingSystem:n,outputCalendar:r});return go(this,{loc:o})}setLocale(e){return this.reconfigure({locale:e})}set(e){if(!this.isValid)return this;let n=hu(e,qg),r=!ue(n.weekYear)||!ue(n.weekNumber)||!ue(n.weekday),o=!ue(n.ordinal),i=!ue(n.year),s=!ue(n.month)||!ue(n.day),a=i||s,l=n.weekYear||n.weekNumber;if((a||o)&&l)throw new Do("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(s&&o)throw new Do("Can't mix ordinal dates with month/day");let u;r?u=jg({...Nd(this.c),...n}):ue(n.ordinal)?(u={...this.toObject(),...n},ue(n.day)&&(u.day=Math.min(mu(u.year,u.month),u.day))):u=$g({...yd(this.c),...n});let[f,d]=cu(u,this.o,this.zone);return go(this,{ts:f,o:d})}plus(e){if(!this.isValid)return this;let n=G.fromDurationLike(e);return go(this,Yg(this,n))}minus(e){if(!this.isValid)return this;let n=G.fromDurationLike(e).negate();return go(this,Yg(this,n))}startOf(e){if(!this.isValid)return this;let n={},r=G.normalizeUnit(e);switch(r){case"years":n.month=1;case"quarters":case"months":n.day=1;case"weeks":case"days":n.hour=0;case"hours":n.minute=0;case"minutes":n.second=0;case"seconds":n.millisecond=0;break}if(r==="weeks"&&(n.weekday=1),r==="quarters"){let o=Math.ceil(this.month/3);n.month=(o-1)*3+1}return this.set(n)}endOf(e){return this.isValid?this.plus({[e]:1}).startOf(e).minus(1):this}toFormat(e,n={}){return this.isValid?Qe.create(this.loc.redefaultToEN(n)).formatDateTimeFromString(this,e):gd}toLocaleString(e=du,n={}){return this.isValid?Qe.create(this.loc.clone(n),e).formatDateTime(this):gd}toLocaleParts(e={}){return this.isValid?Qe.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:n=!1,suppressMilliseconds:r=!1,includeOffset:o=!0,extendedZone:i=!1}={}){if(!this.isValid)return null;let s=e==="extended",a=vd(this,s);return a+="T",a+=Zg(this,s,n,r,o,i),a}toISODate({format:e="extended"}={}){return this.isValid?vd(this,e==="extended"):null}toISOWeekDate(){return lu(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:n=!1,includeOffset:r=!0,includePrefix:o=!1,extendedZone:i=!1,format:s="extended"}={}){return this.isValid?(o?"T":"")+Zg(this,s==="extended",n,e,r,i):null}toRFC2822(){return lu(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return lu(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?vd(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:n=!1,includeOffsetSpace:r=!0}={}){let o="HH:mm:ss.SSS";return(n||e)&&(r&&(o+=" "),n?o+="z":e&&(o+="ZZ")),lu(this,o,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():gd}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};let n={...this.c};return e.includeConfig&&(n.outputCalendar=this.outputCalendar,n.numberingSystem=this.loc.numberingSystem,n.locale=this.loc.locale),n}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,n="milliseconds",r={}){if(!this.isValid||!e.isValid)return G.invalid("created by diffing an invalid DateTime");let o={locale:this.locale,numberingSystem:this.numberingSystem,...r},i=Yx(n).map(G.normalizeUnit),s=e.valueOf()>this.valueOf(),a=s?this:e,l=s?e:this,u=QT(a,l,i,o);return s?u.negate():u}diffNow(e="milliseconds",n={}){return this.diff(Z.now(),e,n)}until(e){return this.isValid?Ce.fromDateTimes(this,e):this}hasSame(e,n){if(!this.isValid)return!1;let r=e.valueOf(),o=this.setZone(e.zone,{keepLocalTime:!0});return o.startOf(n)<=r&&r<=o.endOf(n)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;let n=e.base||Z.fromObject({},{zone:this.zone}),r=e.padding?this<n?-e.padding:e.padding:0,o=["years","months","days","hours","minutes","seconds"],i=e.unit;return Array.isArray(e.unit)&&(o=e.unit,i=void 0),Qg(n,this.plus(r),{...e,numeric:"always",units:o,unit:i})}toRelativeCalendar(e={}){return this.isValid?Qg(e.base||Z.fromObject({},{zone:this.zone}),this,{...e,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...e){if(!e.every(Z.isDateTime))throw new St("min requires all arguments be DateTimes");return Ag(e,n=>n.valueOf(),Math.min)}static max(...e){if(!e.every(Z.isDateTime))throw new St("max requires all arguments be DateTimes");return Ag(e,n=>n.valueOf(),Math.max)}static fromFormatExplain(e,n,r={}){let{locale:o=null,numberingSystem:i=null}=r,s=ye.fromOpts({locale:o,numberingSystem:i,defaultToEN:!0});return j0(s,e,n)}static fromStringExplain(e,n,r={}){return Z.fromFormatExplain(e,n,r)}static get DATE_SHORT(){return du}static get DATE_MED(){return o0}static get DATE_MED_WITH_WEEKDAY(){return bx}static get DATE_FULL(){return i0}static get DATE_HUGE(){return s0}static get TIME_SIMPLE(){return a0}static get TIME_WITH_SECONDS(){return l0}static get TIME_WITH_SHORT_OFFSET(){return u0}static get TIME_WITH_LONG_OFFSET(){return c0}static get TIME_24_SIMPLE(){return f0}static get TIME_24_WITH_SECONDS(){return d0}static get TIME_24_WITH_SHORT_OFFSET(){return m0}static get TIME_24_WITH_LONG_OFFSET(){return p0}static get DATETIME_SHORT(){return h0}static get DATETIME_SHORT_WITH_SECONDS(){return y0}static get DATETIME_MED(){return g0}static get DATETIME_MED_WITH_SECONDS(){return D0}static get DATETIME_MED_WITH_WEEKDAY(){return Rx}static get DATETIME_FULL(){return v0}static get DATETIME_FULL_WITH_SECONDS(){return E0}static get DATETIME_HUGE(){return w0}static get DATETIME_HUGE_WITH_SECONDS(){return S0}};function Js(t){if(Z.isDateTime(t))return t;if(t&&t.valueOf&&Eo(t.valueOf()))return Z.fromJSDate(t);if(t&&typeof t=="object")return Z.fromObject(t);throw new St(`Unknown datetime argument: ${t}, of type ${typeof t}`)}var jd={renderNullAs:"\\-",taskCompletionTracking:!1,taskCompletionUseEmojiShorthand:!1,taskCompletionText:"completion",taskCompletionDateFormat:"yyyy-MM-dd",recursiveSubTaskCompletion:!1,warnOnEmptyResult:!0,refreshEnabled:!0,refreshInterval:2500,defaultDateFormat:"MMMM dd, yyyy",defaultDateTimeFormat:"h:mm a - MMMM dd, yyyy",maxRecursiveRenderDepth:4,tableIdColumnName:"File",tableGroupColumnName:"Group",showResultCount:!0},vF={allowHtml:!0};({...jd,...vF});var ra=class{constructor(e){this.value=e,this.successful=!0}map(e){return new ra(e(this.value))}flatMap(e){return e(this.value)}mapErr(e){return this}bimap(e,n){return this.map(e)}orElse(e){return this.value}cast(){return this}orElseThrow(e){return this.value}},oa=class{constructor(e){this.error=e,this.successful=!1}map(e){return this}flatMap(e){return this}mapErr(e){return new oa(e(this.error))}bimap(e,n){return this.mapErr(n)}orElse(e){return e}cast(){return this}orElseThrow(e){throw e?new Error(e(this.error)):new Error(""+this.error)}},yu;(function(t){function e(i){return new ra(i)}t.success=e;function n(i){return new oa(i)}t.failure=n;function r(i,s,a){return i.successful?s.successful?a(i.value,s.value):n(s.error):n(i.error)}t.flatMap2=r;function o(i,s,a){return r(i,s,(l,u)=>e(a(l,u)))}t.map2=o})(yu||(yu={}));var EF=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{},gu={exports:{}};gu.exports;(function(t,e){(function(n,r){t.exports=r()})(typeof self!="undefined"?self:EF,function(){return function(n){var r={};function o(i){if(r[i])return r[i].exports;var s=r[i]={i,l:!1,exports:{}};return n[i].call(s.exports,s,s.exports,o),s.l=!0,s.exports}return o.m=n,o.c=r,o.d=function(i,s,a){o.o(i,s)||Object.defineProperty(i,s,{configurable:!1,enumerable:!0,get:a})},o.r=function(i){Object.defineProperty(i,"__esModule",{value:!0})},o.n=function(i){var s=i&&i.__esModule?function(){return i.default}:function(){return i};return o.d(s,"a",s),s},o.o=function(i,s){return Object.prototype.hasOwnProperty.call(i,s)},o.p="",o(o.s=0)}([function(n,r,o){function i(g){if(!(this instanceof i))return new i(g);this._=g}var s=i.prototype;function a(g,C){for(var T=0;T<g;T++)C(T)}function l(g,C,T){return function(N,L){a(L.length,function(H){N(L[H],H,L)})}(function(N,L,H){C=g(C,N,L,H)},T),C}function u(g,C){return l(function(T,N,L,H){return T.concat([g(N,L,H)])},[],C)}function f(g,C){var T={v:0,buf:C};return a(g,function(){var N;T={v:T.v<<1|(N=T.buf,N[0]>>7),buf:function(L){var H=l(function(z,ne,ge,mt){return z.concat(ge===mt.length-1?Buffer.from([ne,0]).readUInt16BE(0):mt.readUInt16BE(ge))},[],L);return Buffer.from(u(function(z){return(z<<1&65535)>>8},H))}(T.buf)}}),T}function d(){return typeof Buffer!="undefined"}function m(){if(!d())throw new Error("Buffer global does not exist; please use webpack if you need to parse Buffers in the browser.")}function c(g){m();var C=l(function(H,z){return H+z},0,g);if(C%8!=0)throw new Error("The bits ["+g.join(", ")+"] add up to "+C+" which is not an even number of bytes; the total should be divisible by 8");var T,N=C/8,L=(T=function(H){return H>48},l(function(H,z){return H||(T(z)?z:H)},null,g));if(L)throw new Error(L+" bit range requested exceeds 48 bit (6 byte) Number max.");return new i(function(H,z){var ne=N+z;return ne>H.length?k(z,N.toString()+" bytes"):F(ne,l(function(ge,mt){var nt=f(mt,ge.buf);return{coll:ge.coll.concat(nt.v),buf:nt.buf}},{coll:[],buf:H.slice(z,ne)},g).coll)})}function y(g,C){return new i(function(T,N){return m(),N+C>T.length?k(N,C+" bytes for "+g):F(N+C,T.slice(N,N+C))})}function v(g,C){if(typeof(T=C)!="number"||Math.floor(T)!==T||C<0||C>6)throw new Error(g+" requires integer length in range [0, 6].");var T}function S(g){return v("uintBE",g),y("uintBE("+g+")",g).map(function(C){return C.readUIntBE(0,g)})}function p(g){return v("uintLE",g),y("uintLE("+g+")",g).map(function(C){return C.readUIntLE(0,g)})}function h(g){return v("intBE",g),y("intBE("+g+")",g).map(function(C){return C.readIntBE(0,g)})}function E(g){return v("intLE",g),y("intLE("+g+")",g).map(function(C){return C.readIntLE(0,g)})}function D(g){return g instanceof i}function x(g){return{}.toString.call(g)==="[object Array]"}function O(g){return d()&&Buffer.isBuffer(g)}function F(g,C){return{status:!0,index:g,value:C,furthest:-1,expected:[]}}function k(g,C){return x(C)||(C=[C]),{status:!1,index:-1,value:null,furthest:g,expected:C}}function P(g,C){if(!C||g.furthest>C.furthest)return g;var T=g.furthest===C.furthest?function(N,L){if(function(){if(i._supportsSet!==void 0)return i._supportsSet;var Qn=typeof Set!="undefined";return i._supportsSet=Qn,Qn}()&&Array.from){for(var H=new Set(N),z=0;z<L.length;z++)H.add(L[z]);var ne=Array.from(H);return ne.sort(),ne}for(var ge={},mt=0;mt<N.length;mt++)ge[N[mt]]=!0;for(var nt=0;nt<L.length;nt++)ge[L[nt]]=!0;var Kn=[];for(var bt in ge)({}).hasOwnProperty.call(ge,bt)&&Kn.push(bt);return Kn.sort(),Kn}(g.expected,C.expected):C.expected;return{status:g.status,index:g.index,value:g.value,furthest:C.furthest,expected:T}}var M={};function ee(g,C){if(O(g))return{offset:C,line:-1,column:-1};g in M||(M[g]={});for(var T=M[g],N=0,L=0,H=0,z=C;z>=0;){if(z in T){N=T[z].line,H===0&&(H=T[z].lineStart);break}(g.charAt(z)===`
`||g.charAt(z)==="\r"&&g.charAt(z+1)!==`
`)&&(L++,H===0&&(H=z+1)),z--}var ne=N+L,ge=C-H;return T[C]={line:ne,lineStart:H},{offset:C,line:ne+1,column:ge+1}}function oe(g){if(!D(g))throw new Error("not a parser: "+g)}function b(g,C){return typeof g=="string"?g.charAt(C):g[C]}function _(g){if(typeof g!="number")throw new Error("not a number: "+g)}function R(g){if(typeof g!="function")throw new Error("not a function: "+g)}function W(g){if(typeof g!="string")throw new Error("not a string: "+g)}var Q=2,K=3,Ue=8,Sr=5*Ue,bn=4*Ue,io="  ";function tt(g,C){return new Array(C+1).join(g)}function Te(g,C,T){var N=C-g.length;return N<=0?g:tt(T,N)+g}function an(g,C,T,N){return{from:g-C>0?g-C:0,to:g+T>N?N:g+T}}function so(g,C){var T,N,L,H,z,ne=C.index,ge=ne.offset,mt=1;if(ge===g.length)return"Got the end of the input";if(O(g)){var nt=ge-ge%Ue,Kn=ge-nt,bt=an(nt,Sr,bn+Ue,g.length),Qn=u(function(je){return u(function(Go){return Te(Go.toString(16),2,"0")},je)},function(je,Go){var Yo=je.length,uo=[],Zo=0;if(Yo<=Go)return[je.slice()];for(var qo=0;qo<Yo;qo++)uo[Zo]||uo.push([]),uo[Zo].push(je[qo]),(qo+1)%Go==0&&Zo++;return uo}(g.slice(bt.from,bt.to).toJSON().data,Ue));H=function(je){return je.from===0&&je.to===1?{from:je.from,to:je.to}:{from:je.from/Ue,to:Math.floor(je.to/Ue)}}(bt),N=nt/Ue,T=3*Kn,Kn>=4&&(T+=1),mt=2,L=u(function(je){return je.length<=4?je.join(" "):je.slice(0,4).join(" ")+"  "+je.slice(4).join(" ")},Qn),(z=(8*(H.to>0?H.to-1:H.to)).toString(16).length)<2&&(z=2)}else{var $o=g.split(/\r\n|[\n\r\u2028\u2029]/);T=ne.column-1,N=ne.line-1,H=an(N,Q,K,$o.length),L=$o.slice(H.from,H.to),z=H.to.toString().length}var qS=N-H.from;return O(g)&&(z=(8*(H.to>0?H.to-1:H.to)).toString(16).length)<2&&(z=2),l(function(je,Go,Yo){var uo,Zo=Yo===qS,qo=Zo?"> ":io;return uo=O(g)?Te((8*(H.from+Yo)).toString(16),z,"0"):Te((H.from+Yo+1).toString(),z," "),[].concat(je,[qo+uo+" | "+Go],Zo?[io+tt(" ",z)+" | "+Te("",T," ")+tt("^",mt)]:[])},[],L).join(`
`)}function wf(g,C){return[`
`,"-- PARSING FAILED "+tt("-",50),`

`,so(g,C),`

`,(T=C.expected,T.length===1?`Expected:

`+T[0]:`Expected one of the following: 

`+T.join(", ")),`
`].join("");var T}function Qh(g){return g.flags!==void 0?g.flags:[g.global?"g":"",g.ignoreCase?"i":"",g.multiline?"m":"",g.unicode?"u":"",g.sticky?"y":""].join("")}function wl(){for(var g=[].slice.call(arguments),C=g.length,T=0;T<C;T+=1)oe(g[T]);return i(function(N,L){for(var H,z=new Array(C),ne=0;ne<C;ne+=1){if(!(H=P(g[ne]._(N,L),H)).status)return H;z[ne]=H.value,L=H.index}return P(F(L,z),H)})}function ao(){var g=[].slice.call(arguments);if(g.length===0)throw new Error("seqMap needs at least one argument");var C=g.pop();return R(C),wl.apply(null,g).map(function(T){return C.apply(null,T)})}function Sl(){var g=[].slice.call(arguments),C=g.length;if(C===0)return Cl("zero alternates");for(var T=0;T<C;T+=1)oe(g[T]);return i(function(N,L){for(var H,z=0;z<g.length;z+=1)if((H=P(g[z]._(N,L),H)).status)return H;return H})}function Jh(g,C){return Sf(g,C).or(lo([]))}function Sf(g,C){return oe(g),oe(C),ao(g,C.then(g).many(),function(T,N){return[T].concat(N)})}function Ds(g){W(g);var C="'"+g+"'";return i(function(T,N){var L=N+g.length,H=T.slice(N,L);return H===g?F(L,H):k(N,C)})}function qn(g,C){(function(L){if(!(L instanceof RegExp))throw new Error("not a regexp: "+L);for(var H=Qh(L),z=0;z<H.length;z++){var ne=H.charAt(z);if(ne!=="i"&&ne!=="m"&&ne!=="u"&&ne!=="s")throw new Error('unsupported regexp flag "'+ne+'": '+L)}})(g),arguments.length>=2?_(C):C=0;var T=function(L){return RegExp("^(?:"+L.source+")",Qh(L))}(g),N=""+g;return i(function(L,H){var z=T.exec(L.slice(H));if(z){if(0<=C&&C<=z.length){var ne=z[0],ge=z[C];return F(H+ne.length,ge)}return k(H,"valid match group (0 to "+z.length+") in "+N)}return k(H,N)})}function lo(g){return i(function(C,T){return F(T,g)})}function Cl(g){return i(function(C,T){return k(T,g)})}function xl(g){if(D(g))return i(function(C,T){var N=g._(C,T);return N.index=T,N.value="",N});if(typeof g=="string")return xl(Ds(g));if(g instanceof RegExp)return xl(qn(g));throw new Error("not a string, regexp, or parser: "+g)}function Xh(g){return oe(g),i(function(C,T){var N=g._(C,T),L=C.slice(T,N.index);return N.status?k(T,'not "'+L+'"'):F(T,null)})}function Tl(g){return R(g),i(function(C,T){var N=b(C,T);return T<C.length&&g(N)?F(T+1,N):k(T,"a character/byte matching "+g)})}function ey(g,C){arguments.length<2&&(C=g,g=void 0);var T=i(function(N,L){return T._=C()._,T._(N,L)});return g?T.desc(g):T}function Cf(){return Cl("fantasy-land/empty")}s.parse=function(g){if(typeof g!="string"&&!O(g))throw new Error(".parse must be called with a string or Buffer as its argument");var C,T=this.skip(xf)._(g,0);return C=T.status?{status:!0,value:T.value}:{status:!1,index:ee(g,T.furthest),expected:T.expected},delete M[g],C},s.tryParse=function(g){var C=this.parse(g);if(C.status)return C.value;var T=wf(g,C),N=new Error(T);throw N.type="ParsimmonError",N.result=C,N},s.assert=function(g,C){return this.chain(function(T){return g(T)?lo(T):Cl(C)})},s.or=function(g){return Sl(this,g)},s.trim=function(g){return this.wrap(g,g)},s.wrap=function(g,C){return ao(g,this,C,function(T,N){return N})},s.thru=function(g){return g(this)},s.then=function(g){return oe(g),wl(this,g).map(function(C){return C[1]})},s.many=function(){var g=this;return i(function(C,T){for(var N=[],L=void 0;;){if(!(L=P(g._(C,T),L)).status)return P(F(T,N),L);if(T===L.index)throw new Error("infinite loop detected in .many() parser --- calling .many() on a parser which can accept zero characters is usually the cause");T=L.index,N.push(L.value)}})},s.tieWith=function(g){return W(g),this.map(function(C){if(function(L){if(!x(L))throw new Error("not an array: "+L)}(C),C.length){W(C[0]);for(var T=C[0],N=1;N<C.length;N++)W(C[N]),T+=g+C[N];return T}return""})},s.tie=function(){return this.tieWith("")},s.times=function(g,C){var T=this;return arguments.length<2&&(C=g),_(g),_(C),i(function(N,L){for(var H=[],z=void 0,ne=void 0,ge=0;ge<g;ge+=1){if(ne=P(z=T._(N,L),ne),!z.status)return ne;L=z.index,H.push(z.value)}for(;ge<C&&(ne=P(z=T._(N,L),ne),z.status);ge+=1)L=z.index,H.push(z.value);return P(F(L,H),ne)})},s.result=function(g){return this.map(function(){return g})},s.atMost=function(g){return this.times(0,g)},s.atLeast=function(g){return ao(this.times(g),this.many(),function(C,T){return C.concat(T)})},s.map=function(g){R(g);var C=this;return i(function(T,N){var L=C._(T,N);return L.status?P(F(L.index,g(L.value)),L):L})},s.contramap=function(g){R(g);var C=this;return i(function(T,N){var L=C.parse(g(T.slice(N)));return L.status?F(N+T.length,L.value):L})},s.promap=function(g,C){return R(g),R(C),this.contramap(g).map(C)},s.skip=function(g){return wl(this,g).map(function(C){return C[0]})},s.mark=function(){return ao(vs,this,vs,function(g,C,T){return{start:g,value:C,end:T}})},s.node=function(g){return ao(vs,this,vs,function(C,T,N){return{name:g,value:T,start:C,end:N}})},s.sepBy=function(g){return Jh(this,g)},s.sepBy1=function(g){return Sf(this,g)},s.lookahead=function(g){return this.skip(xl(g))},s.notFollowedBy=function(g){return this.skip(Xh(g))},s.desc=function(g){x(g)||(g=[g]);var C=this;return i(function(T,N){var L=C._(T,N);return L.status||(L.expected=g),L})},s.fallback=function(g){return this.or(lo(g))},s.ap=function(g){return ao(g,this,function(C,T){return C(T)})},s.chain=function(g){var C=this;return i(function(T,N){var L=C._(T,N);return L.status?P(g(L.value)._(T,L.index),L):L})},s.concat=s.or,s.empty=Cf,s.of=lo,s["fantasy-land/ap"]=s.ap,s["fantasy-land/chain"]=s.chain,s["fantasy-land/concat"]=s.concat,s["fantasy-land/empty"]=s.empty,s["fantasy-land/of"]=s.of,s["fantasy-land/map"]=s.map;var vs=i(function(g,C){return F(C,ee(g,C))}),WS=i(function(g,C){return C>=g.length?k(C,"any character/byte"):F(C+1,b(g,C))}),HS=i(function(g,C){return F(g.length,g.slice(C))}),xf=i(function(g,C){return C<g.length?k(C,"EOF"):F(C,null)}),zS=qn(/[0-9]/).desc("a digit"),US=qn(/[0-9]*/).desc("optional digits"),jS=qn(/[a-z]/i).desc("a letter"),$S=qn(/[a-z]*/i).desc("optional letters"),GS=qn(/\s*/).desc("optional whitespace"),YS=qn(/\s+/).desc("whitespace"),ty=Ds("\r"),ny=Ds(`
`),ry=Ds(`\r
`),oy=Sl(ry,ny,ty).desc("newline"),ZS=Sl(oy,xf);i.all=HS,i.alt=Sl,i.any=WS,i.cr=ty,i.createLanguage=function(g){var C={};for(var T in g)({}).hasOwnProperty.call(g,T)&&function(N){C[N]=ey(function(){return g[N](C)})}(T);return C},i.crlf=ry,i.custom=function(g){return i(g(F,k))},i.digit=zS,i.digits=US,i.empty=Cf,i.end=ZS,i.eof=xf,i.fail=Cl,i.formatError=wf,i.index=vs,i.isParser=D,i.lazy=ey,i.letter=jS,i.letters=$S,i.lf=ny,i.lookahead=xl,i.makeFailure=k,i.makeSuccess=F,i.newline=oy,i.noneOf=function(g){return Tl(function(C){return g.indexOf(C)<0}).desc("none of '"+g+"'")},i.notFollowedBy=Xh,i.of=lo,i.oneOf=function(g){for(var C=g.split(""),T=0;T<C.length;T++)C[T]="'"+C[T]+"'";return Tl(function(N){return g.indexOf(N)>=0}).desc(C)},i.optWhitespace=GS,i.Parser=i,i.range=function(g,C){return Tl(function(T){return g<=T&&T<=C}).desc(g+"-"+C)},i.regex=qn,i.regexp=qn,i.sepBy=Jh,i.sepBy1=Sf,i.seq=wl,i.seqMap=ao,i.seqObj=function(){for(var g,C={},T=0,N=(g=arguments,Array.prototype.slice.call(g)),L=N.length,H=0;H<L;H+=1){var z=N[H];if(!D(z)){if(x(z)&&z.length===2&&typeof z[0]=="string"&&D(z[1])){var ne=z[0];if(Object.prototype.hasOwnProperty.call(C,ne))throw new Error("seqObj: duplicate key "+ne);C[ne]=!0,T++;continue}throw new Error("seqObj arguments must be parsers or [string, parser] array pairs.")}}if(T===0)throw new Error("seqObj expects at least one named parser, found zero");return i(function(ge,mt){for(var nt,Kn={},bt=0;bt<L;bt+=1){var Qn,$o;if(x(N[bt])?(Qn=N[bt][0],$o=N[bt][1]):(Qn=null,$o=N[bt]),!(nt=P($o._(ge,mt),nt)).status)return nt;Qn&&(Kn[Qn]=nt.value),mt=nt.index}return P(F(mt,Kn),nt)})},i.string=Ds,i.succeed=lo,i.takeWhile=function(g){return R(g),i(function(C,T){for(var N=T;N<C.length&&g(b(C,N));)N++;return F(N,C.slice(T,N))})},i.test=Tl,i.whitespace=YS,i["fantasy-land/empty"]=Cf,i["fantasy-land/of"]=lo,i.Binary={bitSeq:c,bitSeqObj:function(g){m();var C={},T=0,N=u(function(H){if(x(H)){var z=H;if(z.length!==2)throw new Error("["+z.join(", ")+"] should be length 2, got length "+z.length);if(W(z[0]),_(z[1]),Object.prototype.hasOwnProperty.call(C,z[0]))throw new Error("duplicate key in bitSeqObj: "+z[0]);return C[z[0]]=!0,T++,z}return _(H),[null,H]},g);if(T<1)throw new Error("bitSeqObj expects at least one named pair, got ["+g.join(", ")+"]");var L=u(function(H){return H[0]},N);return c(u(function(H){return H[1]},N)).map(function(H){return l(function(z,ne){return ne[0]!==null&&(z[ne[0]]=ne[1]),z},{},u(function(z,ne){return[z,H[ne]]},L))})},byte:function(g){if(m(),_(g),g>255)throw new Error("Value specified to byte constructor ("+g+"=0x"+g.toString(16)+") is larger in value than a single byte.");var C=(g>15?"0x":"0x0")+g.toString(16);return i(function(T,N){var L=b(T,N);return L===g?F(N+1,L):k(N,C)})},buffer:function(g){return y("buffer",g).map(function(C){return Buffer.from(C)})},encodedString:function(g,C){return y("string",C).map(function(T){return T.toString(g)})},uintBE:S,uint8BE:S(1),uint16BE:S(2),uint32BE:S(4),uintLE:p,uint8LE:p(1),uint16LE:p(2),uint32LE:p(4),intBE:h,int8BE:h(1),int16BE:h(2),int32BE:h(4),intLE:E,int8LE:E(1),int16LE:E(2),int32LE:E(4),floatBE:y("floatBE",4).map(function(g){return g.readFloatBE(0)}),floatLE:y("floatLE",4).map(function(g){return g.readFloatLE(0)}),doubleBE:y("doubleBE",8).map(function(g){return g.readDoubleBE(0)}),doubleLE:y("doubleLE",8).map(function(g){return g.readDoubleLE(0)})},n.exports=i}])})})(gu,gu.exports);var w=gu.exports,$d=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26F9(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC3\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC08\uDC26](?:\u200D\u2B1B)?|[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC2\uDECE-\uDEDB\uDEE0-\uDEE8]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g;function nD(t){return t==null?t:t.shiftToAll().normalize()}function Xg(t){return t.includes("/")&&(t=t.substring(t.lastIndexOf("/")+1)),t.endsWith(".md")&&(t=t.substring(0,t.length-3)),t}w.alt(w.regex(new RegExp($d(),"")),w.regex(/[0-9\p{Letter}_-]+/u).map(t=>t.toLocaleLowerCase()),w.whitespace.map(t=>"-"),w.any.map(t=>"")).many().map(t=>t.join(""));var wF=w.alt(w.regex(new RegExp($d(),"")),w.regex(/[0-9\p{Letter}_-]+/u),w.whitespace.map(t=>" "),w.any.map(t=>" ")).many().map(t=>t.join("").split(/\s+/).join(" ").trim());function SF(t){return wF.tryParse(t)}function CF(t){return t=nD(t),t=G.fromObject(Object.fromEntries(Object.entries(t.toObject()).filter(([,e])=>e!=0))),t.toHuman()}var ia;(function(t){function e(D,x=jd,O=!1){let F=n(D);if(!F)return x.renderNullAs;switch(F.type){case"null":return x.renderNullAs;case"string":return F.value;case"number":case"boolean":return""+F.value;case"html":return F.value.outerHTML;case"widget":return F.value.markdown();case"link":return F.value.markdown();case"function":return"<function>";case"array":let k="";return O&&(k+="["),k+=F.value.map(P=>e(P,x,!0)).join(", "),O&&(k+="]"),k;case"object":return"{ "+Object.entries(F.value).map(P=>P[0]+": "+e(P[1],x,!0)).join(", ")+" }";case"date":return F.value.second==0&&F.value.hour==0&&F.value.minute==0?F.value.toFormat(x.defaultDateFormat):F.value.toFormat(x.defaultDateTimeFormat);case"duration":return CF(F.value)}}t.toString=e;function n(D){return m(D)?{type:"null",value:D}:u(D)?{type:"number",value:D}:l(D)?{type:"string",value:D}:y(D)?{type:"boolean",value:D}:d(D)?{type:"duration",value:D}:f(D)?{type:"date",value:D}:S(D)?{type:"widget",value:D}:c(D)?{type:"array",value:D}:v(D)?{type:"link",value:D}:E(D)?{type:"function",value:D}:p(D)?{type:"html",value:D}:h(D)?{type:"object",value:D}:void 0}t.wrapValue=n;function r(D,x){if(h(D)){let O={};for(let[F,k]of Object.entries(D))O[F]=r(k,x);return O}else if(c(D)){let O=[];for(let F of D)O.push(r(F,x));return O}else return x(D)}t.mapLeaves=r;function o(D,x,O){var F,k;if(D===void 0&&(D=null),x===void 0&&(x=null),D===null&&x===null)return 0;if(D===null)return-1;if(x===null)return 1;let P=n(D),M=n(x);if(P===void 0&&M===void 0)return 0;if(P===void 0)return-1;if(M===void 0)return 1;if(P.type!=M.type)return P.type.localeCompare(M.type);if(P.value===M.value)return 0;switch(P.type){case"string":return P.value.localeCompare(M.value);case"number":return P.value<M.value?-1:P.value==M.value?0:1;case"null":return 0;case"boolean":return P.value==M.value?0:P.value?1:-1;case"link":let ee=P.value,oe=M.value,b=O!=null?O:tt=>tt,_=b(ee.path).localeCompare(b(oe.path));if(_!=0)return _;let R=ee.type.localeCompare(oe.type);return R!=0?R:ee.subpath&&!oe.subpath?1:!ee.subpath&&oe.subpath?-1:!ee.subpath&&!oe.subpath?0:((F=ee.subpath)!==null&&F!==void 0?F:"").localeCompare((k=oe.subpath)!==null&&k!==void 0?k:"");case"date":return P.value<M.value?-1:P.value.equals(M.value)?0:1;case"duration":return P.value<M.value?-1:P.value.equals(M.value)?0:1;case"array":let W=P.value,Q=M.value;for(let tt=0;tt<Math.min(W.length,Q.length);tt++){let Te=o(W[tt],Q[tt]);if(Te!=0)return Te}return W.length-Q.length;case"object":let K=P.value,Ue=M.value,Sr=Array.from(Object.keys(K)),bn=Array.from(Object.keys(Ue));Sr.sort(),bn.sort();let io=o(Sr,bn);if(io!=0)return io;for(let tt of Sr){let Te=o(K[tt],Ue[tt]);if(Te!=0)return Te}return 0;case"widget":case"html":case"function":return 0}}t.compareValue=o;function i(D){var x;return(x=n(D))===null||x===void 0?void 0:x.type}t.typeOf=i;function s(D){let x=n(D);if(!x)return!1;switch(x.type){case"number":return x.value!=0;case"string":return x.value.length>0;case"boolean":return x.value;case"link":return!!x.value.path;case"date":return x.value.toMillis()!=0;case"duration":return x.value.as("seconds")!=0;case"object":return Object.keys(x.value).length>0;case"array":return x.value.length>0;case"null":return!1;case"html":case"widget":case"function":return!0}}t.isTruthy=s;function a(D){if(D==null)return D;if(t.isArray(D))return[].concat(D.map(x=>a(x)));if(t.isObject(D)){let x={};for(let[O,F]of Object.entries(D))x[O]=a(F);return x}else return D}t.deepCopy=a;function l(D){return typeof D=="string"}t.isString=l;function u(D){return typeof D=="number"}t.isNumber=u;function f(D){return D instanceof Z}t.isDate=f;function d(D){return D instanceof G}t.isDuration=d;function m(D){return D==null}t.isNull=m;function c(D){return Array.isArray(D)}t.isArray=c;function y(D){return typeof D=="boolean"}t.isBoolean=y;function v(D){return D instanceof Ke}t.isLink=v;function S(D){return D instanceof sa}t.isWidget=S;function p(D){return typeof HTMLElement!="undefined"?D instanceof HTMLElement:!1}t.isHtml=p;function h(D){return typeof D=="object"&&!p(D)&&!S(D)&&!c(D)&&!d(D)&&!f(D)&&!v(D)&&D!==void 0&&!m(D)}t.isObject=h;function E(D){return typeof D=="function"}t.isFunction=E})(ia||(ia={}));var e0;(function(t){function e(o){return ia.isObject(o)&&Object.keys(o).length==2&&"key"in o&&"rows"in o}t.isElementGroup=e;function n(o){for(let i of o)if(!e(i))return!1;return!0}t.isGrouping=n;function r(o){if(n(o)){let i=0;for(let s of o)i+=r(s.rows);return i}else return o.length}t.count=r})(e0||(e0={}));var Ke=class{static file(e,n=!1,r){return new Ke({path:e,embed:n,display:r,subpath:void 0,type:"file"})}static infer(e,n=!1,r){if(e.includes("#^")){let o=e.split("#^");return Ke.block(o[0],o[1],n,r)}else if(e.includes("#")){let o=e.split("#");return Ke.header(o[0],o[1],n,r)}else return Ke.file(e,n,r)}static header(e,n,r,o){return new Ke({path:e,embed:r,display:o,subpath:SF(n),type:"header"})}static block(e,n,r,o){return new Ke({path:e,embed:r,display:o,subpath:n,type:"block"})}static fromObject(e){return new Ke(e)}constructor(e){Object.assign(this,e)}equals(e){return e==null||e==null?!1:this.path==e.path&&this.type==e.type&&this.subpath==e.subpath}toString(){return this.markdown()}toObject(){return{path:this.path,type:this.type,subpath:this.subpath,display:this.display,embed:this.embed}}withPath(e){return new Ke(Object.assign({},this,{path:e}))}withDisplay(e){return new Ke(Object.assign({},this,{display:e}))}withHeader(e){return Ke.header(this.path,e,this.embed,this.display)}toFile(){return Ke.file(this.path,this.embed,this.display)}toEmbed(){if(this.embed)return this;{let e=new Ke(this);return e.embed=!0,e}}fromEmbed(){if(this.embed){let e=new Ke(this);return e.embed=!1,e}else return this}markdown(){let e=(this.embed?"!":"")+"[["+this.obsidianLink();return this.display?e+="|"+this.display:(e+="|"+Xg(this.path),(this.type=="header"||this.type=="block")&&(e+=" > "+this.subpath)),e+="]]",e}obsidianLink(){var e,n;let r=this.path.replace("|","\\|");return this.type=="header"?r+"#"+((e=this.subpath)===null||e===void 0?void 0:e.replace("|","\\|")):this.type=="block"?r+"#^"+((n=this.subpath)===null||n===void 0?void 0:n.replace("|","\\|")):r}fileName(){return Xg(this.path).replace(".md","")}},sa=class{constructor(e){this.$widget=e}},Md=class extends sa{constructor(e,n){super("dataview:list-pair"),this.key=e,this.value=n}markdown(){return`${ia.toString(this.key)}: ${ia.toString(this.value)}`}},Id=class extends sa{constructor(e,n){super("dataview:external-link"),this.url=e,this.display=n}markdown(){var e;return`[${(e=this.display)!==null&&e!==void 0?e:this.url}](${this.url})`}},t0;(function(t){function e(s,a){return new Md(s,a)}t.listPair=e;function n(s,a){return new Id(s,a)}t.externalLink=n;function r(s){return s.$widget==="dataview:list-pair"}t.isListPair=r;function o(s){return s.$widget==="dataview:external-link"}t.isExternalLink=o;function i(s){return r(s)||o(s)}t.isBuiltin=i})(t0||(t0={}));var Me;(function(t){function e(m){return{type:"variable",name:m}}t.variable=e;function n(m){return{type:"literal",value:m}}t.literal=n;function r(m,c,y){return{type:"binaryop",left:m,op:c,right:y}}t.binaryOp=r;function o(m,c){return{type:"index",object:m,index:c}}t.index=o;function i(m){let c=m.split("."),y=t.variable(c[0]);for(let v=1;v<c.length;v++)y=t.index(y,t.literal(c[v]));return y}t.indexVariable=i;function s(m,c){return{type:"lambda",arguments:m,value:c}}t.lambda=s;function a(m,c){return{type:"function",func:m,arguments:c}}t.func=a;function l(m){return{type:"list",values:m}}t.list=l;function u(m){return{type:"object",values:m}}t.object=u;function f(m){return{type:"negated",child:m}}t.negate=f;function d(m){return m=="<="||m=="<"||m==">"||m==">="||m=="!="||m=="="}t.isCompareOp=d,t.NULL=t.literal(null)})(Me||(Me={}));var An;(function(t){function e(f){return{type:"tag",tag:f}}t.tag=e;function n(f){return{type:"csv",path:f}}t.csv=n;function r(f){return{type:"folder",folder:f}}t.folder=r;function o(f,d){return{type:"link",file:f,direction:d?"incoming":"outgoing"}}t.link=o;function i(f,d,m){return{type:"binaryop",left:f,op:d,right:m}}t.binaryOp=i;function s(f,d){return{type:"binaryop",left:f,op:"&",right:d}}t.and=s;function a(f,d){return{type:"binaryop",left:f,op:"|",right:d}}t.or=a;function l(f){return{type:"negate",child:f}}t.negate=l;function u(){return{type:"empty"}}t.empty=u})(An||(An={}));var n0=new RegExp($d(),""),Ad={year:G.fromObject({years:1}),years:G.fromObject({years:1}),yr:G.fromObject({years:1}),yrs:G.fromObject({years:1}),month:G.fromObject({months:1}),months:G.fromObject({months:1}),mo:G.fromObject({months:1}),mos:G.fromObject({months:1}),week:G.fromObject({weeks:1}),weeks:G.fromObject({weeks:1}),wk:G.fromObject({weeks:1}),wks:G.fromObject({weeks:1}),w:G.fromObject({weeks:1}),day:G.fromObject({days:1}),days:G.fromObject({days:1}),d:G.fromObject({days:1}),hour:G.fromObject({hours:1}),hours:G.fromObject({hours:1}),hr:G.fromObject({hours:1}),hrs:G.fromObject({hours:1}),h:G.fromObject({hours:1}),minute:G.fromObject({minutes:1}),minutes:G.fromObject({minutes:1}),min:G.fromObject({minutes:1}),mins:G.fromObject({minutes:1}),m:G.fromObject({minutes:1}),second:G.fromObject({seconds:1}),seconds:G.fromObject({seconds:1}),sec:G.fromObject({seconds:1}),secs:G.fromObject({seconds:1}),s:G.fromObject({seconds:1})},Ld={now:()=>Z.local(),today:()=>Z.local().startOf("day"),yesterday:()=>Z.local().startOf("day").minus(G.fromObject({days:1})),tomorrow:()=>Z.local().startOf("day").plus(G.fromObject({days:1})),sow:()=>Z.local().startOf("week"),"start-of-week":()=>Z.local().startOf("week"),eow:()=>Z.local().endOf("week"),"end-of-week":()=>Z.local().endOf("week"),soy:()=>Z.local().startOf("year"),"start-of-year":()=>Z.local().startOf("year"),eoy:()=>Z.local().endOf("year"),"end-of-year":()=>Z.local().endOf("year"),som:()=>Z.local().startOf("month"),"start-of-month":()=>Z.local().startOf("month"),eom:()=>Z.local().endOf("month"),"end-of-month":()=>Z.local().endOf("month")},Pd=["FROM","WHERE","LIMIT","GROUP","FLATTEN"];function xF(t){let e=-1;for(;(e=t.indexOf("|",e+1))>=0;)if(!(e>0&&t[e-1]=="\\"))return[t.substring(0,e).replace(/\\\|/g,"|"),t.substring(e+1)];return[t.replace(/\\\|/g,"|"),void 0]}function TF(t){let[e,n]=xF(t);return Ke.infer(e,!1,n)}function Xs(t,e,n){return w.seqMap(t,w.seq(w.optWhitespace,e,w.optWhitespace,t).many(),(r,o)=>{if(o.length==0)return r;let i=n(r,o[0][1],o[0][3]);for(let s=1;s<o.length;s++)i=n(i,o[s][1],o[s][3]);return i})}function FF(t,...e){return w.custom((n,r)=>(o,i)=>{let s=t._(o,i);if(!s.status)return s;for(let a of e){let l=a(s.value)._(o,s.index);if(!l.status)return s;s=l}return s})}var yn=w.createLanguage({number:t=>w.regexp(/-?[0-9]+(\.[0-9]+)?/).map(e=>Number.parseFloat(e)).desc("number"),string:t=>w.string('"').then(w.alt(t.escapeCharacter,w.noneOf('"\\')).atLeast(0).map(e=>e.join(""))).skip(w.string('"')).desc("string"),escapeCharacter:t=>w.string("\\").then(w.any).map(e=>e==='"'?'"':e==="\\"?"\\":"\\"+e),bool:t=>w.regexp(/true|false|True|False/).map(e=>e.toLowerCase()=="true").desc("boolean ('true' or 'false')"),tag:t=>w.seqMap(w.string("#"),w.alt(w.regexp(/[^\u2000-\u206F\u2E00-\u2E7F'!"#$%&()*+,.:;<=>?@^`{|}~\[\]\\\s]/).desc("text")).many(),(e,n)=>e+n.join("")).desc("tag ('#hello/stuff')"),identifier:t=>w.seqMap(w.alt(w.regexp(/\p{Letter}/u),w.regexp(n0).desc("text")),w.alt(w.regexp(/[0-9\p{Letter}_-]/u),w.regexp(n0).desc("text")).many(),(e,n)=>e+n.join("")).desc("variable identifier"),link:t=>w.regexp(/\[\[([^\[\]]*?)\]\]/u,1).map(e=>TF(e)).desc("file link"),embedLink:t=>w.seqMap(w.string("!").atMost(1),t.link,(e,n)=>(e.length>0&&(n.embed=!0),n)).desc("file link"),binaryPlusMinus:t=>w.regexp(/\+|-/).map(e=>e).desc("'+' or '-'"),binaryMulDiv:t=>w.regexp(/\*|\/|%/).map(e=>e).desc("'*' or '/' or '%'"),binaryCompareOp:t=>w.regexp(/>=|<=|!=|>|<|=/).map(e=>e).desc("'>=' or '<=' or '!=' or '=' or '>' or '<'"),binaryBooleanOp:t=>w.regexp(/and|or|&|\|/i).map(e=>e.toLowerCase()=="and"?"&":e.toLowerCase()=="or"?"|":e).desc("'and' or 'or'"),rootDate:t=>w.seqMap(w.regexp(/\d{4}/),w.string("-"),w.regexp(/\d{2}/),(e,n,r)=>Z.fromObject({year:Number.parseInt(e),month:Number.parseInt(r)})).desc("date in format YYYY-MM[-DDTHH-MM-SS.MS]"),dateShorthand:t=>w.alt(...Object.keys(Ld).sort((e,n)=>n.length-e.length).map(w.string)),date:t=>FF(t.rootDate,e=>w.seqMap(w.string("-"),w.regexp(/\d{2}/),(n,r)=>e.set({day:Number.parseInt(r)})),e=>w.seqMap(w.string("T"),w.regexp(/\d{2}/),(n,r)=>e.set({hour:Number.parseInt(r)})),e=>w.seqMap(w.string(":"),w.regexp(/\d{2}/),(n,r)=>e.set({minute:Number.parseInt(r)})),e=>w.seqMap(w.string(":"),w.regexp(/\d{2}/),(n,r)=>e.set({second:Number.parseInt(r)})),e=>w.alt(w.seqMap(w.string("."),w.regexp(/\d{3}/),(n,r)=>e.set({millisecond:Number.parseInt(r)})),w.succeed(e)),e=>w.alt(w.seqMap(w.string("+").or(w.string("-")),w.regexp(/\d{1,2}(:\d{2})?/),(n,r)=>e.setZone("UTC"+n+r,{keepLocalTime:!0})),w.seqMap(w.string("Z"),()=>e.setZone("utc",{keepLocalTime:!0})),w.seqMap(w.string("["),w.regexp(/[0-9A-Za-z+-\/]+/u),w.string("]"),(n,r,o)=>e.setZone(r,{keepLocalTime:!0})))).assert(e=>e.isValid,"valid date").desc("date in format YYYY-MM[-DDTHH-MM-SS.MS]"),datePlus:t=>w.alt(t.dateShorthand.map(e=>Ld[e]()),t.date).desc("date in format YYYY-MM[-DDTHH-MM-SS.MS] or in shorthand"),durationType:t=>w.alt(...Object.keys(Ad).sort((e,n)=>n.length-e.length).map(w.string)),duration:t=>w.seqMap(t.number,w.optWhitespace,t.durationType,(e,n,r)=>Ad[r].mapUnits(o=>o*e)).sepBy1(w.string(",").trim(w.optWhitespace).or(w.optWhitespace)).map(e=>e.reduce((n,r)=>n.plus(r))).desc("duration like 4hr2min"),rawNull:t=>w.string("null"),tagSource:t=>t.tag.map(e=>An.tag(e)),csvSource:t=>w.seqMap(w.string("csv(").skip(w.optWhitespace),t.string,w.string(")"),(e,n,r)=>An.csv(n)),linkIncomingSource:t=>t.link.map(e=>An.link(e.path,!0)),linkOutgoingSource:t=>w.seqMap(w.string("outgoing(").skip(w.optWhitespace),t.link,w.string(")"),(e,n,r)=>An.link(n.path,!1)),folderSource:t=>t.string.map(e=>An.folder(e)),parensSource:t=>w.seqMap(w.string("("),w.optWhitespace,t.source,w.optWhitespace,w.string(")"),(e,n,r,o,i)=>r),negateSource:t=>w.seqMap(w.alt(w.string("-"),w.string("!")),t.atomSource,(e,n)=>An.negate(n)),atomSource:t=>w.alt(t.parensSource,t.negateSource,t.linkOutgoingSource,t.linkIncomingSource,t.folderSource,t.tagSource,t.csvSource),binaryOpSource:t=>Xs(t.atomSource,t.binaryBooleanOp.map(e=>e),An.binaryOp),source:t=>t.binaryOpSource,variableField:t=>t.identifier.chain(e=>Pd.includes(e.toUpperCase())?w.fail("Variable fields cannot be a keyword ("+Pd.join(" or ")+")"):w.succeed(Me.variable(e))).desc("variable"),numberField:t=>t.number.map(e=>Me.literal(e)).desc("number"),stringField:t=>t.string.map(e=>Me.literal(e)).desc("string"),boolField:t=>t.bool.map(e=>Me.literal(e)).desc("boolean"),dateField:t=>w.seqMap(w.string("date("),w.optWhitespace,t.datePlus,w.optWhitespace,w.string(")"),(e,n,r,o,i)=>Me.literal(r)).desc("date"),durationField:t=>w.seqMap(w.string("dur("),w.optWhitespace,t.duration,w.optWhitespace,w.string(")"),(e,n,r,o,i)=>Me.literal(r)).desc("duration"),nullField:t=>t.rawNull.map(e=>Me.NULL),linkField:t=>t.link.map(e=>Me.literal(e)),listField:t=>t.field.sepBy(w.string(",").trim(w.optWhitespace)).wrap(w.string("[").skip(w.optWhitespace),w.optWhitespace.then(w.string("]"))).map(e=>Me.list(e)).desc("list ('[1, 2, 3]')"),objectField:t=>w.seqMap(t.identifier.or(t.string),w.string(":").trim(w.optWhitespace),t.field,(e,n,r)=>({name:e,value:r})).sepBy(w.string(",").trim(w.optWhitespace)).wrap(w.string("{").skip(w.optWhitespace),w.optWhitespace.then(w.string("}"))).map(e=>{let n={};for(let r of e)n[r.name]=r.value;return Me.object(n)}).desc("object ('{ a: 1, b: 2 }')"),atomInlineField:t=>w.alt(t.date,t.duration.map(e=>nD(e)),t.string,t.tag,t.embedLink,t.bool,t.number,t.rawNull),inlineFieldList:t=>t.atomInlineField.sepBy(w.string(",").trim(w.optWhitespace).lookahead(t.atomInlineField)),inlineField:t=>w.alt(w.seqMap(t.atomInlineField,w.string(",").trim(w.optWhitespace),t.inlineFieldList,(e,n,r)=>[e].concat(r)),t.atomInlineField),atomField:t=>w.alt(t.embedLink.map(e=>Me.literal(e)),t.negatedField,t.linkField,t.listField,t.objectField,t.lambdaField,t.parensField,t.boolField,t.numberField,t.stringField,t.dateField,t.durationField,t.nullField,t.variableField),indexField:t=>w.seqMap(t.atomField,w.alt(t.dotPostfix,t.indexPostfix,t.functionPostfix).many(),(e,n)=>{let r=e;for(let o of n)switch(o.type){case"dot":r=Me.index(r,Me.literal(o.field));break;case"index":r=Me.index(r,o.field);break;case"function":r=Me.func(r,o.fields);break}return r}),negatedField:t=>w.seqMap(w.string("!"),t.indexField,(e,n)=>Me.negate(n)).desc("negated field"),parensField:t=>w.seqMap(w.string("("),w.optWhitespace,t.field,w.optWhitespace,w.string(")"),(e,n,r,o,i)=>r),lambdaField:t=>w.seqMap(t.identifier.sepBy(w.string(",").trim(w.optWhitespace)).wrap(w.string("(").trim(w.optWhitespace),w.string(")").trim(w.optWhitespace)),w.string("=>").trim(w.optWhitespace),t.field,(e,n,r)=>({type:"lambda",arguments:e,value:r})),dotPostfix:t=>w.seqMap(w.string("."),t.identifier,(e,n)=>({type:"dot",field:n})),indexPostfix:t=>w.seqMap(w.string("["),w.optWhitespace,t.field,w.optWhitespace,w.string("]"),(e,n,r,o,i)=>({type:"index",field:r})),functionPostfix:t=>w.seqMap(w.string("("),w.optWhitespace,t.field.sepBy(w.string(",").trim(w.optWhitespace)),w.optWhitespace,w.string(")"),(e,n,r,o,i)=>({type:"function",fields:r})),binaryMulDivField:t=>Xs(t.indexField,t.binaryMulDiv,Me.binaryOp),binaryPlusMinusField:t=>Xs(t.binaryMulDivField,t.binaryPlusMinus,Me.binaryOp),binaryCompareField:t=>Xs(t.binaryPlusMinusField,t.binaryCompareOp,Me.binaryOp),binaryBooleanField:t=>Xs(t.binaryCompareField,t.binaryBooleanOp,Me.binaryOp),binaryOpField:t=>t.binaryBooleanField,field:t=>t.binaryOpField});function _F(t){try{return yu.success(yn.field.tryParse(t))}catch(e){return yu.failure(""+e)}}var Du;(function(t){function e(r,o){return{name:r,field:o}}t.named=e;function n(r,o){return{field:r,direction:o}}t.sortBy=n})(Du||(Du={}));function kF(t){return w.custom((e,n)=>(r,o)=>{let i=t._(r,o);return i.status?Object.assign({},i,{value:[i.value,r.substring(o,i.index)]}):i})}function OF(t){return t.split(/[\r\n]+/).map(e=>e.trim()).join("")}function r0(t,e){return w.eof.map(t).or(w.whitespace.then(e))}var bF=w.createLanguage({queryType:t=>w.alt(w.regexp(/TABLE|LIST|TASK|CALENDAR/i)).map(e=>e.toLowerCase()).desc("query type ('TABLE', 'LIST', 'TASK', or 'CALENDAR')"),explicitNamedField:t=>w.seqMap(yn.field.skip(w.whitespace),w.regexp(/AS/i).skip(w.whitespace),yn.identifier.or(yn.string),(e,n,r)=>Du.named(r,e)),namedField:t=>w.alt(t.explicitNamedField,kF(yn.field).map(([e,n])=>Du.named(OF(n),e))),sortField:t=>w.seqMap(yn.field.skip(w.optWhitespace),w.regexp(/ASCENDING|DESCENDING|ASC|DESC/i).atMost(1),(e,n)=>{let r=n.length==0?"ascending":n[0].toLowerCase();return r=="desc"&&(r="descending"),r=="asc"&&(r="ascending"),{field:e,direction:r}}),headerClause:t=>t.queryType.chain(e=>{switch(e){case"table":return r0(()=>({type:e,fields:[],showId:!0}),w.seqMap(w.regexp(/WITHOUT\s+ID/i).skip(w.optWhitespace).atMost(1),w.sepBy(t.namedField,w.string(",").trim(w.optWhitespace)),(n,r)=>({type:e,fields:r,showId:n.length==0})));case"list":return r0(()=>({type:e,format:void 0,showId:!0}),w.seqMap(w.regexp(/WITHOUT\s+ID/i).skip(w.optWhitespace).atMost(1),yn.field.atMost(1),(n,r)=>({type:e,format:r.length==1?r[0]:void 0,showId:n.length==0})));case"task":return w.succeed({type:e});case"calendar":return w.whitespace.then(w.seqMap(t.namedField,n=>({type:e,showId:!0,field:n})));default:return w.fail(`Unrecognized query type '${e}'`)}}).desc("TABLE or LIST or TASK or CALENDAR"),fromClause:t=>w.seqMap(w.regexp(/FROM/i),w.whitespace,yn.source,(e,n,r)=>r),whereClause:t=>w.seqMap(w.regexp(/WHERE/i),w.whitespace,yn.field,(e,n,r)=>({type:"where",clause:r})).desc("WHERE <expression>"),sortByClause:t=>w.seqMap(w.regexp(/SORT/i),w.whitespace,t.sortField.sepBy1(w.string(",").trim(w.optWhitespace)),(e,n,r)=>({type:"sort",fields:r})).desc("SORT field [ASC/DESC]"),limitClause:t=>w.seqMap(w.regexp(/LIMIT/i),w.whitespace,yn.field,(e,n,r)=>({type:"limit",amount:r})).desc("LIMIT <value>"),flattenClause:t=>w.seqMap(w.regexp(/FLATTEN/i).skip(w.whitespace),t.namedField,(e,n)=>({type:"flatten",field:n})).desc("FLATTEN <value> [AS <name>]"),groupByClause:t=>w.seqMap(w.regexp(/GROUP BY/i).skip(w.whitespace),t.namedField,(e,n)=>({type:"group",field:n})).desc("GROUP BY <value> [AS <name>]"),clause:t=>w.alt(t.fromClause,t.whereClause,t.sortByClause,t.limitClause,t.groupByClause,t.flattenClause),query:t=>w.seqMap(t.headerClause.trim(w.optWhitespace),t.fromClause.trim(w.optWhitespace).atMost(1),t.clause.trim(w.optWhitespace).many(),(e,n,r)=>({header:e,source:n.length==0?An.folder(""):n[0],operations:r,settings:jd}))}),RF=t=>{var e;return t?(e=t.plugins.plugins.dataview)===null||e===void 0?void 0:e.api:window.DataviewAPI},NF=t=>t.plugins.enabledPlugins.has("dataview");Ln.DATE_SHORTHANDS=Ld;Ln.DURATION_TYPES=Ad;Ln.EXPRESSION=yn;Ln.KEYWORDS=Pd;Ln.QUERY_LANGUAGE=bF;Ln.getAPI=RF;Ln.isPluginEnabled=NF;Ln.parseField=_F});var DD=Rn(ae=>{"use strict";var ca=Symbol.for("react.element"),LF=Symbol.for("react.portal"),PF=Symbol.for("react.fragment"),BF=Symbol.for("react.strict_mode"),VF=Symbol.for("react.profiler"),WF=Symbol.for("react.provider"),HF=Symbol.for("react.context"),zF=Symbol.for("react.forward_ref"),UF=Symbol.for("react.suspense"),jF=Symbol.for("react.memo"),$F=Symbol.for("react.lazy"),lD=Symbol.iterator;function GF(t){return t===null||typeof t!="object"?null:(t=lD&&t[lD]||t["@@iterator"],typeof t=="function"?t:null)}var fD={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},dD=Object.assign,mD={};function Ti(t,e,n){this.props=t,this.context=e,this.refs=mD,this.updater=n||fD}Ti.prototype.isReactComponent={};Ti.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};Ti.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function pD(){}pD.prototype=Ti.prototype;function Qd(t,e,n){this.props=t,this.context=e,this.refs=mD,this.updater=n||fD}var Jd=Qd.prototype=new pD;Jd.constructor=Qd;dD(Jd,Ti.prototype);Jd.isPureReactComponent=!0;var uD=Array.isArray,hD=Object.prototype.hasOwnProperty,Xd={current:null},yD={key:!0,ref:!0,__self:!0,__source:!0};function gD(t,e,n){var r,o={},i=null,s=null;if(e!=null)for(r in e.ref!==void 0&&(s=e.ref),e.key!==void 0&&(i=""+e.key),e)hD.call(e,r)&&!yD.hasOwnProperty(r)&&(o[r]=e[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];o.children=l}if(t&&t.defaultProps)for(r in a=t.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:ca,type:t,key:i,ref:s,props:o,_owner:Xd.current}}function YF(t,e){return{$$typeof:ca,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function em(t){return typeof t=="object"&&t!==null&&t.$$typeof===ca}function ZF(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var cD=/\/+/g;function Kd(t,e){return typeof t=="object"&&t!==null&&t.key!=null?ZF(""+t.key):e.toString(36)}function _u(t,e,n,r,o){var i=typeof t;(i==="undefined"||i==="boolean")&&(t=null);var s=!1;if(t===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(t.$$typeof){case ca:case LF:s=!0}}if(s)return s=t,o=o(s),t=r===""?"."+Kd(s,0):r,uD(o)?(n="",t!=null&&(n=t.replace(cD,"$&/")+"/"),_u(o,e,n,"",function(u){return u})):o!=null&&(em(o)&&(o=YF(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(cD,"$&/")+"/")+t)),e.push(o)),1;if(s=0,r=r===""?".":r+":",uD(t))for(var a=0;a<t.length;a++){i=t[a];var l=r+Kd(i,a);s+=_u(i,e,n,l,o)}else if(l=GF(t),typeof l=="function")for(t=l.call(t),a=0;!(i=t.next()).done;)i=i.value,l=r+Kd(i,a++),s+=_u(i,e,n,l,o);else if(i==="object")throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return s}function Fu(t,e,n){if(t==null)return t;var r=[],o=0;return _u(t,r,"","",function(i){return e.call(n,i,o++)}),r}function qF(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var pt={current:null},ku={transition:null},KF={ReactCurrentDispatcher:pt,ReactCurrentBatchConfig:ku,ReactCurrentOwner:Xd};ae.Children={map:Fu,forEach:function(t,e,n){Fu(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return Fu(t,function(){e++}),e},toArray:function(t){return Fu(t,function(e){return e})||[]},only:function(t){if(!em(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};ae.Component=Ti;ae.Fragment=PF;ae.Profiler=VF;ae.PureComponent=Qd;ae.StrictMode=BF;ae.Suspense=UF;ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=KF;ae.cloneElement=function(t,e,n){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var r=dD({},t.props),o=t.key,i=t.ref,s=t._owner;if(e!=null){if(e.ref!==void 0&&(i=e.ref,s=Xd.current),e.key!==void 0&&(o=""+e.key),t.type&&t.type.defaultProps)var a=t.type.defaultProps;for(l in e)hD.call(e,l)&&!yD.hasOwnProperty(l)&&(r[l]=e[l]===void 0&&a!==void 0?a[l]:e[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:ca,type:t.type,key:o,ref:i,props:r,_owner:s}};ae.createContext=function(t){return t={$$typeof:HF,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:WF,_context:t},t.Consumer=t};ae.createElement=gD;ae.createFactory=function(t){var e=gD.bind(null,t);return e.type=t,e};ae.createRef=function(){return{current:null}};ae.forwardRef=function(t){return{$$typeof:zF,render:t}};ae.isValidElement=em;ae.lazy=function(t){return{$$typeof:$F,_payload:{_status:-1,_result:t},_init:qF}};ae.memo=function(t,e){return{$$typeof:jF,type:t,compare:e===void 0?null:e}};ae.startTransition=function(t){var e=ku.transition;ku.transition={};try{t()}finally{ku.transition=e}};ae.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};ae.useCallback=function(t,e){return pt.current.useCallback(t,e)};ae.useContext=function(t){return pt.current.useContext(t)};ae.useDebugValue=function(){};ae.useDeferredValue=function(t){return pt.current.useDeferredValue(t)};ae.useEffect=function(t,e){return pt.current.useEffect(t,e)};ae.useId=function(){return pt.current.useId()};ae.useImperativeHandle=function(t,e,n){return pt.current.useImperativeHandle(t,e,n)};ae.useInsertionEffect=function(t,e){return pt.current.useInsertionEffect(t,e)};ae.useLayoutEffect=function(t,e){return pt.current.useLayoutEffect(t,e)};ae.useMemo=function(t,e){return pt.current.useMemo(t,e)};ae.useReducer=function(t,e,n){return pt.current.useReducer(t,e,n)};ae.useRef=function(t){return pt.current.useRef(t)};ae.useState=function(t){return pt.current.useState(t)};ae.useSyncExternalStore=function(t,e,n){return pt.current.useSyncExternalStore(t,e,n)};ae.useTransition=function(){return pt.current.useTransition()};ae.version="18.2.0"});var se=Rn((ZN,vD)=>{"use strict";vD.exports=DD()});var OD=Rn(De=>{"use strict";function om(t,e){var n=t.length;t.push(e);e:for(;0<n;){var r=n-1>>>1,o=t[r];if(0<Ou(o,e))t[r]=e,t[n]=o,n=r;else break e}}function vn(t){return t.length===0?null:t[0]}function Ru(t){if(t.length===0)return null;var e=t[0],n=t.pop();if(n!==e){t[0]=n;e:for(var r=0,o=t.length,i=o>>>1;r<i;){var s=2*(r+1)-1,a=t[s],l=s+1,u=t[l];if(0>Ou(a,n))l<o&&0>Ou(u,a)?(t[r]=u,t[l]=n,r=l):(t[r]=a,t[s]=n,r=s);else if(l<o&&0>Ou(u,n))t[r]=u,t[l]=n,r=l;else break e}}return e}function Ou(t,e){var n=t.sortIndex-e.sortIndex;return n!==0?n:t.id-e.id}typeof performance=="object"&&typeof performance.now=="function"?(ED=performance,De.unstable_now=function(){return ED.now()}):(tm=Date,wD=tm.now(),De.unstable_now=function(){return tm.now()-wD});var ED,tm,wD,Bn=[],Ir=[],QF=1,qt=null,at=3,Nu=!1,Co=!1,da=!1,xD=typeof setTimeout=="function"?setTimeout:null,TD=typeof clearTimeout=="function"?clearTimeout:null,SD=typeof setImmediate!="undefined"?setImmediate:null;typeof navigator!="undefined"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function im(t){for(var e=vn(Ir);e!==null;){if(e.callback===null)Ru(Ir);else if(e.startTime<=t)Ru(Ir),e.sortIndex=e.expirationTime,om(Bn,e);else break;e=vn(Ir)}}function sm(t){if(da=!1,im(t),!Co)if(vn(Bn)!==null)Co=!0,lm(am);else{var e=vn(Ir);e!==null&&um(sm,e.startTime-t)}}function am(t,e){Co=!1,da&&(da=!1,TD(ma),ma=-1),Nu=!0;var n=at;try{for(im(e),qt=vn(Bn);qt!==null&&(!(qt.expirationTime>e)||t&&!kD());){var r=qt.callback;if(typeof r=="function"){qt.callback=null,at=qt.priorityLevel;var o=r(qt.expirationTime<=e);e=De.unstable_now(),typeof o=="function"?qt.callback=o:qt===vn(Bn)&&Ru(Bn),im(e)}else Ru(Bn);qt=vn(Bn)}if(qt!==null)var i=!0;else{var s=vn(Ir);s!==null&&um(sm,s.startTime-e),i=!1}return i}finally{qt=null,at=n,Nu=!1}}var Mu=!1,bu=null,ma=-1,FD=5,_D=-1;function kD(){return!(De.unstable_now()-_D<FD)}function nm(){if(bu!==null){var t=De.unstable_now();_D=t;var e=!0;try{e=bu(!0,t)}finally{e?fa():(Mu=!1,bu=null)}}else Mu=!1}var fa;typeof SD=="function"?fa=function(){SD(nm)}:typeof MessageChannel!="undefined"?(rm=new MessageChannel,CD=rm.port2,rm.port1.onmessage=nm,fa=function(){CD.postMessage(null)}):fa=function(){xD(nm,0)};var rm,CD;function lm(t){bu=t,Mu||(Mu=!0,fa())}function um(t,e){ma=xD(function(){t(De.unstable_now())},e)}De.unstable_IdlePriority=5;De.unstable_ImmediatePriority=1;De.unstable_LowPriority=4;De.unstable_NormalPriority=3;De.unstable_Profiling=null;De.unstable_UserBlockingPriority=2;De.unstable_cancelCallback=function(t){t.callback=null};De.unstable_continueExecution=function(){Co||Nu||(Co=!0,lm(am))};De.unstable_forceFrameRate=function(t){0>t||125<t?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):FD=0<t?Math.floor(1e3/t):5};De.unstable_getCurrentPriorityLevel=function(){return at};De.unstable_getFirstCallbackNode=function(){return vn(Bn)};De.unstable_next=function(t){switch(at){case 1:case 2:case 3:var e=3;break;default:e=at}var n=at;at=e;try{return t()}finally{at=n}};De.unstable_pauseExecution=function(){};De.unstable_requestPaint=function(){};De.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=at;at=t;try{return e()}finally{at=n}};De.unstable_scheduleCallback=function(t,e,n){var r=De.unstable_now();switch(typeof n=="object"&&n!==null?(n=n.delay,n=typeof n=="number"&&0<n?r+n:r):n=r,t){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return o=n+o,t={id:QF++,callback:e,priorityLevel:t,startTime:n,expirationTime:o,sortIndex:-1},n>r?(t.sortIndex=n,om(Ir,t),vn(Bn)===null&&t===vn(Ir)&&(da?(TD(ma),ma=-1):da=!0,um(sm,n-r))):(t.sortIndex=o,om(Bn,t),Co||Nu||(Co=!0,lm(am))),t};De.unstable_shouldYield=kD;De.unstable_wrapCallback=function(t){var e=at;return function(){var n=at;at=e;try{return t.apply(this,arguments)}finally{at=n}}}});var RD=Rn((KN,bD)=>{"use strict";bD.exports=OD()});var Pw=Rn(Wt=>{"use strict";var Bv=se(),Bt=RD();function I(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Vv=new Set,Aa={};function Lo(t,e){Gi(t,e),Gi(t+"Capture",e)}function Gi(t,e){for(Aa[t]=e,t=0;t<e.length;t++)Vv.add(e[t])}var lr=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),Nm=Object.prototype.hasOwnProperty,JF=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ND={},MD={};function XF(t){return Nm.call(MD,t)?!0:Nm.call(ND,t)?!1:JF.test(t)?MD[t]=!0:(ND[t]=!0,!1)}function e_(t,e,n,r){if(n!==null&&n.type===0)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function t_(t,e,n,r){if(e===null||typeof e=="undefined"||e_(t,e,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!e;case 4:return e===!1;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}function gt(t,e,n,r,o,i,s){this.acceptsBooleans=e===2||e===3||e===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=i,this.removeEmptyString=s}var st={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){st[t]=new gt(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var e=t[0];st[e]=new gt(e,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){st[t]=new gt(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){st[t]=new gt(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){st[t]=new gt(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){st[t]=new gt(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){st[t]=new gt(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){st[t]=new gt(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){st[t]=new gt(t,5,!1,t.toLowerCase(),null,!1,!1)});var xp=/[\-:]([a-z])/g;function Tp(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var e=t.replace(xp,Tp);st[e]=new gt(e,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var e=t.replace(xp,Tp);st[e]=new gt(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var e=t.replace(xp,Tp);st[e]=new gt(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){st[t]=new gt(t,1,!1,t.toLowerCase(),null,!1,!1)});st.xlinkHref=new gt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){st[t]=new gt(t,1,!1,t.toLowerCase(),null,!0,!0)});function Fp(t,e,n,r){var o=st.hasOwnProperty(e)?st[e]:null;(o!==null?o.type!==0:r||!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(t_(e,n,o,r)&&(n=null),r||o===null?XF(e)&&(n===null?t.removeAttribute(e):t.setAttribute(e,""+n)):o.mustUseProperty?t[o.propertyName]=n===null?o.type===3?!1:"":n:(e=o.attributeName,r=o.attributeNamespace,n===null?t.removeAttribute(e):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?t.setAttributeNS(r,e,n):t.setAttribute(e,n))))}var dr=Bv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Iu=Symbol.for("react.element"),ki=Symbol.for("react.portal"),Oi=Symbol.for("react.fragment"),_p=Symbol.for("react.strict_mode"),Mm=Symbol.for("react.profiler"),Wv=Symbol.for("react.provider"),Hv=Symbol.for("react.context"),kp=Symbol.for("react.forward_ref"),Im=Symbol.for("react.suspense"),Am=Symbol.for("react.suspense_list"),Op=Symbol.for("react.memo"),Lr=Symbol.for("react.lazy");Symbol.for("react.scope");Symbol.for("react.debug_trace_mode");var zv=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden");Symbol.for("react.cache");Symbol.for("react.tracing_marker");var ID=Symbol.iterator;function pa(t){return t===null||typeof t!="object"?null:(t=ID&&t[ID]||t["@@iterator"],typeof t=="function"?t:null)}var Re=Object.assign,cm;function Sa(t){if(cm===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);cm=e&&e[1]||""}return`
`+cm+t}var fm=!1;function dm(t,e){if(!t||fm)return"";fm=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(e)if(e=function(){throw Error()},Object.defineProperty(e.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(e,[])}catch(u){var r=u}Reflect.construct(t,[],e)}else{try{e.call()}catch(u){r=u}t.call(e.prototype)}else{try{throw Error()}catch(u){r=u}t()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,a=i.length-1;1<=s&&0<=a&&o[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(o[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||o[s]!==i[a]){var l=`
`+o[s].replace(" at new "," at ");return t.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",t.displayName)),l}while(1<=s&&0<=a);break}}}finally{fm=!1,Error.prepareStackTrace=n}return(t=t?t.displayName||t.name:"")?Sa(t):""}function n_(t){switch(t.tag){case 5:return Sa(t.type);case 16:return Sa("Lazy");case 13:return Sa("Suspense");case 19:return Sa("SuspenseList");case 0:case 2:case 15:return t=dm(t.type,!1),t;case 11:return t=dm(t.type.render,!1),t;case 1:return t=dm(t.type,!0),t;default:return""}}function Lm(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case Oi:return"Fragment";case ki:return"Portal";case Mm:return"Profiler";case _p:return"StrictMode";case Im:return"Suspense";case Am:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case Hv:return(t.displayName||"Context")+".Consumer";case Wv:return(t._context.displayName||"Context")+".Provider";case kp:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Op:return e=t.displayName||null,e!==null?e:Lm(t.type)||"Memo";case Lr:e=t._payload,t=t._init;try{return Lm(t(e))}catch(n){}}return null}function r_(t){var e=t.type;switch(t.tag){case 24:return"Cache";case 9:return(e.displayName||"Context")+".Consumer";case 10:return(e._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=e.render,t=t.displayName||t.name||"",e.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return e;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Lm(e);case 8:return e===_p?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e}return null}function Kr(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Uv(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function o_(t){var e=Uv(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof n!="undefined"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Au(t){t._valueTracker||(t._valueTracker=o_(t))}function jv(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),r="";return t&&(r=Uv(t)?t.checked?"true":"false":t.value),t=r,t!==n?(e.setValue(t),!0):!1}function uc(t){if(t=t||(typeof document!="undefined"?document:void 0),typeof t=="undefined")return null;try{return t.activeElement||t.body}catch(e){return t.body}}function Pm(t,e){var n=e.checked;return Re({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n!=null?n:t._wrapperState.initialChecked})}function AD(t,e){var n=e.defaultValue==null?"":e.defaultValue,r=e.checked!=null?e.checked:e.defaultChecked;n=Kr(e.value!=null?e.value:n),t._wrapperState={initialChecked:r,initialValue:n,controlled:e.type==="checkbox"||e.type==="radio"?e.checked!=null:e.value!=null}}function $v(t,e){e=e.checked,e!=null&&Fp(t,"checked",e,!1)}function Bm(t,e){$v(t,e);var n=Kr(e.value),r=e.type;if(n!=null)r==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if(r==="submit"||r==="reset"){t.removeAttribute("value");return}e.hasOwnProperty("value")?Vm(t,e.type,n):e.hasOwnProperty("defaultValue")&&Vm(t,e.type,Kr(e.defaultValue)),e.checked==null&&e.defaultChecked!=null&&(t.defaultChecked=!!e.defaultChecked)}function LD(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var r=e.type;if(!(r!=="submit"&&r!=="reset"||e.value!==void 0&&e.value!==null))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}n=t.name,n!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,n!==""&&(t.name=n)}function Vm(t,e,n){(e!=="number"||uc(t.ownerDocument)!==t)&&(n==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}var Ca=Array.isArray;function Wi(t,e,n,r){if(t=t.options,e){e={};for(var o=0;o<n.length;o++)e["$"+n[o]]=!0;for(n=0;n<t.length;n++)o=e.hasOwnProperty("$"+t[n].value),t[n].selected!==o&&(t[n].selected=o),o&&r&&(t[n].defaultSelected=!0)}else{for(n=""+Kr(n),e=null,o=0;o<t.length;o++){if(t[o].value===n){t[o].selected=!0,r&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function Wm(t,e){if(e.dangerouslySetInnerHTML!=null)throw Error(I(91));return Re({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function PD(t,e){var n=e.value;if(n==null){if(n=e.children,e=e.defaultValue,n!=null){if(e!=null)throw Error(I(92));if(Ca(n)){if(1<n.length)throw Error(I(93));n=n[0]}e=n}e==null&&(e=""),n=e}t._wrapperState={initialValue:Kr(n)}}function Gv(t,e){var n=Kr(e.value),r=Kr(e.defaultValue);n!=null&&(n=""+n,n!==t.value&&(t.value=n),e.defaultValue==null&&t.defaultValue!==n&&(t.defaultValue=n)),r!=null&&(t.defaultValue=""+r)}function BD(t){var e=t.textContent;e===t._wrapperState.initialValue&&e!==""&&e!==null&&(t.value=e)}function Yv(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Hm(t,e){return t==null||t==="http://www.w3.org/1999/xhtml"?Yv(e):t==="http://www.w3.org/2000/svg"&&e==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var Lu,Zv=function(t){return typeof MSApp!="undefined"&&MSApp.execUnsafeLocalFunction?function(e,n,r,o){MSApp.execUnsafeLocalFunction(function(){return t(e,n,r,o)})}:t}(function(t,e){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=e;else{for(Lu=Lu||document.createElement("div"),Lu.innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=Lu.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}});function La(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var Fa={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},i_=["Webkit","ms","Moz","O"];Object.keys(Fa).forEach(function(t){i_.forEach(function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),Fa[e]=Fa[t]})});function qv(t,e,n){return e==null||typeof e=="boolean"||e===""?"":n||typeof e!="number"||e===0||Fa.hasOwnProperty(t)&&Fa[t]?(""+e).trim():e+"px"}function Kv(t,e){t=t.style;for(var n in e)if(e.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=qv(n,e[n],r);n==="float"&&(n="cssFloat"),r?t.setProperty(n,o):t[n]=o}}var s_=Re({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function zm(t,e){if(e){if(s_[t]&&(e.children!=null||e.dangerouslySetInnerHTML!=null))throw Error(I(137,t));if(e.dangerouslySetInnerHTML!=null){if(e.children!=null)throw Error(I(60));if(typeof e.dangerouslySetInnerHTML!="object"||!("__html"in e.dangerouslySetInnerHTML))throw Error(I(61))}if(e.style!=null&&typeof e.style!="object")throw Error(I(62))}}function Um(t,e){if(t.indexOf("-")===-1)return typeof e.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var jm=null;function bp(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var $m=null,Hi=null,zi=null;function VD(t){if(t=el(t)){if(typeof $m!="function")throw Error(I(280));var e=t.stateNode;e&&(e=Pc(e),$m(t.stateNode,t.type,e))}}function Qv(t){Hi?zi?zi.push(t):zi=[t]:Hi=t}function Jv(){if(Hi){var t=Hi,e=zi;if(zi=Hi=null,VD(t),e)for(t=0;t<e.length;t++)VD(e[t])}}function Xv(t,e){return t(e)}function eE(){}var mm=!1;function tE(t,e,n){if(mm)return t(e,n);mm=!0;try{return Xv(t,e,n)}finally{mm=!1,(Hi!==null||zi!==null)&&(eE(),Jv())}}function Pa(t,e){var n=t.stateNode;if(n===null)return null;var r=Pc(n);if(r===null)return null;n=r[e];e:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break e;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(I(231,e,typeof n));return n}var Gm=!1;if(lr)try{Fi={},Object.defineProperty(Fi,"passive",{get:function(){Gm=!0}}),window.addEventListener("test",Fi,Fi),window.removeEventListener("test",Fi,Fi)}catch(t){Gm=!1}var Fi;function a_(t,e,n,r,o,i,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{e.apply(n,u)}catch(f){this.onError(f)}}var _a=!1,cc=null,fc=!1,Ym=null,l_={onError:function(t){_a=!0,cc=t}};function u_(t,e,n,r,o,i,s,a,l){_a=!1,cc=null,a_.apply(l_,arguments)}function c_(t,e,n,r,o,i,s,a,l){if(u_.apply(this,arguments),_a){if(_a){var u=cc;_a=!1,cc=null}else throw Error(I(198));fc||(fc=!0,Ym=u)}}function Po(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function nE(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function WD(t){if(Po(t)!==t)throw Error(I(188))}function f_(t){var e=t.alternate;if(!e){if(e=Po(t),e===null)throw Error(I(188));return e!==t?null:t}for(var n=t,r=e;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return WD(o),t;if(i===r)return WD(o),e;i=i.sibling}throw Error(I(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s)throw Error(I(189))}}if(n.alternate!==r)throw Error(I(190))}if(n.tag!==3)throw Error(I(188));return n.stateNode.current===n?t:e}function rE(t){return t=f_(t),t!==null?oE(t):null}function oE(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var e=oE(t);if(e!==null)return e;t=t.sibling}return null}var iE=Bt.unstable_scheduleCallback,HD=Bt.unstable_cancelCallback,d_=Bt.unstable_shouldYield,m_=Bt.unstable_requestPaint,Le=Bt.unstable_now,p_=Bt.unstable_getCurrentPriorityLevel,Rp=Bt.unstable_ImmediatePriority,sE=Bt.unstable_UserBlockingPriority,dc=Bt.unstable_NormalPriority,h_=Bt.unstable_LowPriority,aE=Bt.unstable_IdlePriority,Mc=null,zn=null;function y_(t){if(zn&&typeof zn.onCommitFiberRoot=="function")try{zn.onCommitFiberRoot(Mc,t,void 0,(t.current.flags&128)===128)}catch(e){}}var xn=Math.clz32?Math.clz32:v_,g_=Math.log,D_=Math.LN2;function v_(t){return t>>>=0,t===0?32:31-(g_(t)/D_|0)|0}var Pu=64,Bu=4194304;function xa(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function mc(t,e){var n=t.pendingLanes;if(n===0)return 0;var r=0,o=t.suspendedLanes,i=t.pingedLanes,s=n&268435455;if(s!==0){var a=s&~o;a!==0?r=xa(a):(i&=s,i!==0&&(r=xa(i)))}else s=n&~o,s!==0?r=xa(s):i!==0&&(r=xa(i));if(r===0)return 0;if(e!==0&&e!==r&&!(e&o)&&(o=r&-r,i=e&-e,o>=i||o===16&&(i&4194240)!==0))return e;if(r&4&&(r|=n&16),e=t.entangledLanes,e!==0)for(t=t.entanglements,e&=r;0<e;)n=31-xn(e),o=1<<n,r|=t[n],e&=~o;return r}function E_(t,e){switch(t){case 1:case 2:case 4:return e+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function w_(t,e){for(var n=t.suspendedLanes,r=t.pingedLanes,o=t.expirationTimes,i=t.pendingLanes;0<i;){var s=31-xn(i),a=1<<s,l=o[s];l===-1?(!(a&n)||a&r)&&(o[s]=E_(a,e)):l<=e&&(t.expiredLanes|=a),i&=~a}}function Zm(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function lE(){var t=Pu;return Pu<<=1,!(Pu&4194240)&&(Pu=64),t}function pm(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Ja(t,e,n){t.pendingLanes|=e,e!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,e=31-xn(e),t[e]=n}function S_(t,e){var n=t.pendingLanes&~e;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=e,t.mutableReadLanes&=e,t.entangledLanes&=e,e=t.entanglements;var r=t.eventTimes;for(t=t.expirationTimes;0<n;){var o=31-xn(n),i=1<<o;e[o]=0,r[o]=-1,t[o]=-1,n&=~i}}function Np(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var r=31-xn(n),o=1<<r;o&e|t[r]&e&&(t[r]|=e),n&=~o}}var pe=0;function uE(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var cE,Mp,fE,dE,mE,qm=!1,Vu=[],zr=null,Ur=null,jr=null,Ba=new Map,Va=new Map,Br=[],C_="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zD(t,e){switch(t){case"focusin":case"focusout":zr=null;break;case"dragenter":case"dragleave":Ur=null;break;case"mouseover":case"mouseout":jr=null;break;case"pointerover":case"pointerout":Ba.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Va.delete(e.pointerId)}}function ha(t,e,n,r,o,i){return t===null||t.nativeEvent!==i?(t={blockedOn:e,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},e!==null&&(e=el(e),e!==null&&Mp(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function x_(t,e,n,r,o){switch(e){case"focusin":return zr=ha(zr,t,e,n,r,o),!0;case"dragenter":return Ur=ha(Ur,t,e,n,r,o),!0;case"mouseover":return jr=ha(jr,t,e,n,r,o),!0;case"pointerover":var i=o.pointerId;return Ba.set(i,ha(Ba.get(i)||null,t,e,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Va.set(i,ha(Va.get(i)||null,t,e,n,r,o)),!0}return!1}function pE(t){var e=Fo(t.target);if(e!==null){var n=Po(e);if(n!==null){if(e=n.tag,e===13){if(e=nE(n),e!==null){t.blockedOn=e,mE(t.priority,function(){fE(n)});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Xu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=Km(t.domEventName,t.eventSystemFlags,e[0],t.nativeEvent);if(n===null){n=t.nativeEvent;var r=new n.constructor(n.type,n);jm=r,n.target.dispatchEvent(r),jm=null}else return e=el(n),e!==null&&Mp(e),t.blockedOn=n,!1;e.shift()}return!0}function UD(t,e,n){Xu(t)&&n.delete(e)}function T_(){qm=!1,zr!==null&&Xu(zr)&&(zr=null),Ur!==null&&Xu(Ur)&&(Ur=null),jr!==null&&Xu(jr)&&(jr=null),Ba.forEach(UD),Va.forEach(UD)}function ya(t,e){t.blockedOn===e&&(t.blockedOn=null,qm||(qm=!0,Bt.unstable_scheduleCallback(Bt.unstable_NormalPriority,T_)))}function Wa(t){function e(o){return ya(o,t)}if(0<Vu.length){ya(Vu[0],t);for(var n=1;n<Vu.length;n++){var r=Vu[n];r.blockedOn===t&&(r.blockedOn=null)}}for(zr!==null&&ya(zr,t),Ur!==null&&ya(Ur,t),jr!==null&&ya(jr,t),Ba.forEach(e),Va.forEach(e),n=0;n<Br.length;n++)r=Br[n],r.blockedOn===t&&(r.blockedOn=null);for(;0<Br.length&&(n=Br[0],n.blockedOn===null);)pE(n),n.blockedOn===null&&Br.shift()}var Ui=dr.ReactCurrentBatchConfig,pc=!0;function F_(t,e,n,r){var o=pe,i=Ui.transition;Ui.transition=null;try{pe=1,Ip(t,e,n,r)}finally{pe=o,Ui.transition=i}}function __(t,e,n,r){var o=pe,i=Ui.transition;Ui.transition=null;try{pe=4,Ip(t,e,n,r)}finally{pe=o,Ui.transition=i}}function Ip(t,e,n,r){if(pc){var o=Km(t,e,n,r);if(o===null)wm(t,e,r,hc,n),zD(t,r);else if(x_(o,t,e,n,r))r.stopPropagation();else if(zD(t,r),e&4&&-1<C_.indexOf(t)){for(;o!==null;){var i=el(o);if(i!==null&&cE(i),i=Km(t,e,n,r),i===null&&wm(t,e,r,hc,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else wm(t,e,r,null,n)}}var hc=null;function Km(t,e,n,r){if(hc=null,t=bp(r),t=Fo(t),t!==null)if(e=Po(t),e===null)t=null;else if(n=e.tag,n===13){if(t=nE(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null);return hc=t,null}function hE(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(p_()){case Rp:return 1;case sE:return 4;case dc:case h_:return 16;case aE:return 536870912;default:return 16}default:return 16}}var Wr=null,Ap=null,ec=null;function yE(){if(ec)return ec;var t,e=Ap,n=e.length,r,o="value"in Wr?Wr.value:Wr.textContent,i=o.length;for(t=0;t<n&&e[t]===o[t];t++);var s=n-t;for(r=1;r<=s&&e[n-r]===o[i-r];r++);return ec=o.slice(t,1<r?1-r:void 0)}function tc(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Wu(){return!0}function jD(){return!1}function Vt(t){function e(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in t)t.hasOwnProperty(a)&&(n=t[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Wu:jD,this.isPropagationStopped=jD,this}return Re(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Wu)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Wu)},persist:function(){},isPersistent:Wu}),e}var Xi={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Lp=Vt(Xi),Xa=Re({},Xi,{view:0,detail:0}),k_=Vt(Xa),hm,ym,ga,Ic=Re({},Xa,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Pp,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==ga&&(ga&&t.type==="mousemove"?(hm=t.screenX-ga.screenX,ym=t.screenY-ga.screenY):ym=hm=0,ga=t),hm)},movementY:function(t){return"movementY"in t?t.movementY:ym}}),$D=Vt(Ic),O_=Re({},Ic,{dataTransfer:0}),b_=Vt(O_),R_=Re({},Xa,{relatedTarget:0}),gm=Vt(R_),N_=Re({},Xi,{animationName:0,elapsedTime:0,pseudoElement:0}),M_=Vt(N_),I_=Re({},Xi,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),A_=Vt(I_),L_=Re({},Xi,{data:0}),GD=Vt(L_),P_={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},B_={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},V_={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function W_(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=V_[t])?!!e[t]:!1}function Pp(){return W_}var H_=Re({},Xa,{key:function(t){if(t.key){var e=P_[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=tc(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?B_[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Pp,charCode:function(t){return t.type==="keypress"?tc(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?tc(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),z_=Vt(H_),U_=Re({},Ic,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),YD=Vt(U_),j_=Re({},Xa,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Pp}),$_=Vt(j_),G_=Re({},Xi,{propertyName:0,elapsedTime:0,pseudoElement:0}),Y_=Vt(G_),Z_=Re({},Ic,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),q_=Vt(Z_),K_=[9,13,27,32],Bp=lr&&"CompositionEvent"in window,ka=null;lr&&"documentMode"in document&&(ka=document.documentMode);var Q_=lr&&"TextEvent"in window&&!ka,gE=lr&&(!Bp||ka&&8<ka&&11>=ka),ZD=String.fromCharCode(32),qD=!1;function DE(t,e){switch(t){case"keyup":return K_.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function vE(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var bi=!1;function J_(t,e){switch(t){case"compositionend":return vE(e);case"keypress":return e.which!==32?null:(qD=!0,ZD);case"textInput":return t=e.data,t===ZD&&qD?null:t;default:return null}}function X_(t,e){if(bi)return t==="compositionend"||!Bp&&DE(t,e)?(t=yE(),ec=Ap=Wr=null,bi=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return gE&&e.locale!=="ko"?null:e.data;default:return null}}var ek={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function KD(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!ek[t.type]:e==="textarea"}function EE(t,e,n,r){Qv(r),e=yc(e,"onChange"),0<e.length&&(n=new Lp("onChange","change",null,n,r),t.push({event:n,listeners:e}))}var Oa=null,Ha=null;function tk(t){RE(t,0)}function Ac(t){var e=Mi(t);if(jv(e))return t}function nk(t,e){if(t==="change")return e}var wE=!1;lr&&(lr?(zu="oninput"in document,zu||(Dm=document.createElement("div"),Dm.setAttribute("oninput","return;"),zu=typeof Dm.oninput=="function"),Hu=zu):Hu=!1,wE=Hu&&(!document.documentMode||9<document.documentMode));var Hu,zu,Dm;function QD(){Oa&&(Oa.detachEvent("onpropertychange",SE),Ha=Oa=null)}function SE(t){if(t.propertyName==="value"&&Ac(Ha)){var e=[];EE(e,Ha,t,bp(t)),tE(tk,e)}}function rk(t,e,n){t==="focusin"?(QD(),Oa=e,Ha=n,Oa.attachEvent("onpropertychange",SE)):t==="focusout"&&QD()}function ok(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Ac(Ha)}function ik(t,e){if(t==="click")return Ac(e)}function sk(t,e){if(t==="input"||t==="change")return Ac(e)}function ak(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Fn=typeof Object.is=="function"?Object.is:ak;function za(t,e){if(Fn(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Nm.call(e,o)||!Fn(t[o],e[o]))return!1}return!0}function JD(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function XD(t,e){var n=JD(t);t=0;for(var r;n;){if(n.nodeType===3){if(r=t+n.textContent.length,t<=e&&r>=e)return{node:n,offset:e-t};t=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=JD(n)}}function CE(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?CE(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function xE(){for(var t=window,e=uc();e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch(r){n=!1}if(n)t=e.contentWindow;else break;e=uc(t.document)}return e}function Vp(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function lk(t){var e=xE(),n=t.focusedElem,r=t.selectionRange;if(e!==n&&n&&n.ownerDocument&&CE(n.ownerDocument.documentElement,n)){if(r!==null&&Vp(n)){if(e=r.start,t=r.end,t===void 0&&(t=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(t,n.value.length);else if(t=(e=n.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!t.extend&&i>r&&(o=r,r=i,i=o),o=XD(n,i);var s=XD(n,r);o&&s&&(t.rangeCount!==1||t.anchorNode!==o.node||t.anchorOffset!==o.offset||t.focusNode!==s.node||t.focusOffset!==s.offset)&&(e=e.createRange(),e.setStart(o.node,o.offset),t.removeAllRanges(),i>r?(t.addRange(e),t.extend(s.node,s.offset)):(e.setEnd(s.node,s.offset),t.addRange(e)))}}for(e=[],t=n;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)t=e[n],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var uk=lr&&"documentMode"in document&&11>=document.documentMode,Ri=null,Qm=null,ba=null,Jm=!1;function ev(t,e,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Jm||Ri==null||Ri!==uc(r)||(r=Ri,"selectionStart"in r&&Vp(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),ba&&za(ba,r)||(ba=r,r=yc(Qm,"onSelect"),0<r.length&&(e=new Lp("onSelect","select",null,e,n),t.push({event:e,listeners:r}),e.target=Ri)))}function Uu(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Ni={animationend:Uu("Animation","AnimationEnd"),animationiteration:Uu("Animation","AnimationIteration"),animationstart:Uu("Animation","AnimationStart"),transitionend:Uu("Transition","TransitionEnd")},vm={},TE={};lr&&(TE=document.createElement("div").style,"AnimationEvent"in window||(delete Ni.animationend.animation,delete Ni.animationiteration.animation,delete Ni.animationstart.animation),"TransitionEvent"in window||delete Ni.transitionend.transition);function Lc(t){if(vm[t])return vm[t];if(!Ni[t])return t;var e=Ni[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in TE)return vm[t]=e[n];return t}var FE=Lc("animationend"),_E=Lc("animationiteration"),kE=Lc("animationstart"),OE=Lc("transitionend"),bE=new Map,tv="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Jr(t,e){bE.set(t,e),Lo(e,[t])}for(ju=0;ju<tv.length;ju++)$u=tv[ju],nv=$u.toLowerCase(),rv=$u[0].toUpperCase()+$u.slice(1),Jr(nv,"on"+rv);var $u,nv,rv,ju;Jr(FE,"onAnimationEnd");Jr(_E,"onAnimationIteration");Jr(kE,"onAnimationStart");Jr("dblclick","onDoubleClick");Jr("focusin","onFocus");Jr("focusout","onBlur");Jr(OE,"onTransitionEnd");Gi("onMouseEnter",["mouseout","mouseover"]);Gi("onMouseLeave",["mouseout","mouseover"]);Gi("onPointerEnter",["pointerout","pointerover"]);Gi("onPointerLeave",["pointerout","pointerover"]);Lo("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Lo("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Lo("onBeforeInput",["compositionend","keypress","textInput","paste"]);Lo("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Lo("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Lo("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ta="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ck=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ta));function ov(t,e,n){var r=t.type||"unknown-event";t.currentTarget=n,c_(r,e,void 0,t),t.currentTarget=null}function RE(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var r=t[n],o=r.event;r=r.listeners;e:{var i=void 0;if(e)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&o.isPropagationStopped())break e;ov(o,a,u),i=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&o.isPropagationStopped())break e;ov(o,a,u),i=l}}}if(fc)throw t=Ym,fc=!1,Ym=null,t}function Ee(t,e){var n=e[rp];n===void 0&&(n=e[rp]=new Set);var r=t+"__bubble";n.has(r)||(NE(e,t,2,!1),n.add(r))}function Em(t,e,n){var r=0;e&&(r|=4),NE(n,t,r,e)}var Gu="_reactListening"+Math.random().toString(36).slice(2);function Ua(t){if(!t[Gu]){t[Gu]=!0,Vv.forEach(function(n){n!=="selectionchange"&&(ck.has(n)||Em(n,!1,t),Em(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Gu]||(e[Gu]=!0,Em("selectionchange",!1,e))}}function NE(t,e,n,r){switch(hE(e)){case 1:var o=F_;break;case 4:o=__;break;default:o=Ip}n=o.bind(null,e,n,t),o=void 0,!Gm||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),r?o!==void 0?t.addEventListener(e,n,{capture:!0,passive:o}):t.addEventListener(e,n,!0):o!==void 0?t.addEventListener(e,n,{passive:o}):t.addEventListener(e,n,!1)}function wm(t,e,n,r,o){var i=r;if(!(e&1)&&!(e&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;s=s.return}for(;a!==null;){if(s=Fo(a),s===null)return;if(l=s.tag,l===5||l===6){r=i=s;continue e}a=a.parentNode}}r=r.return}tE(function(){var u=i,f=bp(n),d=[];e:{var m=bE.get(t);if(m!==void 0){var c=Lp,y=t;switch(t){case"keypress":if(tc(n)===0)break e;case"keydown":case"keyup":c=z_;break;case"focusin":y="focus",c=gm;break;case"focusout":y="blur",c=gm;break;case"beforeblur":case"afterblur":c=gm;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=$D;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=b_;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=$_;break;case FE:case _E:case kE:c=M_;break;case OE:c=Y_;break;case"scroll":c=k_;break;case"wheel":c=q_;break;case"copy":case"cut":case"paste":c=A_;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=YD}var v=(e&4)!==0,S=!v&&t==="scroll",p=v?m!==null?m+"Capture":null:m;v=[];for(var h=u,E;h!==null;){E=h;var D=E.stateNode;if(E.tag===5&&D!==null&&(E=D,p!==null&&(D=Pa(h,p),D!=null&&v.push(ja(h,D,E)))),S)break;h=h.return}0<v.length&&(m=new c(m,y,null,n,f),d.push({event:m,listeners:v}))}}if(!(e&7)){e:{if(m=t==="mouseover"||t==="pointerover",c=t==="mouseout"||t==="pointerout",m&&n!==jm&&(y=n.relatedTarget||n.fromElement)&&(Fo(y)||y[ur]))break e;if((c||m)&&(m=f.window===f?f:(m=f.ownerDocument)?m.defaultView||m.parentWindow:window,c?(y=n.relatedTarget||n.toElement,c=u,y=y?Fo(y):null,y!==null&&(S=Po(y),y!==S||y.tag!==5&&y.tag!==6)&&(y=null)):(c=null,y=u),c!==y)){if(v=$D,D="onMouseLeave",p="onMouseEnter",h="mouse",(t==="pointerout"||t==="pointerover")&&(v=YD,D="onPointerLeave",p="onPointerEnter",h="pointer"),S=c==null?m:Mi(c),E=y==null?m:Mi(y),m=new v(D,h+"leave",c,n,f),m.target=S,m.relatedTarget=E,D=null,Fo(f)===u&&(v=new v(p,h+"enter",y,n,f),v.target=E,v.relatedTarget=S,D=v),S=D,c&&y)t:{for(v=c,p=y,h=0,E=v;E;E=_i(E))h++;for(E=0,D=p;D;D=_i(D))E++;for(;0<h-E;)v=_i(v),h--;for(;0<E-h;)p=_i(p),E--;for(;h--;){if(v===p||p!==null&&v===p.alternate)break t;v=_i(v),p=_i(p)}v=null}else v=null;c!==null&&iv(d,m,c,v,!1),y!==null&&S!==null&&iv(d,S,y,v,!0)}}e:{if(m=u?Mi(u):window,c=m.nodeName&&m.nodeName.toLowerCase(),c==="select"||c==="input"&&m.type==="file")var x=nk;else if(KD(m))if(wE)x=sk;else{x=ok;var O=rk}else(c=m.nodeName)&&c.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(x=ik);if(x&&(x=x(t,u))){EE(d,x,n,f);break e}O&&O(t,m,u),t==="focusout"&&(O=m._wrapperState)&&O.controlled&&m.type==="number"&&Vm(m,"number",m.value)}switch(O=u?Mi(u):window,t){case"focusin":(KD(O)||O.contentEditable==="true")&&(Ri=O,Qm=u,ba=null);break;case"focusout":ba=Qm=Ri=null;break;case"mousedown":Jm=!0;break;case"contextmenu":case"mouseup":case"dragend":Jm=!1,ev(d,n,f);break;case"selectionchange":if(uk)break;case"keydown":case"keyup":ev(d,n,f)}var F;if(Bp)e:{switch(t){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else bi?DE(t,n)&&(k="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(gE&&n.locale!=="ko"&&(bi||k!=="onCompositionStart"?k==="onCompositionEnd"&&bi&&(F=yE()):(Wr=f,Ap="value"in Wr?Wr.value:Wr.textContent,bi=!0)),O=yc(u,k),0<O.length&&(k=new GD(k,t,null,n,f),d.push({event:k,listeners:O}),F?k.data=F:(F=vE(n),F!==null&&(k.data=F)))),(F=Q_?J_(t,n):X_(t,n))&&(u=yc(u,"onBeforeInput"),0<u.length&&(f=new GD("onBeforeInput","beforeinput",null,n,f),d.push({event:f,listeners:u}),f.data=F))}RE(d,e)})}function ja(t,e,n){return{instance:t,listener:e,currentTarget:n}}function yc(t,e){for(var n=e+"Capture",r=[];t!==null;){var o=t,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Pa(t,n),i!=null&&r.unshift(ja(t,i,o)),i=Pa(t,e),i!=null&&r.push(ja(t,i,o))),t=t.return}return r}function _i(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function iv(t,e,n,r,o){for(var i=e._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,o?(l=Pa(n,i),l!=null&&s.unshift(ja(n,l,a))):o||(l=Pa(n,i),l!=null&&s.push(ja(n,l,a)))),n=n.return}s.length!==0&&t.push({event:e,listeners:s})}var fk=/\r\n?/g,dk=/\u0000|\uFFFD/g;function sv(t){return(typeof t=="string"?t:""+t).replace(fk,`
`).replace(dk,"")}function Yu(t,e,n){if(e=sv(e),sv(t)!==e&&n)throw Error(I(425))}function gc(){}var Xm=null,ep=null;function tp(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var np=typeof setTimeout=="function"?setTimeout:void 0,mk=typeof clearTimeout=="function"?clearTimeout:void 0,av=typeof Promise=="function"?Promise:void 0,pk=typeof queueMicrotask=="function"?queueMicrotask:typeof av!="undefined"?function(t){return av.resolve(null).then(t).catch(hk)}:np;function hk(t){setTimeout(function(){throw t})}function Sm(t,e){var n=e,r=0;do{var o=n.nextSibling;if(t.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){t.removeChild(o),Wa(e);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Wa(e)}function $r(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?")break;if(e==="/$")return null}}return t}function lv(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}var es=Math.random().toString(36).slice(2),Hn="__reactFiber$"+es,$a="__reactProps$"+es,ur="__reactContainer$"+es,rp="__reactEvents$"+es,yk="__reactListeners$"+es,gk="__reactHandles$"+es;function Fo(t){var e=t[Hn];if(e)return e;for(var n=t.parentNode;n;){if(e=n[ur]||n[Hn]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=lv(t);t!==null;){if(n=t[Hn])return n;t=lv(t)}return e}t=n,n=t.parentNode}return null}function el(t){return t=t[Hn]||t[ur],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function Mi(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(I(33))}function Pc(t){return t[$a]||null}var op=[],Ii=-1;function Xr(t){return{current:t}}function we(t){0>Ii||(t.current=op[Ii],op[Ii]=null,Ii--)}function ve(t,e){Ii++,op[Ii]=t.current,t.current=e}var Qr={},ft=Xr(Qr),Tt=Xr(!1),Ro=Qr;function Yi(t,e){var n=t.type.contextTypes;if(!n)return Qr;var r=t.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===e)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=e[i];return r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=o),o}function Ft(t){return t=t.childContextTypes,t!=null}function Dc(){we(Tt),we(ft)}function uv(t,e,n){if(ft.current!==Qr)throw Error(I(168));ve(ft,e),ve(Tt,n)}function ME(t,e,n){var r=t.stateNode;if(e=e.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in e))throw Error(I(108,r_(t)||"Unknown",o));return Re({},n,r)}function vc(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||Qr,Ro=ft.current,ve(ft,t),ve(Tt,Tt.current),!0}function cv(t,e,n){var r=t.stateNode;if(!r)throw Error(I(169));n?(t=ME(t,e,Ro),r.__reactInternalMemoizedMergedChildContext=t,we(Tt),we(ft),ve(ft,t)):we(Tt),ve(Tt,n)}var or=null,Bc=!1,Cm=!1;function IE(t){or===null?or=[t]:or.push(t)}function Dk(t){Bc=!0,IE(t)}function eo(){if(!Cm&&or!==null){Cm=!0;var t=0,e=pe;try{var n=or;for(pe=1;t<n.length;t++){var r=n[t];do r=r(!0);while(r!==null)}or=null,Bc=!1}catch(o){throw or!==null&&(or=or.slice(t+1)),iE(Rp,eo),o}finally{pe=e,Cm=!1}}return null}var Ai=[],Li=0,Ec=null,wc=0,Kt=[],Qt=0,No=null,ir=1,sr="";function xo(t,e){Ai[Li++]=wc,Ai[Li++]=Ec,Ec=t,wc=e}function AE(t,e,n){Kt[Qt++]=ir,Kt[Qt++]=sr,Kt[Qt++]=No,No=t;var r=ir;t=sr;var o=32-xn(r)-1;r&=~(1<<o),n+=1;var i=32-xn(e)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,ir=1<<32-xn(e)+o|n<<o|r,sr=i+t}else ir=1<<i|n<<o|r,sr=t}function Wp(t){t.return!==null&&(xo(t,1),AE(t,1,0))}function Hp(t){for(;t===Ec;)Ec=Ai[--Li],Ai[Li]=null,wc=Ai[--Li],Ai[Li]=null;for(;t===No;)No=Kt[--Qt],Kt[Qt]=null,sr=Kt[--Qt],Kt[Qt]=null,ir=Kt[--Qt],Kt[Qt]=null}var Pt=null,Lt=null,xe=!1,Cn=null;function LE(t,e){var n=Jt(5,null,null,0);n.elementType="DELETED",n.stateNode=e,n.return=t,e=t.deletions,e===null?(t.deletions=[n],t.flags|=16):e.push(n)}function fv(t,e){switch(t.tag){case 5:var n=t.type;return e=e.nodeType!==1||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e,e!==null?(t.stateNode=e,Pt=t,Lt=$r(e.firstChild),!0):!1;case 6:return e=t.pendingProps===""||e.nodeType!==3?null:e,e!==null?(t.stateNode=e,Pt=t,Lt=null,!0):!1;case 13:return e=e.nodeType!==8?null:e,e!==null?(n=No!==null?{id:ir,overflow:sr}:null,t.memoizedState={dehydrated:e,treeContext:n,retryLane:1073741824},n=Jt(18,null,null,0),n.stateNode=e,n.return=t,t.child=n,Pt=t,Lt=null,!0):!1;default:return!1}}function ip(t){return(t.mode&1)!==0&&(t.flags&128)===0}function sp(t){if(xe){var e=Lt;if(e){var n=e;if(!fv(t,e)){if(ip(t))throw Error(I(418));e=$r(n.nextSibling);var r=Pt;e&&fv(t,e)?LE(r,n):(t.flags=t.flags&-4097|2,xe=!1,Pt=t)}}else{if(ip(t))throw Error(I(418));t.flags=t.flags&-4097|2,xe=!1,Pt=t}}}function dv(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;Pt=t}function Zu(t){if(t!==Pt)return!1;if(!xe)return dv(t),xe=!0,!1;var e;if((e=t.tag!==3)&&!(e=t.tag!==5)&&(e=t.type,e=e!=="head"&&e!=="body"&&!tp(t.type,t.memoizedProps)),e&&(e=Lt)){if(ip(t))throw PE(),Error(I(418));for(;e;)LE(t,e),e=$r(e.nextSibling)}if(dv(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(I(317));e:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="/$"){if(e===0){Lt=$r(t.nextSibling);break e}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++}t=t.nextSibling}Lt=null}}else Lt=Pt?$r(t.stateNode.nextSibling):null;return!0}function PE(){for(var t=Lt;t;)t=$r(t.nextSibling)}function Zi(){Lt=Pt=null,xe=!1}function zp(t){Cn===null?Cn=[t]:Cn.push(t)}var vk=dr.ReactCurrentBatchConfig;function wn(t,e){if(t&&t.defaultProps){e=Re({},e),t=t.defaultProps;for(var n in t)e[n]===void 0&&(e[n]=t[n]);return e}return e}var Sc=Xr(null),Cc=null,Pi=null,Up=null;function jp(){Up=Pi=Cc=null}function $p(t){var e=Sc.current;we(Sc),t._currentValue=e}function ap(t,e,n){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===n)break;t=t.return}}function ji(t,e){Cc=t,Up=Pi=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&e&&(xt=!0),t.firstContext=null)}function en(t){var e=t._currentValue;if(Up!==t)if(t={context:t,memoizedValue:e,next:null},Pi===null){if(Cc===null)throw Error(I(308));Pi=t,Cc.dependencies={lanes:0,firstContext:t}}else Pi=Pi.next=t;return e}var _o=null;function Gp(t){_o===null?_o=[t]:_o.push(t)}function BE(t,e,n,r){var o=e.interleaved;return o===null?(n.next=n,Gp(e)):(n.next=o.next,o.next=n),e.interleaved=n,cr(t,r)}function cr(t,e){t.lanes|=e;var n=t.alternate;for(n!==null&&(n.lanes|=e),n=t,t=t.return;t!==null;)t.childLanes|=e,n=t.alternate,n!==null&&(n.childLanes|=e),n=t,t=t.return;return n.tag===3?n.stateNode:null}var Pr=!1;function Yp(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function VE(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function ar(t,e){return{eventTime:t,lane:e,tag:0,payload:null,callback:null,next:null}}function Gr(t,e,n){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,ce&2){var o=r.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),r.pending=e,cr(t,n)}return o=r.interleaved,o===null?(e.next=e,Gp(r)):(e.next=o.next,o.next=e),r.interleaved=e,cr(t,n)}function nc(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194240)!==0)){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,Np(t,n)}}function mv(t,e){var n=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=e:i=i.next=e}else o=i=e;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function xc(t,e,n,r){var o=t.updateQueue;Pr=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?i=u:s.next=u,s=l;var f=t.alternate;f!==null&&(f=f.updateQueue,a=f.lastBaseUpdate,a!==s&&(a===null?f.firstBaseUpdate=u:a.next=u,f.lastBaseUpdate=l))}if(i!==null){var d=o.baseState;s=0,f=u=l=null,a=i;do{var m=a.lane,c=a.eventTime;if((r&m)===m){f!==null&&(f=f.next={eventTime:c,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var y=t,v=a;switch(m=e,c=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){d=y.call(c,d,m);break e}d=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,m=typeof y=="function"?y.call(c,d,m):y,m==null)break e;d=Re({},d,m);break e;case 2:Pr=!0}}a.callback!==null&&a.lane!==0&&(t.flags|=64,m=o.effects,m===null?o.effects=[a]:m.push(a))}else c={eventTime:c,lane:m,tag:a.tag,payload:a.payload,callback:a.callback,next:null},f===null?(u=f=c,l=d):f=f.next=c,s|=m;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;m=a,a=m.next,m.next=null,o.lastBaseUpdate=m,o.shared.pending=null}}while(1);if(f===null&&(l=d),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=f,e=o.shared.interleaved,e!==null){o=e;do s|=o.lane,o=o.next;while(o!==e)}else i===null&&(o.shared.lanes=0);Io|=s,t.lanes=s,t.memoizedState=d}}function pv(t,e,n){if(t=e.effects,e.effects=null,t!==null)for(e=0;e<t.length;e++){var r=t[e],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(I(191,o));o.call(r)}}}var WE=new Bv.Component().refs;function lp(t,e,n,r){e=t.memoizedState,n=n(r,e),n=n==null?e:Re({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Vc={isMounted:function(t){return(t=t._reactInternals)?Po(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var r=yt(),o=Zr(t),i=ar(r,o);i.payload=e,n!=null&&(i.callback=n),e=Gr(t,i,o),e!==null&&(Tn(e,t,o,r),nc(e,t,o))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var r=yt(),o=Zr(t),i=ar(r,o);i.tag=1,i.payload=e,n!=null&&(i.callback=n),e=Gr(t,i,o),e!==null&&(Tn(e,t,o,r),nc(e,t,o))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=yt(),r=Zr(t),o=ar(n,r);o.tag=2,e!=null&&(o.callback=e),e=Gr(t,o,r),e!==null&&(Tn(e,t,r,n),nc(e,t,r))}};function hv(t,e,n,r,o,i,s){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,i,s):e.prototype&&e.prototype.isPureReactComponent?!za(n,r)||!za(o,i):!0}function HE(t,e,n){var r=!1,o=Qr,i=e.contextType;return typeof i=="object"&&i!==null?i=en(i):(o=Ft(e)?Ro:ft.current,r=e.contextTypes,i=(r=r!=null)?Yi(t,o):Qr),e=new e(n,i),t.memoizedState=e.state!==null&&e.state!==void 0?e.state:null,e.updater=Vc,t.stateNode=e,e._reactInternals=t,r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=o,t.__reactInternalMemoizedMaskedChildContext=i),e}function yv(t,e,n,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,r),e.state!==t&&Vc.enqueueReplaceState(e,e.state,null)}function up(t,e,n,r){var o=t.stateNode;o.props=n,o.state=t.memoizedState,o.refs=WE,Yp(t);var i=e.contextType;typeof i=="object"&&i!==null?o.context=en(i):(i=Ft(e)?Ro:ft.current,o.context=Yi(t,i)),o.state=t.memoizedState,i=e.getDerivedStateFromProps,typeof i=="function"&&(lp(t,e,i,n),o.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(e=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),e!==o.state&&Vc.enqueueReplaceState(o,o.state,null),xc(t,n,o,r),o.state=t.memoizedState),typeof o.componentDidMount=="function"&&(t.flags|=4194308)}function Da(t,e,n){if(t=n.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(I(309));var r=n.stateNode}if(!r)throw Error(I(147,t));var o=r,i=""+t;return e!==null&&e.ref!==null&&typeof e.ref=="function"&&e.ref._stringRef===i?e.ref:(e=function(s){var a=o.refs;a===WE&&(a=o.refs={}),s===null?delete a[i]:a[i]=s},e._stringRef=i,e)}if(typeof t!="string")throw Error(I(284));if(!n._owner)throw Error(I(290,t))}return t}function qu(t,e){throw t=Object.prototype.toString.call(e),Error(I(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function gv(t){var e=t._init;return e(t._payload)}function zE(t){function e(p,h){if(t){var E=p.deletions;E===null?(p.deletions=[h],p.flags|=16):E.push(h)}}function n(p,h){if(!t)return null;for(;h!==null;)e(p,h),h=h.sibling;return null}function r(p,h){for(p=new Map;h!==null;)h.key!==null?p.set(h.key,h):p.set(h.index,h),h=h.sibling;return p}function o(p,h){return p=qr(p,h),p.index=0,p.sibling=null,p}function i(p,h,E){return p.index=E,t?(E=p.alternate,E!==null?(E=E.index,E<h?(p.flags|=2,h):E):(p.flags|=2,h)):(p.flags|=1048576,h)}function s(p){return t&&p.alternate===null&&(p.flags|=2),p}function a(p,h,E,D){return h===null||h.tag!==6?(h=bm(E,p.mode,D),h.return=p,h):(h=o(h,E),h.return=p,h)}function l(p,h,E,D){var x=E.type;return x===Oi?f(p,h,E.props.children,D,E.key):h!==null&&(h.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Lr&&gv(x)===h.type)?(D=o(h,E.props),D.ref=Da(p,h,E),D.return=p,D):(D=lc(E.type,E.key,E.props,null,p.mode,D),D.ref=Da(p,h,E),D.return=p,D)}function u(p,h,E,D){return h===null||h.tag!==4||h.stateNode.containerInfo!==E.containerInfo||h.stateNode.implementation!==E.implementation?(h=Rm(E,p.mode,D),h.return=p,h):(h=o(h,E.children||[]),h.return=p,h)}function f(p,h,E,D,x){return h===null||h.tag!==7?(h=bo(E,p.mode,D,x),h.return=p,h):(h=o(h,E),h.return=p,h)}function d(p,h,E){if(typeof h=="string"&&h!==""||typeof h=="number")return h=bm(""+h,p.mode,E),h.return=p,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Iu:return E=lc(h.type,h.key,h.props,null,p.mode,E),E.ref=Da(p,null,h),E.return=p,E;case ki:return h=Rm(h,p.mode,E),h.return=p,h;case Lr:var D=h._init;return d(p,D(h._payload),E)}if(Ca(h)||pa(h))return h=bo(h,p.mode,E,null),h.return=p,h;qu(p,h)}return null}function m(p,h,E,D){var x=h!==null?h.key:null;if(typeof E=="string"&&E!==""||typeof E=="number")return x!==null?null:a(p,h,""+E,D);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case Iu:return E.key===x?l(p,h,E,D):null;case ki:return E.key===x?u(p,h,E,D):null;case Lr:return x=E._init,m(p,h,x(E._payload),D)}if(Ca(E)||pa(E))return x!==null?null:f(p,h,E,D,null);qu(p,E)}return null}function c(p,h,E,D,x){if(typeof D=="string"&&D!==""||typeof D=="number")return p=p.get(E)||null,a(h,p,""+D,x);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case Iu:return p=p.get(D.key===null?E:D.key)||null,l(h,p,D,x);case ki:return p=p.get(D.key===null?E:D.key)||null,u(h,p,D,x);case Lr:var O=D._init;return c(p,h,E,O(D._payload),x)}if(Ca(D)||pa(D))return p=p.get(E)||null,f(h,p,D,x,null);qu(h,D)}return null}function y(p,h,E,D){for(var x=null,O=null,F=h,k=h=0,P=null;F!==null&&k<E.length;k++){F.index>k?(P=F,F=null):P=F.sibling;var M=m(p,F,E[k],D);if(M===null){F===null&&(F=P);break}t&&F&&M.alternate===null&&e(p,F),h=i(M,h,k),O===null?x=M:O.sibling=M,O=M,F=P}if(k===E.length)return n(p,F),xe&&xo(p,k),x;if(F===null){for(;k<E.length;k++)F=d(p,E[k],D),F!==null&&(h=i(F,h,k),O===null?x=F:O.sibling=F,O=F);return xe&&xo(p,k),x}for(F=r(p,F);k<E.length;k++)P=c(F,p,k,E[k],D),P!==null&&(t&&P.alternate!==null&&F.delete(P.key===null?k:P.key),h=i(P,h,k),O===null?x=P:O.sibling=P,O=P);return t&&F.forEach(function(ee){return e(p,ee)}),xe&&xo(p,k),x}function v(p,h,E,D){var x=pa(E);if(typeof x!="function")throw Error(I(150));if(E=x.call(E),E==null)throw Error(I(151));for(var O=x=null,F=h,k=h=0,P=null,M=E.next();F!==null&&!M.done;k++,M=E.next()){F.index>k?(P=F,F=null):P=F.sibling;var ee=m(p,F,M.value,D);if(ee===null){F===null&&(F=P);break}t&&F&&ee.alternate===null&&e(p,F),h=i(ee,h,k),O===null?x=ee:O.sibling=ee,O=ee,F=P}if(M.done)return n(p,F),xe&&xo(p,k),x;if(F===null){for(;!M.done;k++,M=E.next())M=d(p,M.value,D),M!==null&&(h=i(M,h,k),O===null?x=M:O.sibling=M,O=M);return xe&&xo(p,k),x}for(F=r(p,F);!M.done;k++,M=E.next())M=c(F,p,k,M.value,D),M!==null&&(t&&M.alternate!==null&&F.delete(M.key===null?k:M.key),h=i(M,h,k),O===null?x=M:O.sibling=M,O=M);return t&&F.forEach(function(oe){return e(p,oe)}),xe&&xo(p,k),x}function S(p,h,E,D){if(typeof E=="object"&&E!==null&&E.type===Oi&&E.key===null&&(E=E.props.children),typeof E=="object"&&E!==null){switch(E.$$typeof){case Iu:e:{for(var x=E.key,O=h;O!==null;){if(O.key===x){if(x=E.type,x===Oi){if(O.tag===7){n(p,O.sibling),h=o(O,E.props.children),h.return=p,p=h;break e}}else if(O.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Lr&&gv(x)===O.type){n(p,O.sibling),h=o(O,E.props),h.ref=Da(p,O,E),h.return=p,p=h;break e}n(p,O);break}else e(p,O);O=O.sibling}E.type===Oi?(h=bo(E.props.children,p.mode,D,E.key),h.return=p,p=h):(D=lc(E.type,E.key,E.props,null,p.mode,D),D.ref=Da(p,h,E),D.return=p,p=D)}return s(p);case ki:e:{for(O=E.key;h!==null;){if(h.key===O)if(h.tag===4&&h.stateNode.containerInfo===E.containerInfo&&h.stateNode.implementation===E.implementation){n(p,h.sibling),h=o(h,E.children||[]),h.return=p,p=h;break e}else{n(p,h);break}else e(p,h);h=h.sibling}h=Rm(E,p.mode,D),h.return=p,p=h}return s(p);case Lr:return O=E._init,S(p,h,O(E._payload),D)}if(Ca(E))return y(p,h,E,D);if(pa(E))return v(p,h,E,D);qu(p,E)}return typeof E=="string"&&E!==""||typeof E=="number"?(E=""+E,h!==null&&h.tag===6?(n(p,h.sibling),h=o(h,E),h.return=p,p=h):(n(p,h),h=bm(E,p.mode,D),h.return=p,p=h),s(p)):n(p,h)}return S}var qi=zE(!0),UE=zE(!1),tl={},Un=Xr(tl),Ga=Xr(tl),Ya=Xr(tl);function ko(t){if(t===tl)throw Error(I(174));return t}function Zp(t,e){switch(ve(Ya,e),ve(Ga,t),ve(Un,tl),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:Hm(null,"");break;default:t=t===8?e.parentNode:e,e=t.namespaceURI||null,t=t.tagName,e=Hm(e,t)}we(Un),ve(Un,e)}function Ki(){we(Un),we(Ga),we(Ya)}function jE(t){ko(Ya.current);var e=ko(Un.current),n=Hm(e,t.type);e!==n&&(ve(Ga,t),ve(Un,n))}function qp(t){Ga.current===t&&(we(Un),we(Ga))}var Oe=Xr(0);function Tc(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var xm=[];function Kp(){for(var t=0;t<xm.length;t++)xm[t]._workInProgressVersionPrimary=null;xm.length=0}var rc=dr.ReactCurrentDispatcher,Tm=dr.ReactCurrentBatchConfig,Mo=0,be=null,Ge=null,Je=null,Fc=!1,Ra=!1,Za=0,Ek=0;function lt(){throw Error(I(321))}function Qp(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Fn(t[n],e[n]))return!1;return!0}function Jp(t,e,n,r,o,i){if(Mo=i,be=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,rc.current=t===null||t.memoizedState===null?xk:Tk,t=n(r,o),Ra){i=0;do{if(Ra=!1,Za=0,25<=i)throw Error(I(301));i+=1,Je=Ge=null,e.updateQueue=null,rc.current=Fk,t=n(r,o)}while(Ra)}if(rc.current=_c,e=Ge!==null&&Ge.next!==null,Mo=0,Je=Ge=be=null,Fc=!1,e)throw Error(I(300));return t}function Xp(){var t=Za!==0;return Za=0,t}function Wn(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Je===null?be.memoizedState=Je=t:Je=Je.next=t,Je}function tn(){if(Ge===null){var t=be.alternate;t=t!==null?t.memoizedState:null}else t=Ge.next;var e=Je===null?be.memoizedState:Je.next;if(e!==null)Je=e,Ge=t;else{if(t===null)throw Error(I(310));Ge=t,t={memoizedState:Ge.memoizedState,baseState:Ge.baseState,baseQueue:Ge.baseQueue,queue:Ge.queue,next:null},Je===null?be.memoizedState=Je=t:Je=Je.next=t}return Je}function qa(t,e){return typeof e=="function"?e(t):e}function Fm(t){var e=tn(),n=e.queue;if(n===null)throw Error(I(311));n.lastRenderedReducer=t;var r=Ge,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var a=s=null,l=null,u=i;do{var f=u.lane;if((Mo&f)===f)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:t(r,u.action);else{var d={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,s=r):l=l.next=d,be.lanes|=f,Io|=f}u=u.next}while(u!==null&&u!==i);l===null?s=r:l.next=a,Fn(r,e.memoizedState)||(xt=!0),e.memoizedState=r,e.baseState=s,e.baseQueue=l,n.lastRenderedState=r}if(t=n.interleaved,t!==null){o=t;do i=o.lane,be.lanes|=i,Io|=i,o=o.next;while(o!==t)}else o===null&&(n.lanes=0);return[e.memoizedState,n.dispatch]}function _m(t){var e=tn(),n=e.queue;if(n===null)throw Error(I(311));n.lastRenderedReducer=t;var r=n.dispatch,o=n.pending,i=e.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=t(i,s.action),s=s.next;while(s!==o);Fn(i,e.memoizedState)||(xt=!0),e.memoizedState=i,e.baseQueue===null&&(e.baseState=i),n.lastRenderedState=i}return[i,r]}function $E(){}function GE(t,e){var n=be,r=tn(),o=e(),i=!Fn(r.memoizedState,o);if(i&&(r.memoizedState=o,xt=!0),r=r.queue,eh(qE.bind(null,n,r,t),[t]),r.getSnapshot!==e||i||Je!==null&&Je.memoizedState.tag&1){if(n.flags|=2048,Ka(9,ZE.bind(null,n,r,o,e),void 0,null),Xe===null)throw Error(I(349));Mo&30||YE(n,e,o)}return o}function YE(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=be.updateQueue,e===null?(e={lastEffect:null,stores:null},be.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function ZE(t,e,n,r){e.value=n,e.getSnapshot=r,KE(e)&&QE(t)}function qE(t,e,n){return n(function(){KE(e)&&QE(t)})}function KE(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Fn(t,n)}catch(r){return!0}}function QE(t){var e=cr(t,1);e!==null&&Tn(e,t,1,-1)}function Dv(t){var e=Wn();return typeof t=="function"&&(t=t()),e.memoizedState=e.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:qa,lastRenderedState:t},e.queue=t,t=t.dispatch=Ck.bind(null,be,t),[e.memoizedState,t]}function Ka(t,e,n,r){return t={tag:t,create:e,destroy:n,deps:r,next:null},e=be.updateQueue,e===null?(e={lastEffect:null,stores:null},be.updateQueue=e,e.lastEffect=t.next=t):(n=e.lastEffect,n===null?e.lastEffect=t.next=t:(r=n.next,n.next=t,t.next=r,e.lastEffect=t)),t}function JE(){return tn().memoizedState}function oc(t,e,n,r){var o=Wn();be.flags|=t,o.memoizedState=Ka(1|e,n,void 0,r===void 0?null:r)}function Wc(t,e,n,r){var o=tn();r=r===void 0?null:r;var i=void 0;if(Ge!==null){var s=Ge.memoizedState;if(i=s.destroy,r!==null&&Qp(r,s.deps)){o.memoizedState=Ka(e,n,i,r);return}}be.flags|=t,o.memoizedState=Ka(1|e,n,i,r)}function vv(t,e){return oc(8390656,8,t,e)}function eh(t,e){return Wc(2048,8,t,e)}function XE(t,e){return Wc(4,2,t,e)}function ew(t,e){return Wc(4,4,t,e)}function tw(t,e){if(typeof e=="function")return t=t(),e(t),function(){e(null)};if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function nw(t,e,n){return n=n!=null?n.concat([t]):null,Wc(4,4,tw.bind(null,e,t),n)}function th(){}function rw(t,e){var n=tn();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Qp(e,r[1])?r[0]:(n.memoizedState=[t,e],t)}function ow(t,e){var n=tn();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Qp(e,r[1])?r[0]:(t=t(),n.memoizedState=[t,e],t)}function iw(t,e,n){return Mo&21?(Fn(n,e)||(n=lE(),be.lanes|=n,Io|=n,t.baseState=!0),e):(t.baseState&&(t.baseState=!1,xt=!0),t.memoizedState=n)}function wk(t,e){var n=pe;pe=n!==0&&4>n?n:4,t(!0);var r=Tm.transition;Tm.transition={};try{t(!1),e()}finally{pe=n,Tm.transition=r}}function sw(){return tn().memoizedState}function Sk(t,e,n){var r=Zr(t);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},aw(t))lw(e,n);else if(n=BE(t,e,n,r),n!==null){var o=yt();Tn(n,t,r,o),uw(n,e,r)}}function Ck(t,e,n){var r=Zr(t),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(aw(t))lw(e,o);else{var i=t.alternate;if(t.lanes===0&&(i===null||i.lanes===0)&&(i=e.lastRenderedReducer,i!==null))try{var s=e.lastRenderedState,a=i(s,n);if(o.hasEagerState=!0,o.eagerState=a,Fn(a,s)){var l=e.interleaved;l===null?(o.next=o,Gp(e)):(o.next=l.next,l.next=o),e.interleaved=o;return}}catch(u){}finally{}n=BE(t,e,o,r),n!==null&&(o=yt(),Tn(n,t,r,o),uw(n,e,r))}}function aw(t){var e=t.alternate;return t===be||e!==null&&e===be}function lw(t,e){Ra=Fc=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function uw(t,e,n){if(n&4194240){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,Np(t,n)}}var _c={readContext:en,useCallback:lt,useContext:lt,useEffect:lt,useImperativeHandle:lt,useInsertionEffect:lt,useLayoutEffect:lt,useMemo:lt,useReducer:lt,useRef:lt,useState:lt,useDebugValue:lt,useDeferredValue:lt,useTransition:lt,useMutableSource:lt,useSyncExternalStore:lt,useId:lt,unstable_isNewReconciler:!1},xk={readContext:en,useCallback:function(t,e){return Wn().memoizedState=[t,e===void 0?null:e],t},useContext:en,useEffect:vv,useImperativeHandle:function(t,e,n){return n=n!=null?n.concat([t]):null,oc(4194308,4,tw.bind(null,e,t),n)},useLayoutEffect:function(t,e){return oc(4194308,4,t,e)},useInsertionEffect:function(t,e){return oc(4,2,t,e)},useMemo:function(t,e){var n=Wn();return e=e===void 0?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var r=Wn();return e=n!==void 0?n(e):e,r.memoizedState=r.baseState=e,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:e},r.queue=t,t=t.dispatch=Sk.bind(null,be,t),[r.memoizedState,t]},useRef:function(t){var e=Wn();return t={current:t},e.memoizedState=t},useState:Dv,useDebugValue:th,useDeferredValue:function(t){return Wn().memoizedState=t},useTransition:function(){var t=Dv(!1),e=t[0];return t=wk.bind(null,t[1]),Wn().memoizedState=t,[e,t]},useMutableSource:function(){},useSyncExternalStore:function(t,e,n){var r=be,o=Wn();if(xe){if(n===void 0)throw Error(I(407));n=n()}else{if(n=e(),Xe===null)throw Error(I(349));Mo&30||YE(r,e,n)}o.memoizedState=n;var i={value:n,getSnapshot:e};return o.queue=i,vv(qE.bind(null,r,i,t),[t]),r.flags|=2048,Ka(9,ZE.bind(null,r,i,n,e),void 0,null),n},useId:function(){var t=Wn(),e=Xe.identifierPrefix;if(xe){var n=sr,r=ir;n=(r&~(1<<32-xn(r)-1)).toString(32)+n,e=":"+e+"R"+n,n=Za++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=Ek++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},unstable_isNewReconciler:!1},Tk={readContext:en,useCallback:rw,useContext:en,useEffect:eh,useImperativeHandle:nw,useInsertionEffect:XE,useLayoutEffect:ew,useMemo:ow,useReducer:Fm,useRef:JE,useState:function(){return Fm(qa)},useDebugValue:th,useDeferredValue:function(t){var e=tn();return iw(e,Ge.memoizedState,t)},useTransition:function(){var t=Fm(qa)[0],e=tn().memoizedState;return[t,e]},useMutableSource:$E,useSyncExternalStore:GE,useId:sw,unstable_isNewReconciler:!1},Fk={readContext:en,useCallback:rw,useContext:en,useEffect:eh,useImperativeHandle:nw,useInsertionEffect:XE,useLayoutEffect:ew,useMemo:ow,useReducer:_m,useRef:JE,useState:function(){return _m(qa)},useDebugValue:th,useDeferredValue:function(t){var e=tn();return Ge===null?e.memoizedState=t:iw(e,Ge.memoizedState,t)},useTransition:function(){var t=_m(qa)[0],e=tn().memoizedState;return[t,e]},useMutableSource:$E,useSyncExternalStore:GE,useId:sw,unstable_isNewReconciler:!1};function Qi(t,e){try{var n="",r=e;do n+=n_(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:t,source:e,stack:o,digest:null}}function km(t,e,n){return{value:t,source:null,stack:n!=null?n:null,digest:e!=null?e:null}}function cp(t,e){try{console.error(e.value)}catch(n){setTimeout(function(){throw n})}}var _k=typeof WeakMap=="function"?WeakMap:Map;function cw(t,e,n){n=ar(-1,n),n.tag=3,n.payload={element:null};var r=e.value;return n.callback=function(){Oc||(Oc=!0,Ep=r),cp(t,e)},n}function fw(t,e,n){n=ar(-1,n),n.tag=3;var r=t.type.getDerivedStateFromError;if(typeof r=="function"){var o=e.value;n.payload=function(){return r(o)},n.callback=function(){cp(t,e)}}var i=t.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){cp(t,e),typeof r!="function"&&(Yr===null?Yr=new Set([this]):Yr.add(this));var s=e.stack;this.componentDidCatch(e.value,{componentStack:s!==null?s:""})}),n}function Ev(t,e,n){var r=t.pingCache;if(r===null){r=t.pingCache=new _k;var o=new Set;r.set(e,o)}else o=r.get(e),o===void 0&&(o=new Set,r.set(e,o));o.has(n)||(o.add(n),t=Hk.bind(null,t,e,n),e.then(t,t))}function wv(t){do{var e;if((e=t.tag===13)&&(e=t.memoizedState,e=e!==null?e.dehydrated!==null:!0),e)return t;t=t.return}while(t!==null);return null}function Sv(t,e,n,r,o){return t.mode&1?(t.flags|=65536,t.lanes=o,t):(t===e?t.flags|=65536:(t.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(e=ar(-1,1),e.tag=2,Gr(n,e,1))),n.lanes|=1),t)}var kk=dr.ReactCurrentOwner,xt=!1;function ht(t,e,n,r){e.child=t===null?UE(e,null,n,r):qi(e,t.child,n,r)}function Cv(t,e,n,r,o){n=n.render;var i=e.ref;return ji(e,o),r=Jp(t,e,n,r,i,o),n=Xp(),t!==null&&!xt?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~o,fr(t,e,o)):(xe&&n&&Wp(e),e.flags|=1,ht(t,e,r,o),e.child)}function xv(t,e,n,r,o){if(t===null){var i=n.type;return typeof i=="function"&&!uh(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(e.tag=15,e.type=i,dw(t,e,i,r,o)):(t=lc(n.type,null,r,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(i=t.child,!(t.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:za,n(s,r)&&t.ref===e.ref)return fr(t,e,o)}return e.flags|=1,t=qr(i,r),t.ref=e.ref,t.return=e,e.child=t}function dw(t,e,n,r,o){if(t!==null){var i=t.memoizedProps;if(za(i,r)&&t.ref===e.ref)if(xt=!1,e.pendingProps=r=i,(t.lanes&o)!==0)t.flags&131072&&(xt=!0);else return e.lanes=t.lanes,fr(t,e,o)}return fp(t,e,n,r,o)}function mw(t,e,n){var r=e.pendingProps,o=r.children,i=t!==null?t.memoizedState:null;if(r.mode==="hidden")if(!(e.mode&1))e.memoizedState={baseLanes:0,cachePool:null,transitions:null},ve(Vi,At),At|=n;else{if(!(n&1073741824))return t=i!==null?i.baseLanes|n:n,e.lanes=e.childLanes=1073741824,e.memoizedState={baseLanes:t,cachePool:null,transitions:null},e.updateQueue=null,ve(Vi,At),At|=t,null;e.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,ve(Vi,At),At|=r}else i!==null?(r=i.baseLanes|n,e.memoizedState=null):r=n,ve(Vi,At),At|=r;return ht(t,e,o,n),e.child}function pw(t,e){var n=e.ref;(t===null&&n!==null||t!==null&&t.ref!==n)&&(e.flags|=512,e.flags|=2097152)}function fp(t,e,n,r,o){var i=Ft(n)?Ro:ft.current;return i=Yi(e,i),ji(e,o),n=Jp(t,e,n,r,i,o),r=Xp(),t!==null&&!xt?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~o,fr(t,e,o)):(xe&&r&&Wp(e),e.flags|=1,ht(t,e,n,o),e.child)}function Tv(t,e,n,r,o){if(Ft(n)){var i=!0;vc(e)}else i=!1;if(ji(e,o),e.stateNode===null)ic(t,e),HE(e,n,r),up(e,n,r,o),r=!0;else if(t===null){var s=e.stateNode,a=e.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=en(u):(u=Ft(n)?Ro:ft.current,u=Yi(e,u));var f=n.getDerivedStateFromProps,d=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&yv(e,s,r,u),Pr=!1;var m=e.memoizedState;s.state=m,xc(e,r,s,o),l=e.memoizedState,a!==r||m!==l||Tt.current||Pr?(typeof f=="function"&&(lp(e,n,f,r),l=e.memoizedState),(a=Pr||hv(e,n,a,r,m,l,u))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(e.flags|=4194308)):(typeof s.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{s=e.stateNode,VE(t,e),a=e.memoizedProps,u=e.type===e.elementType?a:wn(e.type,a),s.props=u,d=e.pendingProps,m=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=en(l):(l=Ft(n)?Ro:ft.current,l=Yi(e,l));var c=n.getDerivedStateFromProps;(f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==d||m!==l)&&yv(e,s,r,l),Pr=!1,m=e.memoizedState,s.state=m,xc(e,r,s,o);var y=e.memoizedState;a!==d||m!==y||Tt.current||Pr?(typeof c=="function"&&(lp(e,n,c,r),y=e.memoizedState),(u=Pr||hv(e,n,u,r,m,y,l)||!1)?(f||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,l)),typeof s.componentDidUpdate=="function"&&(e.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===t.memoizedProps&&m===t.memoizedState||(e.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&m===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=y),s.props=r,s.state=y,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===t.memoizedProps&&m===t.memoizedState||(e.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&m===t.memoizedState||(e.flags|=1024),r=!1)}return dp(t,e,n,r,i,o)}function dp(t,e,n,r,o,i){pw(t,e);var s=(e.flags&128)!==0;if(!r&&!s)return o&&cv(e,n,!1),fr(t,e,i);r=e.stateNode,kk.current=e;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return e.flags|=1,t!==null&&s?(e.child=qi(e,t.child,null,i),e.child=qi(e,null,a,i)):ht(t,e,a,i),e.memoizedState=r.state,o&&cv(e,n,!0),e.child}function hw(t){var e=t.stateNode;e.pendingContext?uv(t,e.pendingContext,e.pendingContext!==e.context):e.context&&uv(t,e.context,!1),Zp(t,e.containerInfo)}function Fv(t,e,n,r,o){return Zi(),zp(o),e.flags|=256,ht(t,e,n,r),e.child}var mp={dehydrated:null,treeContext:null,retryLane:0};function pp(t){return{baseLanes:t,cachePool:null,transitions:null}}function yw(t,e,n){var r=e.pendingProps,o=Oe.current,i=!1,s=(e.flags&128)!==0,a;if((a=s)||(a=t!==null&&t.memoizedState===null?!1:(o&2)!==0),a?(i=!0,e.flags&=-129):(t===null||t.memoizedState!==null)&&(o|=1),ve(Oe,o&1),t===null)return sp(e),t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(e.mode&1?t.data==="$!"?e.lanes=8:e.lanes=1073741824:e.lanes=1,null):(s=r.children,t=r.fallback,i?(r=e.mode,i=e.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=Uc(s,r,0,null),t=bo(t,r,n,null),i.return=e,t.return=e,i.sibling=t,e.child=i,e.child.memoizedState=pp(n),e.memoizedState=mp,t):nh(e,s));if(o=t.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return Ok(t,e,s,r,a,o,n);if(i){i=r.fallback,s=e.mode,o=t.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&e.child!==o?(r=e.child,r.childLanes=0,r.pendingProps=l,e.deletions=null):(r=qr(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?i=qr(a,i):(i=bo(i,s,n,null),i.flags|=2),i.return=e,r.return=e,r.sibling=i,e.child=r,r=i,i=e.child,s=t.child.memoizedState,s=s===null?pp(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=t.childLanes&~n,e.memoizedState=mp,r}return i=t.child,t=i.sibling,r=qr(i,{mode:"visible",children:r.children}),!(e.mode&1)&&(r.lanes=n),r.return=e,r.sibling=null,t!==null&&(n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)),e.child=r,e.memoizedState=null,r}function nh(t,e){return e=Uc({mode:"visible",children:e},t.mode,0,null),e.return=t,t.child=e}function Ku(t,e,n,r){return r!==null&&zp(r),qi(e,t.child,null,n),t=nh(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Ok(t,e,n,r,o,i,s){if(n)return e.flags&256?(e.flags&=-257,r=km(Error(I(422))),Ku(t,e,s,r)):e.memoizedState!==null?(e.child=t.child,e.flags|=128,null):(i=r.fallback,o=e.mode,r=Uc({mode:"visible",children:r.children},o,0,null),i=bo(i,o,s,null),i.flags|=2,r.return=e,i.return=e,r.sibling=i,e.child=r,e.mode&1&&qi(e,t.child,null,s),e.child.memoizedState=pp(s),e.memoizedState=mp,i);if(!(e.mode&1))return Ku(t,e,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(I(419)),r=km(i,r,void 0),Ku(t,e,s,r)}if(a=(s&t.childLanes)!==0,xt||a){if(r=Xe,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,cr(t,o),Tn(r,t,o,-1))}return lh(),r=km(Error(I(421))),Ku(t,e,s,r)}return o.data==="$?"?(e.flags|=128,e.child=t.child,e=zk.bind(null,t),o._reactRetry=e,null):(t=i.treeContext,Lt=$r(o.nextSibling),Pt=e,xe=!0,Cn=null,t!==null&&(Kt[Qt++]=ir,Kt[Qt++]=sr,Kt[Qt++]=No,ir=t.id,sr=t.overflow,No=e),e=nh(e,r.children),e.flags|=4096,e)}function _v(t,e,n){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),ap(t.return,e,n)}function Om(t,e,n,r,o){var i=t.memoizedState;i===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=e,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function gw(t,e,n){var r=e.pendingProps,o=r.revealOrder,i=r.tail;if(ht(t,e,r.children,n),r=Oe.current,r&2)r=r&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&_v(t,n,e);else if(t.tag===19)_v(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}if(ve(Oe,r),!(e.mode&1))e.memoizedState=null;else switch(o){case"forwards":for(n=e.child,o=null;n!==null;)t=n.alternate,t!==null&&Tc(t)===null&&(o=n),n=n.sibling;n=o,n===null?(o=e.child,e.child=null):(o=n.sibling,n.sibling=null),Om(e,!1,o,n,i);break;case"backwards":for(n=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&Tc(t)===null){e.child=o;break}t=o.sibling,o.sibling=n,n=o,o=t}Om(e,!0,n,null,i);break;case"together":Om(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function ic(t,e){!(e.mode&1)&&t!==null&&(t.alternate=null,e.alternate=null,e.flags|=2)}function fr(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Io|=e.lanes,!(n&e.childLanes))return null;if(t!==null&&e.child!==t.child)throw Error(I(153));if(e.child!==null){for(t=e.child,n=qr(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=qr(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function bk(t,e,n){switch(e.tag){case 3:hw(e),Zi();break;case 5:jE(e);break;case 1:Ft(e.type)&&vc(e);break;case 4:Zp(e,e.stateNode.containerInfo);break;case 10:var r=e.type._context,o=e.memoizedProps.value;ve(Sc,r._currentValue),r._currentValue=o;break;case 13:if(r=e.memoizedState,r!==null)return r.dehydrated!==null?(ve(Oe,Oe.current&1),e.flags|=128,null):n&e.child.childLanes?yw(t,e,n):(ve(Oe,Oe.current&1),t=fr(t,e,n),t!==null?t.sibling:null);ve(Oe,Oe.current&1);break;case 19:if(r=(n&e.childLanes)!==0,t.flags&128){if(r)return gw(t,e,n);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ve(Oe,Oe.current),r)break;return null;case 22:case 23:return e.lanes=0,mw(t,e,n)}return fr(t,e,n)}var Dw,hp,vw,Ew;Dw=function(t,e){for(var n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};hp=function(){};vw=function(t,e,n,r){var o=t.memoizedProps;if(o!==r){t=e.stateNode,ko(Un.current);var i=null;switch(n){case"input":o=Pm(t,o),r=Pm(t,r),i=[];break;case"select":o=Re({},o,{value:void 0}),r=Re({},r,{value:void 0}),i=[];break;case"textarea":o=Wm(t,o),r=Wm(t,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(t.onclick=gc)}zm(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Aa.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Aa.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&Ee("scroll",t),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(e.updateQueue=u)&&(e.flags|=4)}};Ew=function(t,e,n,r){n!==r&&(e.flags|=4)};function va(t,e){if(!xe)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function ut(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,r=0;if(e)for(var o=t.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=r,t.childLanes=n,e}function Rk(t,e,n){var r=e.pendingProps;switch(Hp(e),e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ut(e),null;case 1:return Ft(e.type)&&Dc(),ut(e),null;case 3:return r=e.stateNode,Ki(),we(Tt),we(ft),Kp(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(t===null||t.child===null)&&(Zu(e)?e.flags|=4:t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,Cn!==null&&(Cp(Cn),Cn=null))),hp(t,e),ut(e),null;case 5:qp(e);var o=ko(Ya.current);if(n=e.type,t!==null&&e.stateNode!=null)vw(t,e,n,r,o),t.ref!==e.ref&&(e.flags|=512,e.flags|=2097152);else{if(!r){if(e.stateNode===null)throw Error(I(166));return ut(e),null}if(t=ko(Un.current),Zu(e)){r=e.stateNode,n=e.type;var i=e.memoizedProps;switch(r[Hn]=e,r[$a]=i,t=(e.mode&1)!==0,n){case"dialog":Ee("cancel",r),Ee("close",r);break;case"iframe":case"object":case"embed":Ee("load",r);break;case"video":case"audio":for(o=0;o<Ta.length;o++)Ee(Ta[o],r);break;case"source":Ee("error",r);break;case"img":case"image":case"link":Ee("error",r),Ee("load",r);break;case"details":Ee("toggle",r);break;case"input":AD(r,i),Ee("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Ee("invalid",r);break;case"textarea":PD(r,i),Ee("invalid",r)}zm(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Yu(r.textContent,a,t),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Yu(r.textContent,a,t),o=["children",""+a]):Aa.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&Ee("scroll",r)}switch(n){case"input":Au(r),LD(r,i,!0);break;case"textarea":Au(r),BD(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=gc)}r=o,e.updateQueue=r,r!==null&&(e.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=Yv(n)),t==="http://www.w3.org/1999/xhtml"?n==="script"?(t=s.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof r.is=="string"?t=s.createElement(n,{is:r.is}):(t=s.createElement(n),n==="select"&&(s=t,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):t=s.createElementNS(t,n),t[Hn]=e,t[$a]=r,Dw(t,e,!1,!1),e.stateNode=t;e:{switch(s=Um(n,r),n){case"dialog":Ee("cancel",t),Ee("close",t),o=r;break;case"iframe":case"object":case"embed":Ee("load",t),o=r;break;case"video":case"audio":for(o=0;o<Ta.length;o++)Ee(Ta[o],t);o=r;break;case"source":Ee("error",t),o=r;break;case"img":case"image":case"link":Ee("error",t),Ee("load",t),o=r;break;case"details":Ee("toggle",t),o=r;break;case"input":AD(t,r),o=Pm(t,r),Ee("invalid",t);break;case"option":o=r;break;case"select":t._wrapperState={wasMultiple:!!r.multiple},o=Re({},r,{value:void 0}),Ee("invalid",t);break;case"textarea":PD(t,r),o=Wm(t,r),Ee("invalid",t);break;default:o=r}zm(n,o),a=o;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?Kv(t,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Zv(t,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&La(t,l):typeof l=="number"&&La(t,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Aa.hasOwnProperty(i)?l!=null&&i==="onScroll"&&Ee("scroll",t):l!=null&&Fp(t,i,l,s))}switch(n){case"input":Au(t),LD(t,r,!1);break;case"textarea":Au(t),BD(t);break;case"option":r.value!=null&&t.setAttribute("value",""+Kr(r.value));break;case"select":t.multiple=!!r.multiple,i=r.value,i!=null?Wi(t,!!r.multiple,i,!1):r.defaultValue!=null&&Wi(t,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(t.onclick=gc)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(e.flags|=4)}e.ref!==null&&(e.flags|=512,e.flags|=2097152)}return ut(e),null;case 6:if(t&&e.stateNode!=null)Ew(t,e,t.memoizedProps,r);else{if(typeof r!="string"&&e.stateNode===null)throw Error(I(166));if(n=ko(Ya.current),ko(Un.current),Zu(e)){if(r=e.stateNode,n=e.memoizedProps,r[Hn]=e,(i=r.nodeValue!==n)&&(t=Pt,t!==null))switch(t.tag){case 3:Yu(r.nodeValue,n,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&Yu(r.nodeValue,n,(t.mode&1)!==0)}i&&(e.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Hn]=e,e.stateNode=r}return ut(e),null;case 13:if(we(Oe),r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(xe&&Lt!==null&&e.mode&1&&!(e.flags&128))PE(),Zi(),e.flags|=98560,i=!1;else if(i=Zu(e),r!==null&&r.dehydrated!==null){if(t===null){if(!i)throw Error(I(318));if(i=e.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(I(317));i[Hn]=e}else Zi(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;ut(e),i=!1}else Cn!==null&&(Cp(Cn),Cn=null),i=!0;if(!i)return e.flags&65536?e:null}return e.flags&128?(e.lanes=n,e):(r=r!==null,r!==(t!==null&&t.memoizedState!==null)&&r&&(e.child.flags|=8192,e.mode&1&&(t===null||Oe.current&1?Ye===0&&(Ye=3):lh())),e.updateQueue!==null&&(e.flags|=4),ut(e),null);case 4:return Ki(),hp(t,e),t===null&&Ua(e.stateNode.containerInfo),ut(e),null;case 10:return $p(e.type._context),ut(e),null;case 17:return Ft(e.type)&&Dc(),ut(e),null;case 19:if(we(Oe),i=e.memoizedState,i===null)return ut(e),null;if(r=(e.flags&128)!==0,s=i.rendering,s===null)if(r)va(i,!1);else{if(Ye!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(s=Tc(t),s!==null){for(e.flags|=128,va(i,!1),r=s.updateQueue,r!==null&&(e.updateQueue=r,e.flags|=4),e.subtreeFlags=0,r=n,n=e.child;n!==null;)i=n,t=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=t,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,t=s.dependencies,i.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),n=n.sibling;return ve(Oe,Oe.current&1|2),e.child}t=t.sibling}i.tail!==null&&Le()>Ji&&(e.flags|=128,r=!0,va(i,!1),e.lanes=4194304)}else{if(!r)if(t=Tc(s),t!==null){if(e.flags|=128,r=!0,n=t.updateQueue,n!==null&&(e.updateQueue=n,e.flags|=4),va(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!xe)return ut(e),null}else 2*Le()-i.renderingStartTime>Ji&&n!==1073741824&&(e.flags|=128,r=!0,va(i,!1),e.lanes=4194304);i.isBackwards?(s.sibling=e.child,e.child=s):(n=i.last,n!==null?n.sibling=s:e.child=s,i.last=s)}return i.tail!==null?(e=i.tail,i.rendering=e,i.tail=e.sibling,i.renderingStartTime=Le(),e.sibling=null,n=Oe.current,ve(Oe,r?n&1|2:n&1),e):(ut(e),null);case 22:case 23:return ah(),r=e.memoizedState!==null,t!==null&&t.memoizedState!==null!==r&&(e.flags|=8192),r&&e.mode&1?At&1073741824&&(ut(e),e.subtreeFlags&6&&(e.flags|=8192)):ut(e),null;case 24:return null;case 25:return null}throw Error(I(156,e.tag))}function Nk(t,e){switch(Hp(e),e.tag){case 1:return Ft(e.type)&&Dc(),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Ki(),we(Tt),we(ft),Kp(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 5:return qp(e),null;case 13:if(we(Oe),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(I(340));Zi()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return we(Oe),null;case 4:return Ki(),null;case 10:return $p(e.type._context),null;case 22:case 23:return ah(),null;case 24:return null;default:return null}}var Qu=!1,ct=!1,Mk=typeof WeakSet=="function"?WeakSet:Set,Y=null;function Bi(t,e){var n=t.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Ae(t,e,r)}else n.current=null}function yp(t,e,n){try{n()}catch(r){Ae(t,e,r)}}var kv=!1;function Ik(t,e){if(Xm=pc,t=xE(),Vp(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else e:{n=(n=t.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(D){n=null;break e}var s=0,a=-1,l=-1,u=0,f=0,d=t,m=null;t:for(;;){for(var c;d!==n||o!==0&&d.nodeType!==3||(a=s+o),d!==i||r!==0&&d.nodeType!==3||(l=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(c=d.firstChild)!==null;)m=d,d=c;for(;;){if(d===t)break t;if(m===n&&++u===o&&(a=s),m===i&&++f===r&&(l=s),(c=d.nextSibling)!==null)break;d=m,m=d.parentNode}d=c}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(ep={focusedElem:t,selectionRange:n},pc=!1,Y=e;Y!==null;)if(e=Y,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,Y=t;else for(;Y!==null;){e=Y;try{var y=e.alternate;if(e.flags&1024)switch(e.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,S=y.memoizedState,p=e.stateNode,h=p.getSnapshotBeforeUpdate(e.elementType===e.type?v:wn(e.type,v),S);p.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var E=e.stateNode.containerInfo;E.nodeType===1?E.textContent="":E.nodeType===9&&E.documentElement&&E.removeChild(E.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(I(163))}}catch(D){Ae(e,e.return,D)}if(t=e.sibling,t!==null){t.return=e.return,Y=t;break}Y=e.return}return y=kv,kv=!1,y}function Na(t,e,n){var r=e.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&t)===t){var i=o.destroy;o.destroy=void 0,i!==void 0&&yp(e,n,i)}o=o.next}while(o!==r)}}function Hc(t,e){if(e=e.updateQueue,e=e!==null?e.lastEffect:null,e!==null){var n=e=e.next;do{if((n.tag&t)===t){var r=n.create;n.destroy=r()}n=n.next}while(n!==e)}}function gp(t){var e=t.ref;if(e!==null){var n=t.stateNode;switch(t.tag){case 5:t=n;break;default:t=n}typeof e=="function"?e(t):e.current=t}}function ww(t){var e=t.alternate;e!==null&&(t.alternate=null,ww(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&(delete e[Hn],delete e[$a],delete e[rp],delete e[yk],delete e[gk])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function Sw(t){return t.tag===5||t.tag===3||t.tag===4}function Ov(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||Sw(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Dp(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=gc));else if(r!==4&&(t=t.child,t!==null))for(Dp(t,e,n),t=t.sibling;t!==null;)Dp(t,e,n),t=t.sibling}function vp(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(r!==4&&(t=t.child,t!==null))for(vp(t,e,n),t=t.sibling;t!==null;)vp(t,e,n),t=t.sibling}var ot=null,Sn=!1;function Ar(t,e,n){for(n=n.child;n!==null;)Cw(t,e,n),n=n.sibling}function Cw(t,e,n){if(zn&&typeof zn.onCommitFiberUnmount=="function")try{zn.onCommitFiberUnmount(Mc,n)}catch(a){}switch(n.tag){case 5:ct||Bi(n,e);case 6:var r=ot,o=Sn;ot=null,Ar(t,e,n),ot=r,Sn=o,ot!==null&&(Sn?(t=ot,n=n.stateNode,t.nodeType===8?t.parentNode.removeChild(n):t.removeChild(n)):ot.removeChild(n.stateNode));break;case 18:ot!==null&&(Sn?(t=ot,n=n.stateNode,t.nodeType===8?Sm(t.parentNode,n):t.nodeType===1&&Sm(t,n),Wa(t)):Sm(ot,n.stateNode));break;case 4:r=ot,o=Sn,ot=n.stateNode.containerInfo,Sn=!0,Ar(t,e,n),ot=r,Sn=o;break;case 0:case 11:case 14:case 15:if(!ct&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&yp(n,e,s),o=o.next}while(o!==r)}Ar(t,e,n);break;case 1:if(!ct&&(Bi(n,e),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){Ae(n,e,a)}Ar(t,e,n);break;case 21:Ar(t,e,n);break;case 22:n.mode&1?(ct=(r=ct)||n.memoizedState!==null,Ar(t,e,n),ct=r):Ar(t,e,n);break;default:Ar(t,e,n)}}function bv(t){var e=t.updateQueue;if(e!==null){t.updateQueue=null;var n=t.stateNode;n===null&&(n=t.stateNode=new Mk),e.forEach(function(r){var o=Uk.bind(null,t,r);n.has(r)||(n.add(r),r.then(o,o))})}}function En(t,e){var n=e.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=t,s=e,a=s;e:for(;a!==null;){switch(a.tag){case 5:ot=a.stateNode,Sn=!1;break e;case 3:ot=a.stateNode.containerInfo,Sn=!0;break e;case 4:ot=a.stateNode.containerInfo,Sn=!0;break e}a=a.return}if(ot===null)throw Error(I(160));Cw(i,s,o),ot=null,Sn=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){Ae(o,e,u)}}if(e.subtreeFlags&12854)for(e=e.child;e!==null;)xw(e,t),e=e.sibling}function xw(t,e){var n=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(En(e,t),Vn(t),r&4){try{Na(3,t,t.return),Hc(3,t)}catch(v){Ae(t,t.return,v)}try{Na(5,t,t.return)}catch(v){Ae(t,t.return,v)}}break;case 1:En(e,t),Vn(t),r&512&&n!==null&&Bi(n,n.return);break;case 5:if(En(e,t),Vn(t),r&512&&n!==null&&Bi(n,n.return),t.flags&32){var o=t.stateNode;try{La(o,"")}catch(v){Ae(t,t.return,v)}}if(r&4&&(o=t.stateNode,o!=null)){var i=t.memoizedProps,s=n!==null?n.memoizedProps:i,a=t.type,l=t.updateQueue;if(t.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&$v(o,i),Um(a,s);var u=Um(a,i);for(s=0;s<l.length;s+=2){var f=l[s],d=l[s+1];f==="style"?Kv(o,d):f==="dangerouslySetInnerHTML"?Zv(o,d):f==="children"?La(o,d):Fp(o,f,d,u)}switch(a){case"input":Bm(o,i);break;case"textarea":Gv(o,i);break;case"select":var m=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var c=i.value;c!=null?Wi(o,!!i.multiple,c,!1):m!==!!i.multiple&&(i.defaultValue!=null?Wi(o,!!i.multiple,i.defaultValue,!0):Wi(o,!!i.multiple,i.multiple?[]:"",!1))}o[$a]=i}catch(v){Ae(t,t.return,v)}}break;case 6:if(En(e,t),Vn(t),r&4){if(t.stateNode===null)throw Error(I(162));o=t.stateNode,i=t.memoizedProps;try{o.nodeValue=i}catch(v){Ae(t,t.return,v)}}break;case 3:if(En(e,t),Vn(t),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Wa(e.containerInfo)}catch(v){Ae(t,t.return,v)}break;case 4:En(e,t),Vn(t);break;case 13:En(e,t),Vn(t),o=t.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(ih=Le())),r&4&&bv(t);break;case 22:if(f=n!==null&&n.memoizedState!==null,t.mode&1?(ct=(u=ct)||f,En(e,t),ct=u):En(e,t),Vn(t),r&8192){if(u=t.memoizedState!==null,(t.stateNode.isHidden=u)&&!f&&t.mode&1)for(Y=t,f=t.child;f!==null;){for(d=Y=f;Y!==null;){switch(m=Y,c=m.child,m.tag){case 0:case 11:case 14:case 15:Na(4,m,m.return);break;case 1:Bi(m,m.return);var y=m.stateNode;if(typeof y.componentWillUnmount=="function"){r=m,n=m.return;try{e=r,y.props=e.memoizedProps,y.state=e.memoizedState,y.componentWillUnmount()}catch(v){Ae(r,n,v)}}break;case 5:Bi(m,m.return);break;case 22:if(m.memoizedState!==null){Nv(d);continue}}c!==null?(c.return=m,Y=c):Nv(d)}f=f.sibling}e:for(f=null,d=t;;){if(d.tag===5){if(f===null){f=d;try{o=d.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=d.stateNode,l=d.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=qv("display",s))}catch(v){Ae(t,t.return,v)}}}else if(d.tag===6){if(f===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(v){Ae(t,t.return,v)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===t)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===t)break e;for(;d.sibling===null;){if(d.return===null||d.return===t)break e;f===d&&(f=null),d=d.return}f===d&&(f=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:En(e,t),Vn(t),r&4&&bv(t);break;case 21:break;default:En(e,t),Vn(t)}}function Vn(t){var e=t.flags;if(e&2){try{e:{for(var n=t.return;n!==null;){if(Sw(n)){var r=n;break e}n=n.return}throw Error(I(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(La(o,""),r.flags&=-33);var i=Ov(t);vp(t,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,a=Ov(t);Dp(t,a,s);break;default:throw Error(I(161))}}catch(l){Ae(t,t.return,l)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Ak(t,e,n){Y=t,Tw(t,e,n)}function Tw(t,e,n){for(var r=(t.mode&1)!==0;Y!==null;){var o=Y,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||Qu;if(!s){var a=o.alternate,l=a!==null&&a.memoizedState!==null||ct;a=Qu;var u=ct;if(Qu=s,(ct=l)&&!u)for(Y=o;Y!==null;)s=Y,l=s.child,s.tag===22&&s.memoizedState!==null?Mv(o):l!==null?(l.return=s,Y=l):Mv(o);for(;i!==null;)Y=i,Tw(i,e,n),i=i.sibling;Y=o,Qu=a,ct=u}Rv(t,e,n)}else o.subtreeFlags&8772&&i!==null?(i.return=o,Y=i):Rv(t,e,n)}}function Rv(t){for(;Y!==null;){var e=Y;if(e.flags&8772){var n=e.alternate;try{if(e.flags&8772)switch(e.tag){case 0:case 11:case 15:ct||Hc(5,e);break;case 1:var r=e.stateNode;if(e.flags&4&&!ct)if(n===null)r.componentDidMount();else{var o=e.elementType===e.type?n.memoizedProps:wn(e.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=e.updateQueue;i!==null&&pv(e,i,r);break;case 3:var s=e.updateQueue;if(s!==null){if(n=null,e.child!==null)switch(e.child.tag){case 5:n=e.child.stateNode;break;case 1:n=e.child.stateNode}pv(e,s,n)}break;case 5:var a=e.stateNode;if(n===null&&e.flags&4){n=a;var l=e.memoizedProps;switch(e.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(e.memoizedState===null){var u=e.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var d=f.dehydrated;d!==null&&Wa(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(I(163))}ct||e.flags&512&&gp(e)}catch(m){Ae(e,e.return,m)}}if(e===t){Y=null;break}if(n=e.sibling,n!==null){n.return=e.return,Y=n;break}Y=e.return}}function Nv(t){for(;Y!==null;){var e=Y;if(e===t){Y=null;break}var n=e.sibling;if(n!==null){n.return=e.return,Y=n;break}Y=e.return}}function Mv(t){for(;Y!==null;){var e=Y;try{switch(e.tag){case 0:case 11:case 15:var n=e.return;try{Hc(4,e)}catch(l){Ae(e,n,l)}break;case 1:var r=e.stateNode;if(typeof r.componentDidMount=="function"){var o=e.return;try{r.componentDidMount()}catch(l){Ae(e,o,l)}}var i=e.return;try{gp(e)}catch(l){Ae(e,i,l)}break;case 5:var s=e.return;try{gp(e)}catch(l){Ae(e,s,l)}}}catch(l){Ae(e,e.return,l)}if(e===t){Y=null;break}var a=e.sibling;if(a!==null){a.return=e.return,Y=a;break}Y=e.return}}var Lk=Math.ceil,kc=dr.ReactCurrentDispatcher,rh=dr.ReactCurrentOwner,Xt=dr.ReactCurrentBatchConfig,ce=0,Xe=null,He=null,it=0,At=0,Vi=Xr(0),Ye=0,Qa=null,Io=0,zc=0,oh=0,Ma=null,Ct=null,ih=0,Ji=1/0,rr=null,Oc=!1,Ep=null,Yr=null,Ju=!1,Hr=null,bc=0,Ia=0,wp=null,sc=-1,ac=0;function yt(){return ce&6?Le():sc!==-1?sc:sc=Le()}function Zr(t){return t.mode&1?ce&2&&it!==0?it&-it:vk.transition!==null?(ac===0&&(ac=lE()),ac):(t=pe,t!==0||(t=window.event,t=t===void 0?16:hE(t.type)),t):1}function Tn(t,e,n,r){if(50<Ia)throw Ia=0,wp=null,Error(I(185));Ja(t,n,r),(!(ce&2)||t!==Xe)&&(t===Xe&&(!(ce&2)&&(zc|=n),Ye===4&&Vr(t,it)),_t(t,r),n===1&&ce===0&&!(e.mode&1)&&(Ji=Le()+500,Bc&&eo()))}function _t(t,e){var n=t.callbackNode;w_(t,e);var r=mc(t,t===Xe?it:0);if(r===0)n!==null&&HD(n),t.callbackNode=null,t.callbackPriority=0;else if(e=r&-r,t.callbackPriority!==e){if(n!=null&&HD(n),e===1)t.tag===0?Dk(Iv.bind(null,t)):IE(Iv.bind(null,t)),pk(function(){!(ce&6)&&eo()}),n=null;else{switch(uE(r)){case 1:n=Rp;break;case 4:n=sE;break;case 16:n=dc;break;case 536870912:n=aE;break;default:n=dc}n=Mw(n,Fw.bind(null,t))}t.callbackPriority=e,t.callbackNode=n}}function Fw(t,e){if(sc=-1,ac=0,ce&6)throw Error(I(327));var n=t.callbackNode;if($i()&&t.callbackNode!==n)return null;var r=mc(t,t===Xe?it:0);if(r===0)return null;if(r&30||r&t.expiredLanes||e)e=Rc(t,r);else{e=r;var o=ce;ce|=2;var i=kw();(Xe!==t||it!==e)&&(rr=null,Ji=Le()+500,Oo(t,e));do try{Vk();break}catch(a){_w(t,a)}while(1);jp(),kc.current=i,ce=o,He!==null?e=0:(Xe=null,it=0,e=Ye)}if(e!==0){if(e===2&&(o=Zm(t),o!==0&&(r=o,e=Sp(t,o))),e===1)throw n=Qa,Oo(t,0),Vr(t,r),_t(t,Le()),n;if(e===6)Vr(t,r);else{if(o=t.current.alternate,!(r&30)&&!Pk(o)&&(e=Rc(t,r),e===2&&(i=Zm(t),i!==0&&(r=i,e=Sp(t,i))),e===1))throw n=Qa,Oo(t,0),Vr(t,r),_t(t,Le()),n;switch(t.finishedWork=o,t.finishedLanes=r,e){case 0:case 1:throw Error(I(345));case 2:To(t,Ct,rr);break;case 3:if(Vr(t,r),(r&130023424)===r&&(e=ih+500-Le(),10<e)){if(mc(t,0)!==0)break;if(o=t.suspendedLanes,(o&r)!==r){yt(),t.pingedLanes|=t.suspendedLanes&o;break}t.timeoutHandle=np(To.bind(null,t,Ct,rr),e);break}To(t,Ct,rr);break;case 4:if(Vr(t,r),(r&4194240)===r)break;for(e=t.eventTimes,o=-1;0<r;){var s=31-xn(r);i=1<<s,s=e[s],s>o&&(o=s),r&=~i}if(r=o,r=Le()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Lk(r/1960))-r,10<r){t.timeoutHandle=np(To.bind(null,t,Ct,rr),r);break}To(t,Ct,rr);break;case 5:To(t,Ct,rr);break;default:throw Error(I(329))}}}return _t(t,Le()),t.callbackNode===n?Fw.bind(null,t):null}function Sp(t,e){var n=Ma;return t.current.memoizedState.isDehydrated&&(Oo(t,e).flags|=256),t=Rc(t,e),t!==2&&(e=Ct,Ct=n,e!==null&&Cp(e)),t}function Cp(t){Ct===null?Ct=t:Ct.push.apply(Ct,t)}function Pk(t){for(var e=t;;){if(e.flags&16384){var n=e.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Fn(i(),o))return!1}catch(s){return!1}}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Vr(t,e){for(e&=~oh,e&=~zc,t.suspendedLanes|=e,t.pingedLanes&=~e,t=t.expirationTimes;0<e;){var n=31-xn(e),r=1<<n;t[n]=-1,e&=~r}}function Iv(t){if(ce&6)throw Error(I(327));$i();var e=mc(t,0);if(!(e&1))return _t(t,Le()),null;var n=Rc(t,e);if(t.tag!==0&&n===2){var r=Zm(t);r!==0&&(e=r,n=Sp(t,r))}if(n===1)throw n=Qa,Oo(t,0),Vr(t,e),_t(t,Le()),n;if(n===6)throw Error(I(345));return t.finishedWork=t.current.alternate,t.finishedLanes=e,To(t,Ct,rr),_t(t,Le()),null}function sh(t,e){var n=ce;ce|=1;try{return t(e)}finally{ce=n,ce===0&&(Ji=Le()+500,Bc&&eo())}}function Ao(t){Hr!==null&&Hr.tag===0&&!(ce&6)&&$i();var e=ce;ce|=1;var n=Xt.transition,r=pe;try{if(Xt.transition=null,pe=1,t)return t()}finally{pe=r,Xt.transition=n,ce=e,!(ce&6)&&eo()}}function ah(){At=Vi.current,we(Vi)}function Oo(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;if(n!==-1&&(t.timeoutHandle=-1,mk(n)),He!==null)for(n=He.return;n!==null;){var r=n;switch(Hp(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Dc();break;case 3:Ki(),we(Tt),we(ft),Kp();break;case 5:qp(r);break;case 4:Ki();break;case 13:we(Oe);break;case 19:we(Oe);break;case 10:$p(r.type._context);break;case 22:case 23:ah()}n=n.return}if(Xe=t,He=t=qr(t.current,null),it=At=e,Ye=0,Qa=null,oh=zc=Io=0,Ct=Ma=null,_o!==null){for(e=0;e<_o.length;e++)if(n=_o[e],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}_o=null}return t}function _w(t,e){do{var n=He;try{if(jp(),rc.current=_c,Fc){for(var r=be.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Fc=!1}if(Mo=0,Je=Ge=be=null,Ra=!1,Za=0,rh.current=null,n===null||n.return===null){Ye=1,Qa=e,He=null;break}e:{var i=t,s=n.return,a=n,l=e;if(e=it,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,f=a,d=f.tag;if(!(f.mode&1)&&(d===0||d===11||d===15)){var m=f.alternate;m?(f.updateQueue=m.updateQueue,f.memoizedState=m.memoizedState,f.lanes=m.lanes):(f.updateQueue=null,f.memoizedState=null)}var c=wv(s);if(c!==null){c.flags&=-257,Sv(c,s,a,i,e),c.mode&1&&Ev(i,u,e),e=c,l=u;var y=e.updateQueue;if(y===null){var v=new Set;v.add(l),e.updateQueue=v}else y.add(l);break e}else{if(!(e&1)){Ev(i,u,e),lh();break e}l=Error(I(426))}}else if(xe&&a.mode&1){var S=wv(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Sv(S,s,a,i,e),zp(Qi(l,a));break e}}i=l=Qi(l,a),Ye!==4&&(Ye=2),Ma===null?Ma=[i]:Ma.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,e&=-e,i.lanes|=e;var p=cw(i,l,e);mv(i,p);break e;case 1:a=l;var h=i.type,E=i.stateNode;if(!(i.flags&128)&&(typeof h.getDerivedStateFromError=="function"||E!==null&&typeof E.componentDidCatch=="function"&&(Yr===null||!Yr.has(E)))){i.flags|=65536,e&=-e,i.lanes|=e;var D=fw(i,a,e);mv(i,D);break e}}i=i.return}while(i!==null)}bw(n)}catch(x){e=x,He===n&&n!==null&&(He=n=n.return);continue}break}while(1)}function kw(){var t=kc.current;return kc.current=_c,t===null?_c:t}function lh(){(Ye===0||Ye===3||Ye===2)&&(Ye=4),Xe===null||!(Io&268435455)&&!(zc&268435455)||Vr(Xe,it)}function Rc(t,e){var n=ce;ce|=2;var r=kw();(Xe!==t||it!==e)&&(rr=null,Oo(t,e));do try{Bk();break}catch(o){_w(t,o)}while(1);if(jp(),ce=n,kc.current=r,He!==null)throw Error(I(261));return Xe=null,it=0,Ye}function Bk(){for(;He!==null;)Ow(He)}function Vk(){for(;He!==null&&!d_();)Ow(He)}function Ow(t){var e=Nw(t.alternate,t,At);t.memoizedProps=t.pendingProps,e===null?bw(t):He=e,rh.current=null}function bw(t){var e=t;do{var n=e.alternate;if(t=e.return,e.flags&32768){if(n=Nk(n,e),n!==null){n.flags&=32767,He=n;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{Ye=6,He=null;return}}else if(n=Rk(n,e,At),n!==null){He=n;return}if(e=e.sibling,e!==null){He=e;return}He=e=t}while(e!==null);Ye===0&&(Ye=5)}function To(t,e,n){var r=pe,o=Xt.transition;try{Xt.transition=null,pe=1,Wk(t,e,n,r)}finally{Xt.transition=o,pe=r}return null}function Wk(t,e,n,r){do $i();while(Hr!==null);if(ce&6)throw Error(I(327));n=t.finishedWork;var o=t.finishedLanes;if(n===null)return null;if(t.finishedWork=null,t.finishedLanes=0,n===t.current)throw Error(I(177));t.callbackNode=null,t.callbackPriority=0;var i=n.lanes|n.childLanes;if(S_(t,i),t===Xe&&(He=Xe=null,it=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ju||(Ju=!0,Mw(dc,function(){return $i(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Xt.transition,Xt.transition=null;var s=pe;pe=1;var a=ce;ce|=4,rh.current=null,Ik(t,n),xw(n,t),lk(ep),pc=!!Xm,ep=Xm=null,t.current=n,Ak(n,t,o),m_(),ce=a,pe=s,Xt.transition=i}else t.current=n;if(Ju&&(Ju=!1,Hr=t,bc=o),i=t.pendingLanes,i===0&&(Yr=null),y_(n.stateNode,r),_t(t,Le()),e!==null)for(r=t.onRecoverableError,n=0;n<e.length;n++)o=e[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Oc)throw Oc=!1,t=Ep,Ep=null,t;return bc&1&&t.tag!==0&&$i(),i=t.pendingLanes,i&1?t===wp?Ia++:(Ia=0,wp=t):Ia=0,eo(),null}function $i(){if(Hr!==null){var t=uE(bc),e=Xt.transition,n=pe;try{if(Xt.transition=null,pe=16>t?16:t,Hr===null)var r=!1;else{if(t=Hr,Hr=null,bc=0,ce&6)throw Error(I(331));var o=ce;for(ce|=4,Y=t.current;Y!==null;){var i=Y,s=i.child;if(Y.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(Y=u;Y!==null;){var f=Y;switch(f.tag){case 0:case 11:case 15:Na(8,f,i)}var d=f.child;if(d!==null)d.return=f,Y=d;else for(;Y!==null;){f=Y;var m=f.sibling,c=f.return;if(ww(f),f===u){Y=null;break}if(m!==null){m.return=c,Y=m;break}Y=c}}}var y=i.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var S=v.sibling;v.sibling=null,v=S}while(v!==null)}}Y=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,Y=s;else e:for(;Y!==null;){if(i=Y,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Na(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,Y=p;break e}Y=i.return}}var h=t.current;for(Y=h;Y!==null;){s=Y;var E=s.child;if(s.subtreeFlags&2064&&E!==null)E.return=s,Y=E;else e:for(s=h;Y!==null;){if(a=Y,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Hc(9,a)}}catch(x){Ae(a,a.return,x)}if(a===s){Y=null;break e}var D=a.sibling;if(D!==null){D.return=a.return,Y=D;break e}Y=a.return}}if(ce=o,eo(),zn&&typeof zn.onPostCommitFiberRoot=="function")try{zn.onPostCommitFiberRoot(Mc,t)}catch(x){}r=!0}return r}finally{pe=n,Xt.transition=e}}return!1}function Av(t,e,n){e=Qi(n,e),e=cw(t,e,1),t=Gr(t,e,1),e=yt(),t!==null&&(Ja(t,1,e),_t(t,e))}function Ae(t,e,n){if(t.tag===3)Av(t,t,n);else for(;e!==null;){if(e.tag===3){Av(e,t,n);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Yr===null||!Yr.has(r))){t=Qi(n,t),t=fw(e,t,1),e=Gr(e,t,1),t=yt(),e!==null&&(Ja(e,1,t),_t(e,t));break}}e=e.return}}function Hk(t,e,n){var r=t.pingCache;r!==null&&r.delete(e),e=yt(),t.pingedLanes|=t.suspendedLanes&n,Xe===t&&(it&n)===n&&(Ye===4||Ye===3&&(it&130023424)===it&&500>Le()-ih?Oo(t,0):oh|=n),_t(t,e)}function Rw(t,e){e===0&&(t.mode&1?(e=Bu,Bu<<=1,!(Bu&130023424)&&(Bu=4194304)):e=1);var n=yt();t=cr(t,e),t!==null&&(Ja(t,e,n),_t(t,n))}function zk(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Rw(t,n)}function Uk(t,e){var n=0;switch(t.tag){case 13:var r=t.stateNode,o=t.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=t.stateNode;break;default:throw Error(I(314))}r!==null&&r.delete(e),Rw(t,n)}var Nw;Nw=function(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps||Tt.current)xt=!0;else{if(!(t.lanes&n)&&!(e.flags&128))return xt=!1,bk(t,e,n);xt=!!(t.flags&131072)}else xt=!1,xe&&e.flags&1048576&&AE(e,wc,e.index);switch(e.lanes=0,e.tag){case 2:var r=e.type;ic(t,e),t=e.pendingProps;var o=Yi(e,ft.current);ji(e,n),o=Jp(null,e,r,t,o,n);var i=Xp();return e.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(e.tag=1,e.memoizedState=null,e.updateQueue=null,Ft(r)?(i=!0,vc(e)):i=!1,e.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Yp(e),o.updater=Vc,e.stateNode=o,o._reactInternals=e,up(e,r,t,n),e=dp(null,e,r,!0,i,n)):(e.tag=0,xe&&i&&Wp(e),ht(null,e,o,n),e=e.child),e;case 16:r=e.elementType;e:{switch(ic(t,e),t=e.pendingProps,o=r._init,r=o(r._payload),e.type=r,o=e.tag=$k(r),t=wn(r,t),o){case 0:e=fp(null,e,r,t,n);break e;case 1:e=Tv(null,e,r,t,n);break e;case 11:e=Cv(null,e,r,t,n);break e;case 14:e=xv(null,e,r,wn(r.type,t),n);break e}throw Error(I(306,r,""))}return e;case 0:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:wn(r,o),fp(t,e,r,o,n);case 1:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:wn(r,o),Tv(t,e,r,o,n);case 3:e:{if(hw(e),t===null)throw Error(I(387));r=e.pendingProps,i=e.memoizedState,o=i.element,VE(t,e),xc(e,r,null,n);var s=e.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},e.updateQueue.baseState=i,e.memoizedState=i,e.flags&256){o=Qi(Error(I(423)),e),e=Fv(t,e,r,n,o);break e}else if(r!==o){o=Qi(Error(I(424)),e),e=Fv(t,e,r,n,o);break e}else for(Lt=$r(e.stateNode.containerInfo.firstChild),Pt=e,xe=!0,Cn=null,n=UE(e,null,r,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Zi(),r===o){e=fr(t,e,n);break e}ht(t,e,r,n)}e=e.child}return e;case 5:return jE(e),t===null&&sp(e),r=e.type,o=e.pendingProps,i=t!==null?t.memoizedProps:null,s=o.children,tp(r,o)?s=null:i!==null&&tp(r,i)&&(e.flags|=32),pw(t,e),ht(t,e,s,n),e.child;case 6:return t===null&&sp(e),null;case 13:return yw(t,e,n);case 4:return Zp(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=qi(e,null,r,n):ht(t,e,r,n),e.child;case 11:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:wn(r,o),Cv(t,e,r,o,n);case 7:return ht(t,e,e.pendingProps,n),e.child;case 8:return ht(t,e,e.pendingProps.children,n),e.child;case 12:return ht(t,e,e.pendingProps.children,n),e.child;case 10:e:{if(r=e.type._context,o=e.pendingProps,i=e.memoizedProps,s=o.value,ve(Sc,r._currentValue),r._currentValue=s,i!==null)if(Fn(i.value,s)){if(i.children===o.children&&!Tt.current){e=fr(t,e,n);break e}}else for(i=e.child,i!==null&&(i.return=e);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=ar(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?l.next=l:(l.next=f.next,f.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),ap(i.return,n,e),a.lanes|=n;break}l=l.next}}else if(i.tag===10)s=i.type===e.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(I(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),ap(s,n,e),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===e){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}ht(t,e,o.children,n),e=e.child}return e;case 9:return o=e.type,r=e.pendingProps.children,ji(e,n),o=en(o),r=r(o),e.flags|=1,ht(t,e,r,n),e.child;case 14:return r=e.type,o=wn(r,e.pendingProps),o=wn(r.type,o),xv(t,e,r,o,n);case 15:return dw(t,e,e.type,e.pendingProps,n);case 17:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:wn(r,o),ic(t,e),e.tag=1,Ft(r)?(t=!0,vc(e)):t=!1,ji(e,n),HE(e,r,o),up(e,r,o,n),dp(null,e,r,!0,t,n);case 19:return gw(t,e,n);case 22:return mw(t,e,n)}throw Error(I(156,e.tag))};function Mw(t,e){return iE(t,e)}function jk(t,e,n,r){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Jt(t,e,n,r){return new jk(t,e,n,r)}function uh(t){return t=t.prototype,!(!t||!t.isReactComponent)}function $k(t){if(typeof t=="function")return uh(t)?1:0;if(t!=null){if(t=t.$$typeof,t===kp)return 11;if(t===Op)return 14}return 2}function qr(t,e){var n=t.alternate;return n===null?(n=Jt(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&14680064,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function lc(t,e,n,r,o,i){var s=2;if(r=t,typeof t=="function")uh(t)&&(s=1);else if(typeof t=="string")s=5;else e:switch(t){case Oi:return bo(n.children,o,i,e);case _p:s=8,o|=8;break;case Mm:return t=Jt(12,n,e,o|2),t.elementType=Mm,t.lanes=i,t;case Im:return t=Jt(13,n,e,o),t.elementType=Im,t.lanes=i,t;case Am:return t=Jt(19,n,e,o),t.elementType=Am,t.lanes=i,t;case zv:return Uc(n,o,i,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Wv:s=10;break e;case Hv:s=9;break e;case kp:s=11;break e;case Op:s=14;break e;case Lr:s=16,r=null;break e}throw Error(I(130,t==null?t:typeof t,""))}return e=Jt(s,n,e,o),e.elementType=t,e.type=r,e.lanes=i,e}function bo(t,e,n,r){return t=Jt(7,t,r,e),t.lanes=n,t}function Uc(t,e,n,r){return t=Jt(22,t,r,e),t.elementType=zv,t.lanes=n,t.stateNode={isHidden:!1},t}function bm(t,e,n){return t=Jt(6,t,null,e),t.lanes=n,t}function Rm(t,e,n){return e=Jt(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function Gk(t,e,n,r,o){this.tag=e,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=pm(0),this.expirationTimes=pm(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=pm(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function ch(t,e,n,r,o,i,s,a,l){return t=new Gk(t,e,n,a,l),e===1?(e=1,i===!0&&(e|=8)):e=0,i=Jt(3,null,null,e),t.current=i,i.stateNode=t,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Yp(i),t}function Yk(t,e,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ki,key:r==null?null:""+r,children:t,containerInfo:e,implementation:n}}function Iw(t){if(!t)return Qr;t=t._reactInternals;e:{if(Po(t)!==t||t.tag!==1)throw Error(I(170));var e=t;do{switch(e.tag){case 3:e=e.stateNode.context;break e;case 1:if(Ft(e.type)){e=e.stateNode.__reactInternalMemoizedMergedChildContext;break e}}e=e.return}while(e!==null);throw Error(I(171))}if(t.tag===1){var n=t.type;if(Ft(n))return ME(t,n,e)}return e}function Aw(t,e,n,r,o,i,s,a,l){return t=ch(n,r,!0,t,o,i,s,a,l),t.context=Iw(null),n=t.current,r=yt(),o=Zr(n),i=ar(r,o),i.callback=e!=null?e:null,Gr(n,i,o),t.current.lanes=o,Ja(t,o,r),_t(t,r),t}function jc(t,e,n,r){var o=e.current,i=yt(),s=Zr(o);return n=Iw(n),e.context===null?e.context=n:e.pendingContext=n,e=ar(i,s),e.payload={element:t},r=r===void 0?null:r,r!==null&&(e.callback=r),t=Gr(o,e,s),t!==null&&(Tn(t,o,s,i),nc(t,o,s)),s}function Nc(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function Lv(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function fh(t,e){Lv(t,e),(t=t.alternate)&&Lv(t,e)}function Zk(){return null}var Lw=typeof reportError=="function"?reportError:function(t){console.error(t)};function dh(t){this._internalRoot=t}$c.prototype.render=dh.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(I(409));jc(t,e,null,null)};$c.prototype.unmount=dh.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Ao(function(){jc(null,t,null,null)}),e[ur]=null}};function $c(t){this._internalRoot=t}$c.prototype.unstable_scheduleHydration=function(t){if(t){var e=dE();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Br.length&&e!==0&&e<Br[n].priority;n++);Br.splice(n,0,t),n===0&&pE(t)}};function mh(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Gc(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function Pv(){}function qk(t,e,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Nc(s);i.call(u)}}var s=Aw(e,r,t,0,null,!1,!1,"",Pv);return t._reactRootContainer=s,t[ur]=s.current,Ua(t.nodeType===8?t.parentNode:t),Ao(),s}for(;o=t.lastChild;)t.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=Nc(l);a.call(u)}}var l=ch(t,0,!1,null,null,!1,!1,"",Pv);return t._reactRootContainer=l,t[ur]=l.current,Ua(t.nodeType===8?t.parentNode:t),Ao(function(){jc(e,l,n,r)}),l}function Yc(t,e,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var a=o;o=function(){var l=Nc(s);a.call(l)}}jc(e,s,t,o)}else s=qk(n,e,t,o,r);return Nc(s)}cE=function(t){switch(t.tag){case 3:var e=t.stateNode;if(e.current.memoizedState.isDehydrated){var n=xa(e.pendingLanes);n!==0&&(Np(e,n|1),_t(e,Le()),!(ce&6)&&(Ji=Le()+500,eo()))}break;case 13:Ao(function(){var r=cr(t,1);if(r!==null){var o=yt();Tn(r,t,1,o)}}),fh(t,1)}};Mp=function(t){if(t.tag===13){var e=cr(t,134217728);if(e!==null){var n=yt();Tn(e,t,134217728,n)}fh(t,134217728)}};fE=function(t){if(t.tag===13){var e=Zr(t),n=cr(t,e);if(n!==null){var r=yt();Tn(n,t,e,r)}fh(t,e)}};dE=function(){return pe};mE=function(t,e){var n=pe;try{return pe=t,e()}finally{pe=n}};$m=function(t,e,n){switch(e){case"input":if(Bm(t,n),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var r=n[e];if(r!==t&&r.form===t.form){var o=Pc(r);if(!o)throw Error(I(90));jv(r),Bm(r,o)}}}break;case"textarea":Gv(t,n);break;case"select":e=n.value,e!=null&&Wi(t,!!n.multiple,e,!1)}};Xv=sh;eE=Ao;var Kk={usingClientEntryPoint:!1,Events:[el,Mi,Pc,Qv,Jv,sh]},Ea={findFiberByHostInstance:Fo,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},Qk={bundleType:Ea.bundleType,version:Ea.version,rendererPackageName:Ea.rendererPackageName,rendererConfig:Ea.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:dr.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=rE(t),t===null?null:t.stateNode},findFiberByHostInstance:Ea.findFiberByHostInstance||Zk,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&(wa=__REACT_DEVTOOLS_GLOBAL_HOOK__,!wa.isDisabled&&wa.supportsFiber))try{Mc=wa.inject(Qk),zn=wa}catch(t){}var wa;Wt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Kk;Wt.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!mh(e))throw Error(I(200));return Yk(t,e,null,n)};Wt.createRoot=function(t,e){if(!mh(t))throw Error(I(299));var n=!1,r="",o=Lw;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onRecoverableError!==void 0&&(o=e.onRecoverableError)),e=ch(t,1,!1,null,null,n,!1,r,o),t[ur]=e.current,Ua(t.nodeType===8?t.parentNode:t),new dh(e)};Wt.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(I(188)):(t=Object.keys(t).join(","),Error(I(268,t)));return t=rE(e),t=t===null?null:t.stateNode,t};Wt.flushSync=function(t){return Ao(t)};Wt.hydrate=function(t,e,n){if(!Gc(e))throw Error(I(200));return Yc(null,t,e,!0,n)};Wt.hydrateRoot=function(t,e,n){if(!mh(t))throw Error(I(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=Lw;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),e=Aw(e,null,t,1,n!=null?n:null,o,!1,i,s),t[ur]=e.current,Ua(t),r)for(t=0;t<r.length;t++)n=r[t],o=n._getVersion,o=o(n._source),e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[n,o]:e.mutableSourceEagerHydrationData.push(n,o);return new $c(e)};Wt.render=function(t,e,n){if(!Gc(e))throw Error(I(200));return Yc(null,t,e,!1,n)};Wt.unmountComponentAtNode=function(t){if(!Gc(t))throw Error(I(40));return t._reactRootContainer?(Ao(function(){Yc(null,null,t,!1,function(){t._reactRootContainer=null,t[ur]=null})}),!0):!1};Wt.unstable_batchedUpdates=sh;Wt.unstable_renderSubtreeIntoContainer=function(t,e,n,r){if(!Gc(n))throw Error(I(200));if(t==null||t._reactInternals===void 0)throw Error(I(38));return Yc(t,e,n,!1,r)};Wt.version="18.2.0-next-9e3b772b8-20220608"});var Zc=Rn((JN,Vw)=>{"use strict";function Bw(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Bw)}catch(t){console.error(t)}}Bw(),Vw.exports=Pw()});var Hw=Rn(ph=>{"use strict";var Ww=Zc();ph.createRoot=Ww.createRoot,ph.hydrateRoot=Ww.hydrateRoot;var XN});var Uw=Rn(qc=>{"use strict";var Jk=se(),Xk=Symbol.for("react.element"),e2=Symbol.for("react.fragment"),t2=Object.prototype.hasOwnProperty,n2=Jk.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,r2={key:!0,ref:!0,__self:!0,__source:!0};function zw(t,e,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),e.key!==void 0&&(i=""+e.key),e.ref!==void 0&&(s=e.ref);for(r in e)t2.call(e,r)&&!r2.hasOwnProperty(r)&&(o[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps,e)o[r]===void 0&&(o[r]=e[r]);return{$$typeof:Xk,type:t,key:i,ref:s,props:o,_owner:n2.current}}qc.Fragment=e2;qc.jsx=zw;qc.jsxs=zw});var X=Rn((nM,jw)=>{"use strict";jw.exports=Uw()});var hO={};tC(hO,{default:()=>Ef});module.exports=nC(hO);var VS=require("obsidian");var Jn=class extends Error{},_l=class extends Jn{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}},kl=class extends Jn{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}},Ol=class extends Jn{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}},ln=class extends Jn{},Ko=class extends Jn{constructor(e){super(`Invalid unit ${e}`)}},Ve=class extends Jn{},un=class extends Jn{constructor(){super("Zone is an abstract class")}};var U="numeric",cn="short",Rt="long",Cr={year:U,month:U,day:U},Es={year:U,month:cn,day:U},Tf={year:U,month:cn,day:U,weekday:cn},ws={year:U,month:Rt,day:U},Ss={year:U,month:Rt,day:U,weekday:Rt},Cs={hour:U,minute:U},xs={hour:U,minute:U,second:U},Ts={hour:U,minute:U,second:U,timeZoneName:cn},Fs={hour:U,minute:U,second:U,timeZoneName:Rt},_s={hour:U,minute:U,hourCycle:"h23"},ks={hour:U,minute:U,second:U,hourCycle:"h23"},Os={hour:U,minute:U,second:U,hourCycle:"h23",timeZoneName:cn},bs={hour:U,minute:U,second:U,hourCycle:"h23",timeZoneName:Rt},Rs={year:U,month:U,day:U,hour:U,minute:U},Ns={year:U,month:U,day:U,hour:U,minute:U,second:U},Ms={year:U,month:cn,day:U,hour:U,minute:U},Is={year:U,month:cn,day:U,hour:U,minute:U,second:U},Ff={year:U,month:cn,day:U,weekday:cn,hour:U,minute:U},As={year:U,month:Rt,day:U,hour:U,minute:U,timeZoneName:cn},Ls={year:U,month:Rt,day:U,hour:U,minute:U,second:U,timeZoneName:cn},Ps={year:U,month:Rt,day:U,weekday:Rt,hour:U,minute:U,timeZoneName:Rt},Bs={year:U,month:Rt,day:U,weekday:Rt,hour:U,minute:U,second:U,timeZoneName:Rt};var wt=class{get type(){throw new un}get name(){throw new un}get ianaName(){return this.name}get isUniversal(){throw new un}offsetName(e,n){throw new un}formatOffset(e,n){throw new un}offset(e){throw new un}equals(e){throw new un}get isValid(){throw new un}};var _f=null,Nn=class extends wt{static get instance(){return _f===null&&(_f=new Nn),_f}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:n,locale:r}){return Rl(e,n,r)}formatOffset(e,n){return xr(this.offset(e),n)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return e.type==="system"}get isValid(){return!0}};var Ml={};function rC(t){return Ml[t]||(Ml[t]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:t,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),Ml[t]}var oC={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function iC(t,e){let n=t.format(e).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(n),[,o,i,s,a,l,u,f]=r;return[s,o,i,a,l,u,f]}function sC(t,e){let n=t.formatToParts(e),r=[];for(let o=0;o<n.length;o++){let{type:i,value:s}=n[o],a=oC[i];i==="era"?r[a]=s:J(a)||(r[a]=parseInt(s,10))}return r}var Nl={},$e=class extends wt{static create(e){return Nl[e]||(Nl[e]=new $e(e)),Nl[e]}static resetCache(){Nl={},Ml={}}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch(n){return!1}}constructor(e){super(),this.zoneName=e,this.valid=$e.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:n,locale:r}){return Rl(e,n,r,this.name)}formatOffset(e,n){return xr(this.offset(e),n)}offset(e){let n=new Date(e);if(isNaN(n))return NaN;let r=rC(this.name),[o,i,s,a,l,u,f]=r.formatToParts?sC(r,n):iC(r,n);a==="BC"&&(o=-Math.abs(o)+1);let m=Qo({year:o,month:i,day:s,hour:l===24?0:l,minute:u,second:f,millisecond:0}),c=+n,y=c%1e3;return c-=y>=0?y:1e3+y,(m-c)/(60*1e3)}equals(e){return e.type==="iana"&&e.name===this.name}get isValid(){return this.valid}};var sy={};function aC(t,e={}){let n=JSON.stringify([t,e]),r=sy[n];return r||(r=new Intl.ListFormat(t,e),sy[n]=r),r}var kf={};function Of(t,e={}){let n=JSON.stringify([t,e]),r=kf[n];return r||(r=new Intl.DateTimeFormat(t,e),kf[n]=r),r}var bf={};function lC(t,e={}){let n=JSON.stringify([t,e]),r=bf[n];return r||(r=new Intl.NumberFormat(t,e),bf[n]=r),r}var Rf={};function uC(t,e={}){let{base:n,...r}=e,o=JSON.stringify([t,r]),i=Rf[o];return i||(i=new Intl.RelativeTimeFormat(t,e),Rf[o]=i),i}var Vs=null;function cC(){return Vs||(Vs=new Intl.DateTimeFormat().resolvedOptions().locale,Vs)}var ay={};function fC(t){let e=ay[t];if(!e){let n=new Intl.Locale(t);e="getWeekInfo"in n?n.getWeekInfo():n.weekInfo,ay[t]=e}return e}function dC(t){let e=t.indexOf("-x-");e!==-1&&(t=t.substring(0,e));let n=t.indexOf("-u-");if(n===-1)return[t];{let r,o;try{r=Of(t).resolvedOptions(),o=t}catch(a){let l=t.substring(0,n);r=Of(l).resolvedOptions(),o=l}let{numberingSystem:i,calendar:s}=r;return[o,i,s]}}function mC(t,e,n){return(n||e)&&(t.includes("-u-")||(t+="-u"),n&&(t+=`-ca-${n}`),e&&(t+=`-nu-${e}`)),t}function pC(t){let e=[];for(let n=1;n<=12;n++){let r=$.utc(2009,n,1);e.push(t(r))}return e}function hC(t){let e=[];for(let n=1;n<=7;n++){let r=$.utc(2016,11,13+n);e.push(t(r))}return e}function Il(t,e,n,r){let o=t.listingMode();return o==="error"?null:o==="en"?n(e):r(e)}function yC(t){return t.numberingSystem&&t.numberingSystem!=="latn"?!1:t.numberingSystem==="latn"||!t.locale||t.locale.startsWith("en")||new Intl.DateTimeFormat(t.intl).resolvedOptions().numberingSystem==="latn"}var Nf=class{constructor(e,n,r){this.padTo=r.padTo||0,this.floor=r.floor||!1;let{padTo:o,floor:i,...s}=r;if(!n||Object.keys(s).length>0){let a={useGrouping:!1,...r};r.padTo>0&&(a.minimumIntegerDigits=r.padTo),this.inf=lC(e,a)}}format(e){if(this.inf){let n=this.floor?Math.floor(e):e;return this.inf.format(n)}else{let n=this.floor?Math.floor(e):Jo(e,3);return Fe(n,this.padTo)}}},Mf=class{constructor(e,n,r){this.opts=r,this.originalZone=void 0;let o;if(this.opts.timeZone)this.dt=e;else if(e.zone.type==="fixed"){let s=-1*(e.offset/60),a=s>=0?`Etc/GMT+${s}`:`Etc/GMT${s}`;e.offset!==0&&$e.create(a).valid?(o=a,this.dt=e):(o="UTC",this.dt=e.offset===0?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)}else e.zone.type==="system"?this.dt=e:e.zone.type==="iana"?(this.dt=e,o=e.zone.name):(o="UTC",this.dt=e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone);let i={...this.opts};i.timeZone=i.timeZone||o,this.dtf=Of(n,i)}format(){return this.originalZone?this.formatToParts().map(({value:e})=>e).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){let e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map(n=>{if(n.type==="timeZoneName"){let r=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...n,value:r}}else return n}):e}resolvedOptions(){return this.dtf.resolvedOptions()}},If=class{constructor(e,n,r){this.opts={style:"long",...r},!n&&Al()&&(this.rtf=uC(e,r))}format(e,n){return this.rtf?this.rtf.format(e,n):ly(n,e,this.opts.numeric,this.opts.style!=="long")}formatToParts(e,n){return this.rtf?this.rtf.formatToParts(e,n):[]}},gC={firstDay:1,minimalDays:4,weekend:[6,7]},le=class{static fromOpts(e){return le.create(e.locale,e.numberingSystem,e.outputCalendar,e.weekSettings,e.defaultToEN)}static create(e,n,r,o,i=!1){let s=e||fe.defaultLocale,a=s||(i?"en-US":cC()),l=n||fe.defaultNumberingSystem,u=r||fe.defaultOutputCalendar,f=Ws(o)||fe.defaultWeekSettings;return new le(a,l,u,f,s)}static resetCache(){Vs=null,kf={},bf={},Rf={}}static fromObject({locale:e,numberingSystem:n,outputCalendar:r,weekSettings:o}={}){return le.create(e,n,r,o)}constructor(e,n,r,o,i){let[s,a,l]=dC(e);this.locale=s,this.numberingSystem=n||a||null,this.outputCalendar=r||l||null,this.weekSettings=o,this.intl=mC(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=i,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=yC(this)),this.fastNumbersCached}listingMode(){let e=this.isEnglish(),n=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return e&&n?"en":"intl"}clone(e){return!e||Object.getOwnPropertyNames(e).length===0?this:le.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,Ws(e.weekSettings)||this.weekSettings,e.defaultToEN||!1)}redefaultToEN(e={}){return this.clone({...e,defaultToEN:!0})}redefaultToSystem(e={}){return this.clone({...e,defaultToEN:!1})}months(e,n=!1){return Il(this,e,Af,()=>{let r=n?{month:e,day:"numeric"}:{month:e},o=n?"format":"standalone";return this.monthsCache[o][e]||(this.monthsCache[o][e]=pC(i=>this.extract(i,r,"month"))),this.monthsCache[o][e]})}weekdays(e,n=!1){return Il(this,e,Lf,()=>{let r=n?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},o=n?"format":"standalone";return this.weekdaysCache[o][e]||(this.weekdaysCache[o][e]=hC(i=>this.extract(i,r,"weekday"))),this.weekdaysCache[o][e]})}meridiems(){return Il(this,void 0,()=>Pf,()=>{if(!this.meridiemCache){let e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[$.utc(2016,11,13,9),$.utc(2016,11,13,19)].map(n=>this.extract(n,e,"dayperiod"))}return this.meridiemCache})}eras(e){return Il(this,e,Bf,()=>{let n={era:e};return this.eraCache[e]||(this.eraCache[e]=[$.utc(-40,1,1),$.utc(2017,1,1)].map(r=>this.extract(r,n,"era"))),this.eraCache[e]})}extract(e,n,r){let o=this.dtFormatter(e,n),i=o.formatToParts(),s=i.find(a=>a.type.toLowerCase()===r);return s?s.value:null}numberFormatter(e={}){return new Nf(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,n={}){return new Mf(e,this.intl,n)}relFormatter(e={}){return new If(this.intl,this.isEnglish(),e)}listFormatter(e={}){return aC(this.intl,e)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}getWeekSettings(){return this.weekSettings?this.weekSettings:Ll()?fC(this.locale):gC}getStartOfWeek(){return this.getWeekSettings().firstDay}getMinDaysInFirstWeek(){return this.getWeekSettings().minimalDays}getWeekendDays(){return this.getWeekSettings().weekend}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}};var Wf=null,_e=class extends wt{static get utcInstance(){return Wf===null&&(Wf=new _e(0)),Wf}static instance(e){return e===0?_e.utcInstance:new _e(e)}static parseSpecifier(e){if(e){let n=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(n)return new _e(co(n[1],n[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${xr(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${xr(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,n){return xr(this.fixed,n)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return e.type==="fixed"&&e.fixed===this.fixed}get isValid(){return!0}};var Xo=class extends wt{constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}};function fn(t,e){let n;if(J(t)||t===null)return e;if(t instanceof wt)return t;if(uy(t)){let r=t.toLowerCase();return r==="default"?e:r==="local"||r==="system"?Nn.instance:r==="utc"||r==="gmt"?_e.utcInstance:_e.parseSpecifier(r)||$e.create(t)}else return Mn(t)?_e.instance(t):typeof t=="object"&&"offset"in t&&typeof t.offset=="function"?t:new Xo(t)}var cy=()=>Date.now(),fy="system",dy=null,my=null,py=null,hy=60,yy,gy=null,fe=class{static get now(){return cy}static set now(e){cy=e}static set defaultZone(e){fy=e}static get defaultZone(){return fn(fy,Nn.instance)}static get defaultLocale(){return dy}static set defaultLocale(e){dy=e}static get defaultNumberingSystem(){return my}static set defaultNumberingSystem(e){my=e}static get defaultOutputCalendar(){return py}static set defaultOutputCalendar(e){py=e}static get defaultWeekSettings(){return gy}static set defaultWeekSettings(e){gy=Ws(e)}static get twoDigitCutoffYear(){return hy}static set twoDigitCutoffYear(e){hy=e%100}static get throwOnInvalid(){return yy}static set throwOnInvalid(e){yy=e}static resetCaches(){le.resetCache(),$e.resetCache()}};var qe=class{constructor(e,n){this.reason=e,this.explanation=n}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}};var Dy=[0,31,59,90,120,151,181,212,243,273,304,334],vy=[0,31,60,91,121,152,182,213,244,274,305,335];function jt(t,e){return new qe("unit out of range",`you specified ${e} (of type ${typeof e}) as a ${t}, which is invalid`)}function Pl(t,e,n){let r=new Date(Date.UTC(t,e-1,n));t<100&&t>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);let o=r.getUTCDay();return o===0?7:o}function Ey(t,e,n){return n+(mo(t)?vy:Dy)[e-1]}function wy(t,e){let n=mo(t)?vy:Dy,r=n.findIndex(i=>i<e),o=e-n[r];return{month:r+1,day:o}}function Bl(t,e){return(t-e+7)%7+1}function Hs(t,e=4,n=1){let{year:r,month:o,day:i}=t,s=Ey(r,o,i),a=Bl(Pl(r,o,i),n),l=Math.floor((s-a+14-e)/7),u;return l<1?(u=r-1,l=fo(u,e,n)):l>fo(r,e,n)?(u=r+1,l=1):u=r,{weekYear:u,weekNumber:l,weekday:a,...Us(t)}}function Hf(t,e=4,n=1){let{weekYear:r,weekNumber:o,weekday:i}=t,s=Bl(Pl(r,1,e),n),a=Tr(r),l=o*7+i-s-7+e,u;l<1?(u=r-1,l+=Tr(u)):l>a?(u=r+1,l-=Tr(r)):u=r;let{month:f,day:d}=wy(u,l);return{year:u,month:f,day:d,...Us(t)}}function Vl(t){let{year:e,month:n,day:r}=t,o=Ey(e,n,r);return{year:e,ordinal:o,...Us(t)}}function zf(t){let{year:e,ordinal:n}=t,{month:r,day:o}=wy(e,n);return{year:e,month:r,day:o,...Us(t)}}function Uf(t,e){if(!J(t.localWeekday)||!J(t.localWeekNumber)||!J(t.localWeekYear)){if(!J(t.weekday)||!J(t.weekNumber)||!J(t.weekYear))throw new ln("Cannot mix locale-based week fields with ISO-based week fields");return J(t.localWeekday)||(t.weekday=t.localWeekday),J(t.localWeekNumber)||(t.weekNumber=t.localWeekNumber),J(t.localWeekYear)||(t.weekYear=t.localWeekYear),delete t.localWeekday,delete t.localWeekNumber,delete t.localWeekYear,{minDaysInFirstWeek:e.getMinDaysInFirstWeek(),startOfWeek:e.getStartOfWeek()}}else return{minDaysInFirstWeek:4,startOfWeek:1}}function Sy(t,e=4,n=1){let r=zs(t.weekYear),o=Nt(t.weekNumber,1,fo(t.weekYear,e,n)),i=Nt(t.weekday,1,7);return r?o?i?!1:jt("weekday",t.weekday):jt("week",t.weekNumber):jt("weekYear",t.weekYear)}function Cy(t){let e=zs(t.year),n=Nt(t.ordinal,1,Tr(t.year));return e?n?!1:jt("ordinal",t.ordinal):jt("year",t.year)}function jf(t){let e=zs(t.year),n=Nt(t.month,1,12),r=Nt(t.day,1,ei(t.year,t.month));return e?n?r?!1:jt("day",t.day):jt("month",t.month):jt("year",t.year)}function $f(t){let{hour:e,minute:n,second:r,millisecond:o}=t,i=Nt(e,0,23)||e===24&&n===0&&r===0&&o===0,s=Nt(n,0,59),a=Nt(r,0,59),l=Nt(o,0,999);return i?s?a?l?!1:jt("millisecond",o):jt("second",r):jt("minute",n):jt("hour",e)}function J(t){return typeof t=="undefined"}function Mn(t){return typeof t=="number"}function zs(t){return typeof t=="number"&&t%1===0}function uy(t){return typeof t=="string"}function Ty(t){return Object.prototype.toString.call(t)==="[object Date]"}function Al(){try{return typeof Intl!="undefined"&&!!Intl.RelativeTimeFormat}catch(t){return!1}}function Ll(){try{return typeof Intl!="undefined"&&!!Intl.Locale&&("weekInfo"in Intl.Locale.prototype||"getWeekInfo"in Intl.Locale.prototype)}catch(t){return!1}}function Fy(t){return Array.isArray(t)?t:[t]}function Gf(t,e,n){if(t.length!==0)return t.reduce((r,o)=>{let i=[e(o),o];return r&&n(r[0],i[0])===r[0]?r:i},null)[1]}function _y(t,e){return e.reduce((n,r)=>(n[r]=t[r],n),{})}function Fr(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function Ws(t){if(t==null)return null;if(typeof t!="object")throw new Ve("Week settings must be an object");if(!Nt(t.firstDay,1,7)||!Nt(t.minimalDays,1,7)||!Array.isArray(t.weekend)||t.weekend.some(e=>!Nt(e,1,7)))throw new Ve("Invalid week settings");return{firstDay:t.firstDay,minimalDays:t.minimalDays,weekend:Array.from(t.weekend)}}function Nt(t,e,n){return zs(t)&&t>=e&&t<=n}function DC(t,e){return t-e*Math.floor(t/e)}function Fe(t,e=2){let n=t<0,r;return n?r="-"+(""+-t).padStart(e,"0"):r=(""+t).padStart(e,"0"),r}function Xn(t){if(!(J(t)||t===null||t===""))return parseInt(t,10)}function _r(t){if(!(J(t)||t===null||t===""))return parseFloat(t)}function js(t){if(!(J(t)||t===null||t==="")){let e=parseFloat("0."+t)*1e3;return Math.floor(e)}}function Jo(t,e,n=!1){let r=10**e;return(n?Math.trunc:Math.round)(t*r)/r}function mo(t){return t%4===0&&(t%100!==0||t%400===0)}function Tr(t){return mo(t)?366:365}function ei(t,e){let n=DC(e-1,12)+1,r=t+(e-n)/12;return n===2?mo(r)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function Qo(t){let e=Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,t.second,t.millisecond);return t.year<100&&t.year>=0&&(e=new Date(e),e.setUTCFullYear(t.year,t.month-1,t.day)),+e}function xy(t,e,n){return-Bl(Pl(t,1,e),n)+e-1}function fo(t,e=4,n=1){let r=xy(t,e,n),o=xy(t+1,e,n);return(Tr(t)-r+o)/7}function $s(t){return t>99?t:t>fe.twoDigitCutoffYear?1900+t:2e3+t}function Rl(t,e,n,r=null){let o=new Date(t),i={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(i.timeZone=r);let s={timeZoneName:e,...i},a=new Intl.DateTimeFormat(n,s).formatToParts(o).find(l=>l.type.toLowerCase()==="timezonename");return a?a.value:null}function co(t,e){let n=parseInt(t,10);Number.isNaN(n)&&(n=0);let r=parseInt(e,10)||0,o=n<0||Object.is(n,-0)?-r:r;return n*60+o}function Yf(t){let e=Number(t);if(typeof t=="boolean"||t===""||Number.isNaN(e))throw new Ve(`Invalid unit value ${t}`);return e}function ti(t,e){let n={};for(let r in t)if(Fr(t,r)){let o=t[r];if(o==null)continue;n[e(r)]=Yf(o)}return n}function xr(t,e){let n=Math.trunc(Math.abs(t/60)),r=Math.trunc(Math.abs(t%60)),o=t>=0?"+":"-";switch(e){case"short":return`${o}${Fe(n,2)}:${Fe(r,2)}`;case"narrow":return`${o}${n}${r>0?`:${r}`:""}`;case"techie":return`${o}${Fe(n,2)}${Fe(r,2)}`;default:throw new RangeError(`Value format ${e} is out of range for property format`)}}function Us(t){return _y(t,["hour","minute","second","millisecond"])}var vC=["January","February","March","April","May","June","July","August","September","October","November","December"],Zf=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],EC=["J","F","M","A","M","J","J","A","S","O","N","D"];function Af(t){switch(t){case"narrow":return[...EC];case"short":return[...Zf];case"long":return[...vC];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}var qf=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Kf=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],wC=["M","T","W","T","F","S","S"];function Lf(t){switch(t){case"narrow":return[...wC];case"short":return[...Kf];case"long":return[...qf];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}var Pf=["AM","PM"],SC=["Before Christ","Anno Domini"],CC=["BC","AD"],xC=["B","A"];function Bf(t){switch(t){case"narrow":return[...xC];case"short":return[...CC];case"long":return[...SC];default:return null}}function ky(t){return Pf[t.hour<12?0:1]}function Oy(t,e){return Lf(e)[t.weekday-1]}function by(t,e){return Af(e)[t.month-1]}function Ry(t,e){return Bf(e)[t.year<0?0:1]}function ly(t,e,n="always",r=!1){let o={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},i=["hours","minutes","seconds"].indexOf(t)===-1;if(n==="auto"&&i){let d=t==="days";switch(e){case 1:return d?"tomorrow":`next ${o[t][0]}`;case-1:return d?"yesterday":`last ${o[t][0]}`;case 0:return d?"today":`this ${o[t][0]}`;default:}}let s=Object.is(e,-0)||e<0,a=Math.abs(e),l=a===1,u=o[t],f=r?l?u[1]:u[2]||u[1]:l?o[t][0]:t;return s?`${a} ${f} ago`:`in ${a} ${f}`}function Ny(t,e){let n="";for(let r of t)r.literal?n+=r.val:n+=e(r.val);return n}var TC={D:Cr,DD:Es,DDD:ws,DDDD:Ss,t:Cs,tt:xs,ttt:Ts,tttt:Fs,T:_s,TT:ks,TTT:Os,TTTT:bs,f:Rs,ff:Ms,fff:As,ffff:Ps,F:Ns,FF:Is,FFF:Ls,FFFF:Bs},ke=class{static create(e,n={}){return new ke(e,n)}static parseFormat(e){let n=null,r="",o=!1,i=[];for(let s=0;s<e.length;s++){let a=e.charAt(s);a==="'"?(r.length>0&&i.push({literal:o||/^\s+$/.test(r),val:r}),n=null,r="",o=!o):o||a===n?r+=a:(r.length>0&&i.push({literal:/^\s+$/.test(r),val:r}),r=a,n=a)}return r.length>0&&i.push({literal:o||/^\s+$/.test(r),val:r}),i}static macroTokenToFormatOpts(e){return TC[e]}constructor(e,n){this.opts=n,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,n){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,{...this.opts,...n}).format()}dtFormatter(e,n={}){return this.loc.dtFormatter(e,{...this.opts,...n})}formatDateTime(e,n){return this.dtFormatter(e,n).format()}formatDateTimeParts(e,n){return this.dtFormatter(e,n).formatToParts()}formatInterval(e,n){return this.dtFormatter(e.start,n).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())}resolvedOptions(e,n){return this.dtFormatter(e,n).resolvedOptions()}num(e,n=0){if(this.opts.forceSimple)return Fe(e,n);let r={...this.opts};return n>0&&(r.padTo=n),this.loc.numberFormatter(r).format(e)}formatDateTimeFromString(e,n){let r=this.loc.listingMode()==="en",o=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",i=(c,y)=>this.loc.extract(e,c,y),s=c=>e.isOffsetFixed&&e.offset===0&&c.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,c.format):"",a=()=>r?ky(e):i({hour:"numeric",hourCycle:"h12"},"dayperiod"),l=(c,y)=>r?by(e,c):i(y?{month:c}:{month:c,day:"numeric"},"month"),u=(c,y)=>r?Oy(e,c):i(y?{weekday:c}:{weekday:c,month:"long",day:"numeric"},"weekday"),f=c=>{let y=ke.macroTokenToFormatOpts(c);return y?this.formatWithSystemDefault(e,y):c},d=c=>r?Ry(e,c):i({era:c},"era"),m=c=>{switch(c){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12===0?12:e.hour%12);case"hh":return this.num(e.hour%12===0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return s({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return s({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return s({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return a();case"d":return o?i({day:"numeric"},"day"):this.num(e.day);case"dd":return o?i({day:"2-digit"},"day"):this.num(e.day,2);case"c":return this.num(e.weekday);case"ccc":return u("short",!0);case"cccc":return u("long",!0);case"ccccc":return u("narrow",!0);case"E":return this.num(e.weekday);case"EEE":return u("short",!1);case"EEEE":return u("long",!1);case"EEEEE":return u("narrow",!1);case"L":return o?i({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return o?i({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return l("short",!0);case"LLLL":return l("long",!0);case"LLLLL":return l("narrow",!0);case"M":return o?i({month:"numeric"},"month"):this.num(e.month);case"MM":return o?i({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return l("short",!1);case"MMMM":return l("long",!1);case"MMMMM":return l("narrow",!1);case"y":return o?i({year:"numeric"},"year"):this.num(e.year);case"yy":return o?i({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return o?i({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return o?i({year:"numeric"},"year"):this.num(e.year,6);case"G":return d("short");case"GG":return d("long");case"GGGGG":return d("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"n":return this.num(e.localWeekNumber);case"nn":return this.num(e.localWeekNumber,2);case"ii":return this.num(e.localWeekYear.toString().slice(-2),2);case"iiii":return this.num(e.localWeekYear,4);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return f(c)}};return Ny(ke.parseFormat(n),m)}formatDurationFromString(e,n){let r=l=>{switch(l[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},o=l=>u=>{let f=r(u);return f?this.num(l.get(f),u.length):u},i=ke.parseFormat(n),s=i.reduce((l,{literal:u,val:f})=>u?l:l.concat(f),[]),a=e.shiftTo(...s.map(r).filter(l=>l));return Ny(i,o(a))}};var Iy=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function ri(...t){let e=t.reduce((n,r)=>n+r.source,"");return RegExp(`^${e}$`)}function oi(...t){return e=>t.reduce(([n,r,o],i)=>{let[s,a,l]=i(e,o);return[{...n,...s},a||r,l]},[{},null,1]).slice(0,2)}function ii(t,...e){if(t==null)return[null,null];for(let[n,r]of e){let o=n.exec(t);if(o)return r(o)}return[null,null]}function Ay(...t){return(e,n)=>{let r={},o;for(o=0;o<t.length;o++)r[t[o]]=Xn(e[n+o]);return[r,null,n+o]}}var Ly=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,FC=`(?:${Ly.source}?(?:\\[(${Iy.source})\\])?)?`,Qf=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,Py=RegExp(`${Qf.source}${FC}`),Jf=RegExp(`(?:T${Py.source})?`),_C=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,kC=/(\d{4})-?W(\d\d)(?:-?(\d))?/,OC=/(\d{4})-?(\d{3})/,bC=Ay("weekYear","weekNumber","weekDay"),RC=Ay("year","ordinal"),NC=/(\d{4})-(\d\d)-(\d\d)/,By=RegExp(`${Qf.source} ?(?:${Ly.source}|(${Iy.source}))?`),MC=RegExp(`(?: ${By.source})?`);function ni(t,e,n){let r=t[e];return J(r)?n:Xn(r)}function IC(t,e){return[{year:ni(t,e),month:ni(t,e+1,1),day:ni(t,e+2,1)},null,e+3]}function si(t,e){return[{hours:ni(t,e,0),minutes:ni(t,e+1,0),seconds:ni(t,e+2,0),milliseconds:js(t[e+3])},null,e+4]}function Gs(t,e){let n=!t[e]&&!t[e+1],r=co(t[e+1],t[e+2]),o=n?null:_e.instance(r);return[{},o,e+3]}function Ys(t,e){let n=t[e]?$e.create(t[e]):null;return[{},n,e+1]}var AC=RegExp(`^T?${Qf.source}$`),LC=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function PC(t){let[e,n,r,o,i,s,a,l,u]=t,f=e[0]==="-",d=l&&l[0]==="-",m=(c,y=!1)=>c!==void 0&&(y||c&&f)?-c:c;return[{years:m(_r(n)),months:m(_r(r)),weeks:m(_r(o)),days:m(_r(i)),hours:m(_r(s)),minutes:m(_r(a)),seconds:m(_r(l),l==="-0"),milliseconds:m(js(u),d)}]}var BC={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function Xf(t,e,n,r,o,i,s){let a={year:e.length===2?$s(Xn(e)):Xn(e),month:Zf.indexOf(n)+1,day:Xn(r),hour:Xn(o),minute:Xn(i)};return s&&(a.second=Xn(s)),t&&(a.weekday=t.length>3?qf.indexOf(t)+1:Kf.indexOf(t)+1),a}var VC=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function WC(t){let[,e,n,r,o,i,s,a,l,u,f,d]=t,m=Xf(e,o,r,n,i,s,a),c;return l?c=BC[l]:u?c=0:c=co(f,d),[m,new _e(c)]}function HC(t){return t.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}var zC=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,UC=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,jC=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function My(t){let[,e,n,r,o,i,s,a]=t;return[Xf(e,o,r,n,i,s,a),_e.utcInstance]}function $C(t){let[,e,n,r,o,i,s,a]=t;return[Xf(e,a,n,r,o,i,s),_e.utcInstance]}var GC=ri(_C,Jf),YC=ri(kC,Jf),ZC=ri(OC,Jf),qC=ri(Py),Vy=oi(IC,si,Gs,Ys),KC=oi(bC,si,Gs,Ys),QC=oi(RC,si,Gs,Ys),JC=oi(si,Gs,Ys);function Wy(t){return ii(t,[GC,Vy],[YC,KC],[ZC,QC],[qC,JC])}function Hy(t){return ii(HC(t),[VC,WC])}function zy(t){return ii(t,[zC,My],[UC,My],[jC,$C])}function Uy(t){return ii(t,[LC,PC])}var XC=oi(si);function jy(t){return ii(t,[AC,XC])}var ex=ri(NC,MC),tx=ri(By),nx=oi(si,Gs,Ys);function $y(t){return ii(t,[ex,Vy],[tx,nx])}var Gy="Invalid Duration",Zy={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},rx={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...Zy},$t=146097/400,ai=146097/4800,ox={years:{quarters:4,months:12,weeks:$t/7,days:$t,hours:$t*24,minutes:$t*24*60,seconds:$t*24*60*60,milliseconds:$t*24*60*60*1e3},quarters:{months:3,weeks:$t/28,days:$t/4,hours:$t*24/4,minutes:$t*24*60/4,seconds:$t*24*60*60/4,milliseconds:$t*24*60*60*1e3/4},months:{weeks:ai/7,days:ai,hours:ai*24,minutes:ai*24*60,seconds:ai*24*60*60,milliseconds:ai*24*60*60*1e3},...Zy},po=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],ix=po.slice(0).reverse();function kr(t,e,n=!1){let r={values:n?e.values:{...t.values,...e.values||{}},loc:t.loc.clone(e.loc),conversionAccuracy:e.conversionAccuracy||t.conversionAccuracy,matrix:e.matrix||t.matrix};return new ie(r)}function qy(t,e){var r;let n=(r=e.milliseconds)!=null?r:0;for(let o of ix.slice(1))e[o]&&(n+=e[o]*t[o].milliseconds);return n}function Yy(t,e){let n=qy(t,e)<0?-1:1;po.reduceRight((r,o)=>{if(J(e[o]))return r;if(r){let i=e[r]*n,s=t[o][r],a=Math.floor(i/s);e[o]+=a*n,e[r]-=a*s*n}return o},null),po.reduce((r,o)=>{if(J(e[o]))return r;if(r){let i=e[r]%1;e[r]-=i,e[o]+=i*t[r][o]}return o},null)}function sx(t){let e={};for(let[n,r]of Object.entries(t))r!==0&&(e[n]=r);return e}var ie=class{constructor(e){let n=e.conversionAccuracy==="longterm"||!1,r=n?ox:rx;e.matrix&&(r=e.matrix),this.values=e.values,this.loc=e.loc||le.create(),this.conversionAccuracy=n?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=r,this.isLuxonDuration=!0}static fromMillis(e,n){return ie.fromObject({milliseconds:e},n)}static fromObject(e,n={}){if(e==null||typeof e!="object")throw new Ve(`Duration.fromObject: argument expected to be an object, got ${e===null?"null":typeof e}`);return new ie({values:ti(e,ie.normalizeUnit),loc:le.fromObject(n),conversionAccuracy:n.conversionAccuracy,matrix:n.matrix})}static fromDurationLike(e){if(Mn(e))return ie.fromMillis(e);if(ie.isDuration(e))return e;if(typeof e=="object")return ie.fromObject(e);throw new Ve(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,n){let[r]=Uy(e);return r?ie.fromObject(r,n):ie.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,n){let[r]=jy(e);return r?ie.fromObject(r,n):ie.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,n=null){if(!e)throw new Ve("need to specify a reason the Duration is invalid");let r=e instanceof qe?e:new qe(e,n);if(fe.throwOnInvalid)throw new Ol(r);return new ie({invalid:r})}static normalizeUnit(e){let n={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e&&e.toLowerCase()];if(!n)throw new Ko(e);return n}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,n={}){let r={...n,floor:n.round!==!1&&n.floor!==!1};return this.isValid?ke.create(this.loc,r).formatDurationFromString(this,e):Gy}toHuman(e={}){if(!this.isValid)return Gy;let n=po.map(r=>{let o=this.values[r];return J(o)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...e,unit:r.slice(0,-1)}).format(o)}).filter(r=>r);return this.loc.listFormatter({type:"conjunction",style:e.listStyle||"narrow",...e}).format(n)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let e="P";return this.years!==0&&(e+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(e+=this.months+this.quarters*3+"M"),this.weeks!==0&&(e+=this.weeks+"W"),this.days!==0&&(e+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(e+="T"),this.hours!==0&&(e+=this.hours+"H"),this.minutes!==0&&(e+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(e+=Jo(this.seconds+this.milliseconds/1e3,3)+"S"),e==="P"&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;let n=this.toMillis();return n<0||n>=864e5?null:(e={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...e,includeOffset:!1},$.fromMillis(n,{zone:"UTC"}).toISOTime(e))}toJSON(){return this.toISO()}toString(){return this.toISO()}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Duration { values: ${JSON.stringify(this.values)} }`:`Duration { Invalid, reason: ${this.invalidReason} }`}toMillis(){return this.isValid?qy(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;let n=ie.fromDurationLike(e),r={};for(let o of po)(Fr(n.values,o)||Fr(this.values,o))&&(r[o]=n.get(o)+this.get(o));return kr(this,{values:r},!0)}minus(e){if(!this.isValid)return this;let n=ie.fromDurationLike(e);return this.plus(n.negate())}mapUnits(e){if(!this.isValid)return this;let n={};for(let r of Object.keys(this.values))n[r]=Yf(e(this.values[r],r));return kr(this,{values:n},!0)}get(e){return this[ie.normalizeUnit(e)]}set(e){if(!this.isValid)return this;let n={...this.values,...ti(e,ie.normalizeUnit)};return kr(this,{values:n})}reconfigure({locale:e,numberingSystem:n,conversionAccuracy:r,matrix:o}={}){let s={loc:this.loc.clone({locale:e,numberingSystem:n}),matrix:o,conversionAccuracy:r};return kr(this,s)}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;let e=this.toObject();return Yy(this.matrix,e),kr(this,{values:e},!0)}rescale(){if(!this.isValid)return this;let e=sx(this.normalize().shiftToAll().toObject());return kr(this,{values:e},!0)}shiftTo(...e){if(!this.isValid)return this;if(e.length===0)return this;e=e.map(s=>ie.normalizeUnit(s));let n={},r={},o=this.toObject(),i;for(let s of po)if(e.indexOf(s)>=0){i=s;let a=0;for(let u in r)a+=this.matrix[u][s]*r[u],r[u]=0;Mn(o[s])&&(a+=o[s]);let l=Math.trunc(a);n[s]=l,r[s]=(a*1e3-l*1e3)/1e3}else Mn(o[s])&&(r[s]=o[s]);for(let s in r)r[s]!==0&&(n[i]+=s===i?r[s]:r[s]/this.matrix[i][s]);return Yy(this.matrix,n),kr(this,{values:n},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;let e={};for(let n of Object.keys(this.values))e[n]=this.values[n]===0?0:-this.values[n];return kr(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid||!this.loc.equals(e.loc))return!1;function n(r,o){return r===void 0||r===0?o===void 0||o===0:r===o}for(let r of po)if(!n(this.values[r],e.values[r]))return!1;return!0}};var li="Invalid Interval";function ax(t,e){return!t||!t.isValid?he.invalid("missing or invalid start"):!e||!e.isValid?he.invalid("missing or invalid end"):e<t?he.invalid("end before start",`The end of an interval must be after its start, but you had start=${t.toISO()} and end=${e.toISO()}`):null}var he=class{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,n=null){if(!e)throw new Ve("need to specify a reason the Interval is invalid");let r=e instanceof qe?e:new qe(e,n);if(fe.throwOnInvalid)throw new kl(r);return new he({invalid:r})}static fromDateTimes(e,n){let r=ui(e),o=ui(n),i=ax(r,o);return i==null?new he({start:r,end:o}):i}static after(e,n){let r=ie.fromDurationLike(n),o=ui(e);return he.fromDateTimes(o,o.plus(r))}static before(e,n){let r=ie.fromDurationLike(n),o=ui(e);return he.fromDateTimes(o.minus(r),o)}static fromISO(e,n){let[r,o]=(e||"").split("/",2);if(r&&o){let i,s;try{i=$.fromISO(r,n),s=i.isValid}catch(u){s=!1}let a,l;try{a=$.fromISO(o,n),l=a.isValid}catch(u){l=!1}if(s&&l)return he.fromDateTimes(i,a);if(s){let u=ie.fromISO(o,n);if(u.isValid)return he.after(i,u)}else if(l){let u=ie.fromISO(r,n);if(u.isValid)return he.before(a,u)}}return he.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds",n){if(!this.isValid)return NaN;let r=this.start.startOf(e,n),o;return n!=null&&n.useLocaleWeeks?o=this.end.reconfigure({locale:r.locale}):o=this.end,o=o.startOf(e,n),Math.floor(o.diff(r,e).get(e))+(o.valueOf()!==this.end.valueOf())}hasSame(e){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,e):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return this.isValid?this.s>e:!1}isBefore(e){return this.isValid?this.e<=e:!1}contains(e){return this.isValid?this.s<=e&&this.e>e:!1}set({start:e,end:n}={}){return this.isValid?he.fromDateTimes(e||this.s,n||this.e):this}splitAt(...e){if(!this.isValid)return[];let n=e.map(ui).filter(s=>this.contains(s)).sort((s,a)=>s.toMillis()-a.toMillis()),r=[],{s:o}=this,i=0;for(;o<this.e;){let s=n[i]||this.e,a=+s>+this.e?this.e:s;r.push(he.fromDateTimes(o,a)),o=a,i+=1}return r}splitBy(e){let n=ie.fromDurationLike(e);if(!this.isValid||!n.isValid||n.as("milliseconds")===0)return[];let{s:r}=this,o=1,i,s=[];for(;r<this.e;){let a=this.start.plus(n.mapUnits(l=>l*o));i=+a>+this.e?this.e:a,s.push(he.fromDateTimes(r,i)),r=i,o+=1}return s}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return this.isValid?+this.e==+e.s:!1}abutsEnd(e){return this.isValid?+e.e==+this.s:!1}engulfs(e){return this.isValid?this.s<=e.s&&this.e>=e.e:!1}equals(e){return!this.isValid||!e.isValid?!1:this.s.equals(e.s)&&this.e.equals(e.e)}intersection(e){if(!this.isValid)return this;let n=this.s>e.s?this.s:e.s,r=this.e<e.e?this.e:e.e;return n>=r?null:he.fromDateTimes(n,r)}union(e){if(!this.isValid)return this;let n=this.s<e.s?this.s:e.s,r=this.e>e.e?this.e:e.e;return he.fromDateTimes(n,r)}static merge(e){let[n,r]=e.sort((o,i)=>o.s-i.s).reduce(([o,i],s)=>i?i.overlaps(s)||i.abutsStart(s)?[o,i.union(s)]:[o.concat([i]),s]:[o,s],[[],null]);return r&&n.push(r),n}static xor(e){let n=null,r=0,o=[],i=e.map(l=>[{time:l.s,type:"s"},{time:l.e,type:"e"}]),s=Array.prototype.concat(...i),a=s.sort((l,u)=>l.time-u.time);for(let l of a)r+=l.type==="s"?1:-1,r===1?n=l.time:(n&&+n!=+l.time&&o.push(he.fromDateTimes(n,l.time)),n=null);return he.merge(o)}difference(...e){return he.xor([this].concat(e)).map(n=>this.intersection(n)).filter(n=>n&&!n.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} \u2013 ${this.e.toISO()})`:li}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`:`Interval { Invalid, reason: ${this.invalidReason} }`}toLocaleString(e=Cr,n={}){return this.isValid?ke.create(this.s.loc.clone(n),e).formatInterval(this):li}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:li}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:li}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:li}toFormat(e,{separator:n=" \u2013 "}={}){return this.isValid?`${this.s.toFormat(e)}${n}${this.e.toFormat(e)}`:li}toDuration(e,n){return this.isValid?this.e.diff(this.s,e,n):ie.invalid(this.invalidReason)}mapEndpoints(e){return he.fromDateTimes(e(this.s),e(this.e))}};var er=class{static hasDST(e=fe.defaultZone){let n=$.now().setZone(e).set({month:12});return!e.isUniversal&&n.offset!==n.set({month:6}).offset}static isValidIANAZone(e){return $e.isValidZone(e)}static normalizeZone(e){return fn(e,fe.defaultZone)}static getStartOfWeek({locale:e=null,locObj:n=null}={}){return(n||le.create(e)).getStartOfWeek()}static getMinimumDaysInFirstWeek({locale:e=null,locObj:n=null}={}){return(n||le.create(e)).getMinDaysInFirstWeek()}static getWeekendWeekdays({locale:e=null,locObj:n=null}={}){return(n||le.create(e)).getWeekendDays().slice()}static months(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null,outputCalendar:i="gregory"}={}){return(o||le.create(n,r,i)).months(e)}static monthsFormat(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null,outputCalendar:i="gregory"}={}){return(o||le.create(n,r,i)).months(e,!0)}static weekdays(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null}={}){return(o||le.create(n,r,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null}={}){return(o||le.create(n,r,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return le.create(e).meridiems()}static eras(e="short",{locale:n=null}={}){return le.create(n,null,"gregory").eras(e)}static features(){return{relative:Al(),localeWeek:Ll()}}};function Ky(t,e){let n=o=>o.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=n(e)-n(t);return Math.floor(ie.fromMillis(r).as("days"))}function lx(t,e,n){let r=[["years",(l,u)=>u.year-l.year],["quarters",(l,u)=>u.quarter-l.quarter+(u.year-l.year)*4],["months",(l,u)=>u.month-l.month+(u.year-l.year)*12],["weeks",(l,u)=>{let f=Ky(l,u);return(f-f%7)/7}],["days",Ky]],o={},i=t,s,a;for(let[l,u]of r)n.indexOf(l)>=0&&(s=l,o[l]=u(t,e),a=i.plus(o),a>e?(o[l]--,t=i.plus(o),t>e&&(a=t,o[l]--,t=i.plus(o))):t=a);return[t,o,a,s]}function Qy(t,e,n,r){let[o,i,s,a]=lx(t,e,n),l=e-o,u=n.filter(d=>["hours","minutes","seconds","milliseconds"].indexOf(d)>=0);u.length===0&&(s<e&&(s=o.plus({[a]:1})),s!==o&&(i[a]=(i[a]||0)+l/(s-o)));let f=ie.fromObject(i,r);return u.length>0?ie.fromMillis(l,r).shiftTo(...u).plus(f):f}var ed={arab:"[\u0660-\u0669]",arabext:"[\u06F0-\u06F9]",bali:"[\u1B50-\u1B59]",beng:"[\u09E6-\u09EF]",deva:"[\u0966-\u096F]",fullwide:"[\uFF10-\uFF19]",gujr:"[\u0AE6-\u0AEF]",hanidec:"[\u3007|\u4E00|\u4E8C|\u4E09|\u56DB|\u4E94|\u516D|\u4E03|\u516B|\u4E5D]",khmr:"[\u17E0-\u17E9]",knda:"[\u0CE6-\u0CEF]",laoo:"[\u0ED0-\u0ED9]",limb:"[\u1946-\u194F]",mlym:"[\u0D66-\u0D6F]",mong:"[\u1810-\u1819]",mymr:"[\u1040-\u1049]",orya:"[\u0B66-\u0B6F]",tamldec:"[\u0BE6-\u0BEF]",telu:"[\u0C66-\u0C6F]",thai:"[\u0E50-\u0E59]",tibt:"[\u0F20-\u0F29]",latn:"\\d"},Jy={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},ux=ed.hanidec.replace(/[\[|\]]/g,"").split("");function Xy(t){let e=parseInt(t,10);if(isNaN(e)){e="";for(let n=0;n<t.length;n++){let r=t.charCodeAt(n);if(t[n].search(ed.hanidec)!==-1)e+=ux.indexOf(t[n]);else for(let o in Jy){let[i,s]=Jy[o];r>=i&&r<=s&&(e+=r-i)}}return parseInt(e,10)}else return e}function Gt({numberingSystem:t},e=""){return new RegExp(`${ed[t||"latn"]}${e}`)}var cx="missing Intl.DateTimeFormat.formatToParts support";function de(t,e=n=>n){return{regex:t,deser:([n])=>e(Xy(n))}}var fx=String.fromCharCode(160),ng=`[ ${fx}]`,rg=new RegExp(ng,"g");function dx(t){return t.replace(/\./g,"\\.?").replace(rg,ng)}function eg(t){return t.replace(/\./g,"").replace(rg," ").toLowerCase()}function dn(t,e){return t===null?null:{regex:RegExp(t.map(dx).join("|")),deser:([n])=>t.findIndex(r=>eg(n)===eg(r))+e}}function tg(t,e){return{regex:t,deser:([,n,r])=>co(n,r),groups:e}}function Wl(t){return{regex:t,deser:([e])=>e}}function mx(t){return t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function px(t,e){let n=Gt(e),r=Gt(e,"{2}"),o=Gt(e,"{3}"),i=Gt(e,"{4}"),s=Gt(e,"{6}"),a=Gt(e,"{1,2}"),l=Gt(e,"{1,3}"),u=Gt(e,"{1,6}"),f=Gt(e,"{1,9}"),d=Gt(e,"{2,4}"),m=Gt(e,"{4,6}"),c=S=>({regex:RegExp(mx(S.val)),deser:([p])=>p,literal:!0}),v=(S=>{if(t.literal)return c(S);switch(S.val){case"G":return dn(e.eras("short"),0);case"GG":return dn(e.eras("long"),0);case"y":return de(u);case"yy":return de(d,$s);case"yyyy":return de(i);case"yyyyy":return de(m);case"yyyyyy":return de(s);case"M":return de(a);case"MM":return de(r);case"MMM":return dn(e.months("short",!0),1);case"MMMM":return dn(e.months("long",!0),1);case"L":return de(a);case"LL":return de(r);case"LLL":return dn(e.months("short",!1),1);case"LLLL":return dn(e.months("long",!1),1);case"d":return de(a);case"dd":return de(r);case"o":return de(l);case"ooo":return de(o);case"HH":return de(r);case"H":return de(a);case"hh":return de(r);case"h":return de(a);case"mm":return de(r);case"m":return de(a);case"q":return de(a);case"qq":return de(r);case"s":return de(a);case"ss":return de(r);case"S":return de(l);case"SSS":return de(o);case"u":return Wl(f);case"uu":return Wl(a);case"uuu":return de(n);case"a":return dn(e.meridiems(),0);case"kkkk":return de(i);case"kk":return de(d,$s);case"W":return de(a);case"WW":return de(r);case"E":case"c":return de(n);case"EEE":return dn(e.weekdays("short",!1),1);case"EEEE":return dn(e.weekdays("long",!1),1);case"ccc":return dn(e.weekdays("short",!0),1);case"cccc":return dn(e.weekdays("long",!0),1);case"Z":case"ZZ":return tg(new RegExp(`([+-]${a.source})(?::(${r.source}))?`),2);case"ZZZ":return tg(new RegExp(`([+-]${a.source})(${r.source})?`),2);case"z":return Wl(/[a-z_+-/]{1,256}?/i);case" ":return Wl(/[^\S\n\r]/);default:return c(S)}})(t)||{invalidReason:cx};return v.token=t,v}var hx={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function yx(t,e,n){let{type:r,value:o}=t;if(r==="literal"){let l=/^\s+$/.test(o);return{literal:!l,val:l?" ":o}}let i=e[r],s=r;r==="hour"&&(e.hour12!=null?s=e.hour12?"hour12":"hour24":e.hourCycle!=null?e.hourCycle==="h11"||e.hourCycle==="h12"?s="hour12":s="hour24":s=n.hour12?"hour12":"hour24");let a=hx[s];if(typeof a=="object"&&(a=a[i]),a)return{literal:!1,val:a}}function gx(t){return[`^${t.map(n=>n.regex).reduce((n,r)=>`${n}(${r.source})`,"")}$`,t]}function Dx(t,e,n){let r=t.match(e);if(r){let o={},i=1;for(let s in n)if(Fr(n,s)){let a=n[s],l=a.groups?a.groups+1:1;!a.literal&&a.token&&(o[a.token.val[0]]=a.deser(r.slice(i,i+l))),i+=l}return[r,o]}else return[r,{}]}function vx(t){let e=i=>{switch(i){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}},n=null,r;return J(t.z)||(n=$e.create(t.z)),J(t.Z)||(n||(n=new _e(t.Z)),r=t.Z),J(t.q)||(t.M=(t.q-1)*3+1),J(t.h)||(t.h<12&&t.a===1?t.h+=12:t.h===12&&t.a===0&&(t.h=0)),t.G===0&&t.y&&(t.y=-t.y),J(t.u)||(t.S=js(t.u)),[Object.keys(t).reduce((i,s)=>{let a=e(s);return a&&(i[a]=t[s]),i},{}),n,r]}var td=null;function Ex(){return td||(td=$.fromMillis(1555555555555)),td}function wx(t,e){if(t.literal)return t;let n=ke.macroTokenToFormatOpts(t.val),r=od(n,e);return r==null||r.includes(void 0)?t:r}function nd(t,e){return Array.prototype.concat(...t.map(n=>wx(n,e)))}function rd(t,e,n){let r=nd(ke.parseFormat(n),t),o=r.map(s=>px(s,t)),i=o.find(s=>s.invalidReason);if(i)return{input:e,tokens:r,invalidReason:i.invalidReason};{let[s,a]=gx(o),l=RegExp(s,"i"),[u,f]=Dx(e,l,a),[d,m,c]=f?vx(f):[null,null,void 0];if(Fr(f,"a")&&Fr(f,"H"))throw new ln("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:r,regex:l,rawMatches:u,matches:f,result:d,zone:m,specificOffset:c}}}function og(t,e,n){let{result:r,zone:o,specificOffset:i,invalidReason:s}=rd(t,e,n);return[r,o,i,s]}function od(t,e){if(!t)return null;let r=ke.create(e,t).dtFormatter(Ex()),o=r.formatToParts(),i=r.resolvedOptions();return o.map(s=>yx(s,t,i))}var id="Invalid DateTime",ig=864e13;function Hl(t){return new qe("unsupported zone",`the zone "${t.name}" is not supported`)}function sd(t){return t.weekData===null&&(t.weekData=Hs(t.c)),t.weekData}function ad(t){return t.localWeekData===null&&(t.localWeekData=Hs(t.c,t.loc.getMinDaysInFirstWeek(),t.loc.getStartOfWeek())),t.localWeekData}function ho(t,e){let n={ts:t.ts,zone:t.zone,c:t.c,o:t.o,loc:t.loc,invalid:t.invalid};return new $({...n,...e,old:n})}function dg(t,e,n){let r=t-e*60*1e3,o=n.offset(r);if(e===o)return[r,e];r-=(o-e)*60*1e3;let i=n.offset(r);return o===i?[r,o]:[t-Math.min(o,i)*60*1e3,Math.max(o,i)]}function zl(t,e){t+=e*60*1e3;let n=new Date(t);return{year:n.getUTCFullYear(),month:n.getUTCMonth()+1,day:n.getUTCDate(),hour:n.getUTCHours(),minute:n.getUTCMinutes(),second:n.getUTCSeconds(),millisecond:n.getUTCMilliseconds()}}function jl(t,e,n){return dg(Qo(t),e,n)}function sg(t,e){let n=t.o,r=t.c.year+Math.trunc(e.years),o=t.c.month+Math.trunc(e.months)+Math.trunc(e.quarters)*3,i={...t.c,year:r,month:o,day:Math.min(t.c.day,ei(r,o))+Math.trunc(e.days)+Math.trunc(e.weeks)*7},s=ie.fromObject({years:e.years-Math.trunc(e.years),quarters:e.quarters-Math.trunc(e.quarters),months:e.months-Math.trunc(e.months),weeks:e.weeks-Math.trunc(e.weeks),days:e.days-Math.trunc(e.days),hours:e.hours,minutes:e.minutes,seconds:e.seconds,milliseconds:e.milliseconds}).as("milliseconds"),a=Qo(i),[l,u]=dg(a,n,t.zone);return s!==0&&(l+=s,u=t.zone.offset(l)),{ts:l,o:u}}function Zs(t,e,n,r,o,i){let{setZone:s,zone:a}=n;if(t&&Object.keys(t).length!==0||e){let l=e||a,u=$.fromObject(t,{...n,zone:l,specificOffset:i});return s?u:u.setZone(a)}else return $.invalid(new qe("unparsable",`the input "${o}" can't be parsed as ${r}`))}function Ul(t,e,n=!0){return t.isValid?ke.create(le.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(t,e):null}function ld(t,e){let n=t.c.year>9999||t.c.year<0,r="";return n&&t.c.year>=0&&(r+="+"),r+=Fe(t.c.year,n?6:4),e?(r+="-",r+=Fe(t.c.month),r+="-",r+=Fe(t.c.day)):(r+=Fe(t.c.month),r+=Fe(t.c.day)),r}function ag(t,e,n,r,o,i){let s=Fe(t.c.hour);return e?(s+=":",s+=Fe(t.c.minute),(t.c.millisecond!==0||t.c.second!==0||!n)&&(s+=":")):s+=Fe(t.c.minute),(t.c.millisecond!==0||t.c.second!==0||!n)&&(s+=Fe(t.c.second),(t.c.millisecond!==0||!r)&&(s+=".",s+=Fe(t.c.millisecond,3))),o&&(t.isOffsetFixed&&t.offset===0&&!i?s+="Z":t.o<0?(s+="-",s+=Fe(Math.trunc(-t.o/60)),s+=":",s+=Fe(Math.trunc(-t.o%60))):(s+="+",s+=Fe(Math.trunc(t.o/60)),s+=":",s+=Fe(Math.trunc(t.o%60)))),i&&(s+="["+t.zone.ianaName+"]"),s}var mg={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},Sx={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},Cx={ordinal:1,hour:0,minute:0,second:0,millisecond:0},pg=["year","month","day","hour","minute","second","millisecond"],xx=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],Tx=["year","ordinal","hour","minute","second","millisecond"];function Fx(t){let e={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[t.toLowerCase()];if(!e)throw new Ko(t);return e}function lg(t){switch(t.toLowerCase()){case"localweekday":case"localweekdays":return"localWeekday";case"localweeknumber":case"localweeknumbers":return"localWeekNumber";case"localweekyear":case"localweekyears":return"localWeekYear";default:return Fx(t)}}function ug(t,e){let n=fn(e.zone,fe.defaultZone),r=le.fromObject(e),o=fe.now(),i,s;if(J(t.year))i=o;else{for(let u of pg)J(t[u])&&(t[u]=mg[u]);let a=jf(t)||$f(t);if(a)return $.invalid(a);let l=n.offset(o);[i,s]=jl(t,l,n)}return new $({ts:i,zone:n,loc:r,o:s})}function cg(t,e,n){let r=J(n.round)?!0:n.round,o=(s,a)=>(s=Jo(s,r||n.calendary?0:2,!0),e.loc.clone(n).relFormatter(n).format(s,a)),i=s=>n.calendary?e.hasSame(t,s)?0:e.startOf(s).diff(t.startOf(s),s).get(s):e.diff(t,s).get(s);if(n.unit)return o(i(n.unit),n.unit);for(let s of n.units){let a=i(s);if(Math.abs(a)>=1)return o(a,s)}return o(t>e?-0:0,n.units[n.units.length-1])}function fg(t){let e={},n;return t.length>0&&typeof t[t.length-1]=="object"?(e=t[t.length-1],n=Array.from(t).slice(0,t.length-1)):n=Array.from(t),[e,n]}var $=class{constructor(e){let n=e.zone||fe.defaultZone,r=e.invalid||(Number.isNaN(e.ts)?new qe("invalid input"):null)||(n.isValid?null:Hl(n));this.ts=J(e.ts)?fe.now():e.ts;let o=null,i=null;if(!r)if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(n))[o,i]=[e.old.c,e.old.o];else{let a=n.offset(this.ts);o=zl(this.ts,a),r=Number.isNaN(o.year)?new qe("invalid input"):null,o=r?null:o,i=r?null:a}this._zone=n,this.loc=e.loc||le.create(),this.invalid=r,this.weekData=null,this.localWeekData=null,this.c=o,this.o=i,this.isLuxonDateTime=!0}static now(){return new $({})}static local(){let[e,n]=fg(arguments),[r,o,i,s,a,l,u]=n;return ug({year:r,month:o,day:i,hour:s,minute:a,second:l,millisecond:u},e)}static utc(){let[e,n]=fg(arguments),[r,o,i,s,a,l,u]=n;return e.zone=_e.utcInstance,ug({year:r,month:o,day:i,hour:s,minute:a,second:l,millisecond:u},e)}static fromJSDate(e,n={}){let r=Ty(e)?e.valueOf():NaN;if(Number.isNaN(r))return $.invalid("invalid input");let o=fn(n.zone,fe.defaultZone);return o.isValid?new $({ts:r,zone:o,loc:le.fromObject(n)}):$.invalid(Hl(o))}static fromMillis(e,n={}){if(Mn(e))return e<-ig||e>ig?$.invalid("Timestamp out of range"):new $({ts:e,zone:fn(n.zone,fe.defaultZone),loc:le.fromObject(n)});throw new Ve(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,n={}){if(Mn(e))return new $({ts:e*1e3,zone:fn(n.zone,fe.defaultZone),loc:le.fromObject(n)});throw new Ve("fromSeconds requires a numerical input")}static fromObject(e,n={}){e=e||{};let r=fn(n.zone,fe.defaultZone);if(!r.isValid)return $.invalid(Hl(r));let o=le.fromObject(n),i=ti(e,lg),{minDaysInFirstWeek:s,startOfWeek:a}=Uf(i,o),l=fe.now(),u=J(n.specificOffset)?r.offset(l):n.specificOffset,f=!J(i.ordinal),d=!J(i.year),m=!J(i.month)||!J(i.day),c=d||m,y=i.weekYear||i.weekNumber;if((c||f)&&y)throw new ln("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(m&&f)throw new ln("Can't mix ordinal dates with month/day");let v=y||i.weekday&&!c,S,p,h=zl(l,u);v?(S=xx,p=Sx,h=Hs(h,s,a)):f?(S=Tx,p=Cx,h=Vl(h)):(S=pg,p=mg);let E=!1;for(let M of S){let ee=i[M];J(ee)?E?i[M]=p[M]:i[M]=h[M]:E=!0}let D=v?Sy(i,s,a):f?Cy(i):jf(i),x=D||$f(i);if(x)return $.invalid(x);let O=v?Hf(i,s,a):f?zf(i):i,[F,k]=jl(O,u,r),P=new $({ts:F,zone:r,o:k,loc:o});return i.weekday&&c&&e.weekday!==P.weekday?$.invalid("mismatched weekday",`you can't specify both a weekday of ${i.weekday} and a date of ${P.toISO()}`):P}static fromISO(e,n={}){let[r,o]=Wy(e);return Zs(r,o,n,"ISO 8601",e)}static fromRFC2822(e,n={}){let[r,o]=Hy(e);return Zs(r,o,n,"RFC 2822",e)}static fromHTTP(e,n={}){let[r,o]=zy(e);return Zs(r,o,n,"HTTP",n)}static fromFormat(e,n,r={}){if(J(e)||J(n))throw new Ve("fromFormat requires an input string and a format");let{locale:o=null,numberingSystem:i=null}=r,s=le.fromOpts({locale:o,numberingSystem:i,defaultToEN:!0}),[a,l,u,f]=og(s,e,n);return f?$.invalid(f):Zs(a,l,r,`format ${n}`,e,u)}static fromString(e,n,r={}){return $.fromFormat(e,n,r)}static fromSQL(e,n={}){let[r,o]=$y(e);return Zs(r,o,n,"SQL",e)}static invalid(e,n=null){if(!e)throw new Ve("need to specify a reason the DateTime is invalid");let r=e instanceof qe?e:new qe(e,n);if(fe.throwOnInvalid)throw new _l(r);return new $({invalid:r})}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,n={}){let r=od(e,le.fromObject(n));return r?r.map(o=>o?o.val:null).join(""):null}static expandFormat(e,n={}){return nd(ke.parseFormat(e),le.fromObject(n)).map(o=>o.val).join("")}get(e){return this[e]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?sd(this).weekYear:NaN}get weekNumber(){return this.isValid?sd(this).weekNumber:NaN}get weekday(){return this.isValid?sd(this).weekday:NaN}get isWeekend(){return this.isValid&&this.loc.getWeekendDays().includes(this.weekday)}get localWeekday(){return this.isValid?ad(this).weekday:NaN}get localWeekNumber(){return this.isValid?ad(this).weekNumber:NaN}get localWeekYear(){return this.isValid?ad(this).weekYear:NaN}get ordinal(){return this.isValid?Vl(this.c).ordinal:NaN}get monthShort(){return this.isValid?er.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?er.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?er.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?er.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];let e=864e5,n=6e4,r=Qo(this.c),o=this.zone.offset(r-e),i=this.zone.offset(r+e),s=this.zone.offset(r-o*n),a=this.zone.offset(r-i*n);if(s===a)return[this];let l=r-s*n,u=r-a*n,f=zl(l,s),d=zl(u,a);return f.hour===d.hour&&f.minute===d.minute&&f.second===d.second&&f.millisecond===d.millisecond?[ho(this,{ts:l}),ho(this,{ts:u})]:[this]}get isInLeapYear(){return mo(this.year)}get daysInMonth(){return ei(this.year,this.month)}get daysInYear(){return this.isValid?Tr(this.year):NaN}get weeksInWeekYear(){return this.isValid?fo(this.weekYear):NaN}get weeksInLocalWeekYear(){return this.isValid?fo(this.localWeekYear,this.loc.getMinDaysInFirstWeek(),this.loc.getStartOfWeek()):NaN}resolvedLocaleOptions(e={}){let{locale:n,numberingSystem:r,calendar:o}=ke.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:n,numberingSystem:r,outputCalendar:o}}toUTC(e=0,n={}){return this.setZone(_e.instance(e),n)}toLocal(){return this.setZone(fe.defaultZone)}setZone(e,{keepLocalTime:n=!1,keepCalendarTime:r=!1}={}){if(e=fn(e,fe.defaultZone),e.equals(this.zone))return this;if(e.isValid){let o=this.ts;if(n||r){let i=e.offset(this.ts),s=this.toObject();[o]=jl(s,i,e)}return ho(this,{ts:o,zone:e})}else return $.invalid(Hl(e))}reconfigure({locale:e,numberingSystem:n,outputCalendar:r}={}){let o=this.loc.clone({locale:e,numberingSystem:n,outputCalendar:r});return ho(this,{loc:o})}setLocale(e){return this.reconfigure({locale:e})}set(e){if(!this.isValid)return this;let n=ti(e,lg),{minDaysInFirstWeek:r,startOfWeek:o}=Uf(n,this.loc),i=!J(n.weekYear)||!J(n.weekNumber)||!J(n.weekday),s=!J(n.ordinal),a=!J(n.year),l=!J(n.month)||!J(n.day),u=a||l,f=n.weekYear||n.weekNumber;if((u||s)&&f)throw new ln("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(l&&s)throw new ln("Can't mix ordinal dates with month/day");let d;i?d=Hf({...Hs(this.c,r,o),...n},r,o):J(n.ordinal)?(d={...this.toObject(),...n},J(n.day)&&(d.day=Math.min(ei(d.year,d.month),d.day))):d=zf({...Vl(this.c),...n});let[m,c]=jl(d,this.o,this.zone);return ho(this,{ts:m,o:c})}plus(e){if(!this.isValid)return this;let n=ie.fromDurationLike(e);return ho(this,sg(this,n))}minus(e){if(!this.isValid)return this;let n=ie.fromDurationLike(e).negate();return ho(this,sg(this,n))}startOf(e,{useLocaleWeeks:n=!1}={}){if(!this.isValid)return this;let r={},o=ie.normalizeUnit(e);switch(o){case"years":r.month=1;case"quarters":case"months":r.day=1;case"weeks":case"days":r.hour=0;case"hours":r.minute=0;case"minutes":r.second=0;case"seconds":r.millisecond=0;break;case"milliseconds":break}if(o==="weeks")if(n){let i=this.loc.getStartOfWeek(),{weekday:s}=this;s<i&&(r.weekNumber=this.weekNumber-1),r.weekday=i}else r.weekday=1;if(o==="quarters"){let i=Math.ceil(this.month/3);r.month=(i-1)*3+1}return this.set(r)}endOf(e,n){return this.isValid?this.plus({[e]:1}).startOf(e,n).minus(1):this}toFormat(e,n={}){return this.isValid?ke.create(this.loc.redefaultToEN(n)).formatDateTimeFromString(this,e):id}toLocaleString(e=Cr,n={}){return this.isValid?ke.create(this.loc.clone(n),e).formatDateTime(this):id}toLocaleParts(e={}){return this.isValid?ke.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:n=!1,suppressMilliseconds:r=!1,includeOffset:o=!0,extendedZone:i=!1}={}){if(!this.isValid)return null;let s=e==="extended",a=ld(this,s);return a+="T",a+=ag(this,s,n,r,o,i),a}toISODate({format:e="extended"}={}){return this.isValid?ld(this,e==="extended"):null}toISOWeekDate(){return Ul(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:n=!1,includeOffset:r=!0,includePrefix:o=!1,extendedZone:i=!1,format:s="extended"}={}){return this.isValid?(o?"T":"")+ag(this,s==="extended",n,e,r,i):null}toRFC2822(){return Ul(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return Ul(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?ld(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:n=!1,includeOffsetSpace:r=!0}={}){let o="HH:mm:ss.SSS";return(n||e)&&(r&&(o+=" "),n?o+="z":e&&(o+="ZZ")),Ul(this,o,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():id}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`:`DateTime { Invalid, reason: ${this.invalidReason} }`}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};let n={...this.c};return e.includeConfig&&(n.outputCalendar=this.outputCalendar,n.numberingSystem=this.loc.numberingSystem,n.locale=this.loc.locale),n}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,n="milliseconds",r={}){if(!this.isValid||!e.isValid)return ie.invalid("created by diffing an invalid DateTime");let o={locale:this.locale,numberingSystem:this.numberingSystem,...r},i=Fy(n).map(ie.normalizeUnit),s=e.valueOf()>this.valueOf(),a=s?this:e,l=s?e:this,u=Qy(a,l,i,o);return s?u.negate():u}diffNow(e="milliseconds",n={}){return this.diff($.now(),e,n)}until(e){return this.isValid?he.fromDateTimes(this,e):this}hasSame(e,n,r){if(!this.isValid)return!1;let o=e.valueOf(),i=this.setZone(e.zone,{keepLocalTime:!0});return i.startOf(n,r)<=o&&o<=i.endOf(n,r)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;let n=e.base||$.fromObject({},{zone:this.zone}),r=e.padding?this<n?-e.padding:e.padding:0,o=["years","months","days","hours","minutes","seconds"],i=e.unit;return Array.isArray(e.unit)&&(o=e.unit,i=void 0),cg(n,this.plus(r),{...e,numeric:"always",units:o,unit:i})}toRelativeCalendar(e={}){return this.isValid?cg(e.base||$.fromObject({},{zone:this.zone}),this,{...e,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...e){if(!e.every($.isDateTime))throw new Ve("min requires all arguments be DateTimes");return Gf(e,n=>n.valueOf(),Math.min)}static max(...e){if(!e.every($.isDateTime))throw new Ve("max requires all arguments be DateTimes");return Gf(e,n=>n.valueOf(),Math.max)}static fromFormatExplain(e,n,r={}){let{locale:o=null,numberingSystem:i=null}=r,s=le.fromOpts({locale:o,numberingSystem:i,defaultToEN:!0});return rd(s,e,n)}static fromStringExplain(e,n,r={}){return $.fromFormatExplain(e,n,r)}static get DATE_SHORT(){return Cr}static get DATE_MED(){return Es}static get DATE_MED_WITH_WEEKDAY(){return Tf}static get DATE_FULL(){return ws}static get DATE_HUGE(){return Ss}static get TIME_SIMPLE(){return Cs}static get TIME_WITH_SECONDS(){return xs}static get TIME_WITH_SHORT_OFFSET(){return Ts}static get TIME_WITH_LONG_OFFSET(){return Fs}static get TIME_24_SIMPLE(){return _s}static get TIME_24_WITH_SECONDS(){return ks}static get TIME_24_WITH_SHORT_OFFSET(){return Os}static get TIME_24_WITH_LONG_OFFSET(){return bs}static get DATETIME_SHORT(){return Rs}static get DATETIME_SHORT_WITH_SECONDS(){return Ns}static get DATETIME_MED(){return Ms}static get DATETIME_MED_WITH_SECONDS(){return Is}static get DATETIME_MED_WITH_WEEKDAY(){return Ff}static get DATETIME_FULL(){return As}static get DATETIME_FULL_WITH_SECONDS(){return Ls}static get DATETIME_HUGE(){return Ps}static get DATETIME_HUGE_WITH_SECONDS(){return Bs}};function ui(t){if($.isDateTime(t))return t;if(t&&t.valueOf&&Mn(t.valueOf()))return $.fromJSDate(t);if(t&&typeof t=="object")return $.fromObject(t);throw new Ve(`Unknown datetime argument: ${t}, of type ${typeof t}`)}function ud(t){return typeof t=="string"?new Date(t):t}function hg(t,e){let n=$.fromJSDate(t);return $.fromJSDate(e).diff(n,"days").days}function qs(t){return`${t.getFullYear()}-${t.getMonth()<9?"0"+(t.getMonth()+1):t.getMonth()+1}-${t.getDate()<10?"0"+t.getDate():t.getDate()}`}function $l(t,e){return(e-t+7)%7}function cd(t,e){return(t-e+6)%7}function yg(t){let e=new Date,n=t<=1?1:t,r=new Date(e.getFullYear()-n+1,0,1),o=new Date(e.getFullYear(),12,0);return{start:r,end:o}}function gg(t){let e=new Date,n=t<=1?1:t,r=new Date(e.getFullYear(),e.getMonth()-n+1,1),o=new Date(e.getFullYear(),e.getMonth()+1,0);return{start:r,end:o}}function ci(t,e,n,r){let o=new Map;for(let i of t){let s=e(i);o.has(s)?o.set(s,r(o.get(s),n(i))):o.set(s,n(i))}return o}function Dg(t,e){for(let n=0;n<e.length;n++)if(t>=e[n].min&&t<e[n].max)return e[n];return null}function vg(t){let e=t.trim();if(e==="")return null;let n=Number(e);return Number.isNaN(n)?null:n}var Gl=[{id:"default_b",color:"#9be9a8",min:1,max:2},{id:"default_c",color:"#40c463",min:2,max:5},{id:"default_d",color:"#30a14e",min:5,max:10},{id:"default_e",color:"#216e39",min:10,max:999}];function Eg(t){if(!t||t.length===0)return[];let n=t.map(i=>i.date instanceof Date?{...i,timestamp:i.date.getTime()}:{...i,date:new Date(i.date),timestamp:new Date(i.date).getTime()}).sort((i,s)=>s.timestamp-i.timestamp),r=n[n.length-1].timestamp,o=n[0].timestamp;return Yl(new Date(r),new Date(o),t)}function Yl(t,e,n){let r=hg(t,e)+1,o=_x(n),i=[],s=$.fromJSDate(e);for(let a=0;a<r;a++){let l=s.minus({days:a}),u=l.toFormat("yyyy-MM-dd"),f=o.get(u);i.unshift({date:u,weekDay:l.weekday==7?0:l.weekday,month:l.month-1,monthDate:l.day,year:l.year,value:f?f.value:0,summary:f?f.summary:void 0,items:f?f.items||[]:[]})}return i}function wg(t,e=[]){let n=new Date;return n.setDate(n.getDate()-t+1),Yl(n,new Date,e)}function _x(t){let e=new Map;for(let n of t){let r;if(typeof n.date=="string"?r=n.date:r=$.fromJSDate(n.date).toFormat("yyyy-MM-dd"),e.has(r)){let o={...n,value:e.get(r).value+n.value};e.set(r,o)}else e.set(r,n)}return e}var Zl=class{constructor(){this.default="default";this.click_to_reset="click to reset";this.context_menu_create="Add Heatmap";this.form_basic_settings="Basic Settings";this.form_style_settings="Style Settings";this.form_about="About";this.form_contact_me="Contact me";this.form_project_url="Project";this.form_sponsor="Sponsor";this.form_title="Title";this.form_title_placeholder="Input title";this.form_graph_type="Graph Type";this.form_graph_type_git="Git Style";this.form_graph_type_month_track="Month Track";this.form_graph_type_calendar="Calendar";this.form_date_range="Date Range";this.form_date_range_latest_days="Latest Days";this.form_date_range_latest_month="Latest Whole Month";this.form_date_range_latest_year="Latest Whole Year";this.form_date_range_input_placeholder="Input number here";this.form_date_range_fixed_date="Fixed Date";this.form_date_range_start_date="Start Date";this.form_start_of_week="Start of Week";this.form_data_source_value="Source";this.form_data_source_filter_label="Filter";this.form_datasource_filter_type_none="None";this.form_datasource_filter_type_status_is="Status Is";this.form_datasource_filter_type_contains_any_tag="Contains Any Tag";this.form_datasource_filter_type_status_in="Status In";this.form_datasource_filter_task_none="None";this.form_datasource_filter_task_status_completed="Completed";this.form_datasource_filter_task_status_fully_completed="Fully completed";this.form_datasource_filter_task_status_any="Any Status";this.form_datasource_filter_task_status_incomplete="Incomplete";this.form_datasource_filter_task_status_canceled="Canceled";this.form_datasource_filter_contains_tag="Contains Any Tag";this.form_datasource_filter_contains_tag_input_placeholder="Please input tag, such as #todo";this.form_datasource_filter_customize="Customize";this.form_query_placeholder=' such as #tag or "folder"';this.form_date_field="Date Field";this.form_date_field_type_file_name="File Name";this.form_date_field_type_file_ctime="File Create Time";this.form_date_field_type_file_mtime="File Modify Time";this.form_date_field_type_file_specific_page_property="Specific Page Property";this.form_date_field_type_file_specific_task_property="Specific Task Property";this.form_date_field_placeholder="default is file's create time";this.form_date_field_format="Date Field Format";this.form_date_field_format_sample="Sample";this.form_date_field_format_description="If your date property value is not a standard format, you need to specify this field so that the system knows how to recognize your date format";this.form_date_field_format_placeholder="such as yyyy-MM-dd HH:mm:ss";this.form_date_field_format_type_smart="Auto Detect";this.form_date_field_format_type_manual="Specify Format";this.form_count_field_count_field_label="Count Field";this.form_count_field_count_field_input_placeholder="Please input property name";this.form_count_field_count_field_type_default="Default";this.form_count_field_count_field_type_page_prop="Page Property";this.form_count_field_count_field_type_task_prop="Task Property";this.form_title_font_size_label="Title font Size";this.form_number_input_min_warning="allow min value is {value}";this.form_number_input_max_warning="allow max value is {value}";this.form_fill_the_screen_label="Fill The Screen";this.form_main_container_bg_color="Background Color";this.form_enable_main_container_shadow="Enable Shadow";this.form_show_cell_indicators="Show Cell Indicators";this.form_cell_shape="Cell Shape";this.form_cell_shape_circle="Circle";this.form_cell_shape_square="Square";this.form_cell_shape_rounded="Rounded";this.form_cell_min_height="Min Height";this.form_cell_min_width="Min Width";this.form_datasource_type_page="Page";this.form_datasource_type_all_task="All Task";this.form_datasource_type_task_in_specific_page="Task in Specific Page";this.form_theme="Theme";this.form_theme_placeholder="Select theme or customize style";this.form_cell_style_rules="Cell Style Rules";this.form_button_preview="Preview";this.form_button_save="Save";this.weekday_sunday="Sunday";this.weekday_monday="Monday";this.weekday_tuesday="Tuesday";this.weekday_wednesday="Wednesday";this.weekday_thursday="Thursday";this.weekday_friday="Friday";this.weekday_saturday="Saturday";this.you_have_no_contributions_on="No contributions on {date}";this.you_have_contributed_to="{value} contributions on {date}";this.click_to_load_more="Click to load more..."}};var ql=class{constructor(){this.default="\u9ED8\u8BA4";this.click_to_reset="\u70B9\u51FB\u91CD\u7F6E";this.context_menu_create="\u65B0\u5EFA\u70ED\u529B\u56FE";this.form_basic_settings="\u57FA\u7840\u8BBE\u7F6E";this.form_style_settings="\u6837\u5F0F\u8BBE\u7F6E";this.form_about="\u5173\u4E8E";this.form_contact_me="\u8054\u7CFB\u6211";this.form_project_url="\u9879\u76EE\u5730\u5740";this.form_sponsor="\u8D5E\u52A9";this.form_title="\u6807\u9898";this.form_title_placeholder="\u8F93\u5165\u6807\u9898";this.form_graph_type="\u56FE\u8868\u7C7B\u578B";this.form_graph_type_git="Git \u89C6\u56FE";this.form_graph_type_month_track="\u6708\u8FFD\u8E2A\u89C6\u56FE";this.form_graph_type_calendar="\u65E5\u5386\u89C6\u56FE";this.form_date_range="\u65E5\u671F\u8303\u56F4";this.form_date_range_latest_days="\u6700\u8FD1\u51E0\u5929";this.form_date_range_latest_month="\u6700\u8FD1\u51E0\u4E2A\u6574\u6708";this.form_date_range_latest_year="\u6700\u8FD1\u51E0\u4E2A\u6574\u5E74";this.form_date_range_input_placeholder="\u5728\u8FD9\u91CC\u8F93\u5165\u6570\u503C";this.form_date_range_fixed_date="\u56FA\u5B9A\u65E5\u671F";this.form_date_range_start_date="\u5F00\u59CB\u65E5\u671F";this.form_start_of_week="\u6BCF\u5468\u5F00\u59CB\u4E8E";this.form_data_source_value="\u6765\u6E90";this.form_data_source_filter_label="\u7B5B\u9009";this.form_datasource_filter_type_none="\u65E0";this.form_datasource_filter_type_status_is="\u72B6\u6001\u7B49\u4E8E";this.form_datasource_filter_type_contains_any_tag="\u5305\u542B\u4EFB\u610F\u6807\u7B7E";this.form_datasource_filter_type_status_in="\u5305\u542B\u4EFB\u610F\u4E00\u4E2A\u72B6\u6001";this.form_datasource_filter_task_none="\u65E0";this.form_datasource_filter_task_status_completed="\u5DF2\u5B8C\u6210\uFF08\u4E0D\u5305\u542B\u5B50\u4EFB\u52A1\uFF09";this.form_datasource_filter_task_status_fully_completed="\u5DF2\u5B8C\u6210\uFF08\u5305\u542B\u5B50\u4EFB\u52A1\uFF09";this.form_datasource_filter_task_status_canceled="\u5DF2\u53D6\u6D88";this.form_datasource_filter_task_status_any="\u4EFB\u610F\u72B6\u6001";this.form_datasource_filter_task_status_incomplete="\u672A\u5B8C\u6210";this.form_datasource_filter_contains_tag="\u5305\u542B\u4EFB\u610F\u4E00\u4E2A\u6807\u7B7E";this.form_datasource_filter_contains_tag_input_placeholder="\u8BF7\u8F93\u5165\u6807\u7B7E\uFF0C\u6BD4\u5982 #todo";this.form_datasource_filter_customize="\u81EA\u5B9A\u4E49";this.form_query_placeholder='\u6BD4\u5982 #tag \u6216 "folder"';this.form_date_field="\u65E5\u671F\u5B57\u6BB5";this.form_date_field_type_file_name="\u6587\u4EF6\u540D\u79F0";this.form_date_field_type_file_ctime="\u6587\u4EF6\u521B\u5EFA\u65E5\u671F";this.form_date_field_type_file_mtime="\u6587\u4EF6\u4FEE\u6539\u65E5\u671F";this.form_date_field_type_file_specific_page_property="\u6307\u5B9A\u6587\u6863\u5C5E\u6027";this.form_date_field_type_file_specific_task_property="\u6307\u5B9A\u4EFB\u52A1\u5C5E\u6027";this.form_date_field_placeholder="\u9ED8\u8BA4\u4E3A\u6587\u4EF6\u7684\u521B\u5EFA\u65E5\u671F";this.form_date_field_format="\u65E5\u671F\u683C\u5F0F";this.form_date_field_format_sample="\u793A\u4F8B\u503C";this.form_date_field_format_description="\u5982\u679C\u4F60\u7684\u65E5\u671F\u5C5E\u6027\u503C\u4E0D\u662F\u6807\u51C6\u7684\u683C\u5F0F\uFF0C\u9700\u8981\u6307\u5B9A\u8BE5\u5B57\u6BB5\u8BA9\u7CFB\u7EDF\u77E5\u9053\u5982\u4F55\u8BC6\u522B\u4F60\u7684\u65E5\u671F\u683C\u5F0F";this.form_date_field_format_placeholder="\u6BD4\u5982 yyyy-MM-dd HH:mm:ss";this.form_date_field_format_type_smart="\u81EA\u52A8\u8BC6\u522B";this.form_date_field_format_type_manual="\u6307\u5B9A\u683C\u5F0F";this.form_count_field_count_field_label="\u6253\u5206\u5C5E\u6027";this.form_count_field_count_field_input_placeholder="\u8BF7\u8F93\u5165\u5C5E\u6027\u540D\u79F0";this.form_count_field_count_field_type_default="\u9ED8\u8BA4";this.form_count_field_count_field_type_page_prop="\u6587\u6863\u5C5E\u6027";this.form_count_field_count_field_type_task_prop="\u4EFB\u52A1\u5C5E\u6027";this.form_title_font_size_label="\u6807\u9898\u5B57\u4F53\u5927\u5C0F";this.form_number_input_min_warning="\u5141\u8BB8\u7684\u6700\u5C0F\u503C\u4E3A {value}";this.form_number_input_max_warning="\u5141\u8BB8\u7684\u6700\u5927\u503C\u4E3A {value}";this.form_fill_the_screen_label="\u5145\u6EE1\u5C4F\u5E55";this.form_main_container_bg_color="\u80CC\u666F\u989C\u8272";this.form_enable_main_container_shadow="\u542F\u7528\u9634\u5F71";this.form_show_cell_indicators="\u663E\u793A\u5355\u5143\u683C\u6307\u793A\u5668";this.form_cell_shape="\u5355\u5143\u683C\u5F62\u72B6";this.form_cell_shape_circle="\u5706\u5F62";this.form_cell_shape_square="\u65B9\u5757";this.form_cell_shape_rounded="\u5706\u89D2";this.form_cell_min_height="\u5355\u5143\u683C\u6700\u5C0F\u9AD8\u5EA6";this.form_cell_min_width="\u5355\u5143\u683C\u6700\u5C0F\u5BBD\u5EA6";this.form_datasource_type_page="\u6587\u6863";this.form_datasource_type_all_task="\u6240\u6709\u4EFB\u52A1";this.form_datasource_type_task_in_specific_page="\u6307\u5B9A\u6587\u6863\u4E2D\u7684\u4EFB\u52A1";this.form_theme="\u4E3B\u9898";this.form_theme_placeholder="\u9009\u62E9\u4E3B\u9898\u6216\u81EA\u5B9A\u4E49\u6837\u5F0F";this.form_cell_style_rules="\u5355\u5143\u683C\u6837\u5F0F\u89C4\u5219";this.form_button_preview="\u9884\u89C8";this.form_button_save="\u4FDD\u5B58";this.weekday_sunday="\u5468\u65E5";this.weekday_monday="\u5468\u4E00";this.weekday_tuesday="\u5468\u4E8C";this.weekday_wednesday="\u5468\u4E09";this.weekday_thursday="\u5468\u56DB";this.weekday_friday="\u5468\u4E94";this.weekday_saturday="\u5468\u516D";this.you_have_no_contributions_on="\u4F60\u5728 {date} \u6CA1\u6709\u4EFB\u4F55\u8D21\u732E";this.you_have_contributed_to="\u4F60\u5728 {date} \u6709 {value} \u6B21\u8D21\u732E";this.click_to_load_more="\u70B9\u51FB\u52A0\u8F7D\u66F4\u591A......"}};var q=class{static get(){return window.localStorage.getItem("language")==="zh"?new ql:new Zl}};function Ks(){return window.localStorage.getItem("language")==="zh"}var kx=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],Ox=["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],Sg=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function fi(t){return window.localStorage.getItem("language")==="zh"?`${t+1}\u6708`:Sg[t]}function Kl(t,e){let n=window.localStorage.getItem("language"),r;return n==="zh"?r=Ox[t]:r=kx[t],e?r.substring(0,e):r}function Ql(t,e){return window.localStorage.getItem("language")==="zh"?`${t}\u5E74${e+1}\u6708`:`${Sg[e]} ${t}`}var Or=class{constructor(){}render(e,n){throw new Error("Method not implemented.")}createGraphEl(e){return createDiv({cls:"contribution-graph",parent:e})}createMainEl(e,n){let r="main";n.fillTheScreen&&this.graphType()!="calendar"&&(r=`main ${n.fillTheScreen?"fill-the-screen":""}`),n.enableMainContainerShadow&&(r+=" shadow");let o=createDiv({cls:r,parent:e});return n.mainContainerStyle&&Object.assign(o.style,n.mainContainerStyle),o}renderTitle(e,n){let r=document.createElement("div");return r.className="title",e.title&&(r.innerText=e.title),e.titleStyle&&Object.assign(r.style,e.titleStyle),n.appendChild(r),r}renderCellRuleIndicator(e,n){if(e.showCellRuleIndicators===!1)return;let r=createDiv({cls:"cell-rule-indicator-container",parent:n}),o=this.getCellRules(e);createDiv({cls:"cell text",text:"less",parent:r}),o.sort((i,s)=>i.min-s.min).forEach(i=>{let s=createDiv({cls:["cell"],parent:r});s.className="cell",s.style.backgroundColor=i.color,s.innerText=i.text||"";let a=`${i.min} \u2264 contributions \uFF1C ${i.max}`;s.ariaLabel=a}),createDiv({cls:"cell text",text:"more",parent:r})}renderActivityContainer(e,n){return createDiv({cls:"activity-container",parent:n})}renderActivity(e,n,r){r.empty();let o=createEl("button",{cls:"close-button",text:"x",parent:r});o.onclick=()=>{r.empty()};let i;if(n.value>0?i=q.get().you_have_contributed_to.replace("{date}",n.date).replace("{value}",n.value.toString()):i=q.get().you_have_no_contributions_on.replace("{date}",n.date).replace("{value}","0"),createDiv({cls:"activity-summary",parent:r,text:i}),(n.items||[]).length===0)return;let s=createDiv({cls:"activity-content",parent:r}),a=createDiv({cls:"activity-list",parent:s}),l=10,u=n.items||[];Cg(u.slice(0,l),a);let f=createDiv({cls:"activity-navigation",parent:s}),d=1;if(u.length>l){let m=createEl("a",{text:q.get().click_to_load_more,href:"#",parent:f});m.onclick=c=>{c.preventDefault(),d++,Cg(u.slice((d-1)*l,d*l),a),d*l>=u.length&&m.remove()}}}generateContributionData(e){if(e.days)return wg(e.days,e.data);if(e.fromDate&&e.toDate){let n=ud(e.fromDate),r=ud(e.toDate);return Yl(n,r,e.data)}else return Eg(e.data)}getCellRules(e){return e.cellStyleRules&&e.cellStyleRules.length>0?e.cellStyleRules:Gl}bindMonthTips(e,n,r){let o=`${n.year}-${n.month+1}`,i=r.get(o)||0;e.ariaLabel=`${i} contributions on ${o}.`}applyCellGlobalStyle(e,n){n.cellStyle&&Object.assign(e.style,n.cellStyle)}applyCellGlobalStylePartial(e,n,r){if(n.cellStyle){let o=r.reduce((i,s)=>(i[s]=n.cellStyle[s],i),{});Object.assign(e.style,o)}}applyCellStyleRule(e,n,r,o){let i=Dg(n.value,r);if(i!=null){e.style.backgroundColor=i.color,e.innerText=i.text||"";return}if(o){let s=o();e.style.backgroundColor=s.color,e.innerText=s.text||""}}bindCellAttribute(e,n){e.setAttribute("data-year",n.year.toString()),e.setAttribute("data-month",n.month.toString()),e.setAttribute("data-date",n.date.toString())}bindCellClickEvent(e,n,r,o){e.onclick=i=>{r.onCellClick&&r.onCellClick(n,i),o&&this.renderActivity(r,n,o)}}bindCellTips(e,n){let r=n.summary?n.summary:`${n.value} contributions on ${n.date}.`;e.ariaLabel=r}};function Cg(t,e){(t||[]).slice(0,10).forEach(n=>{var i;let r=createDiv({cls:"activity-item",parent:e}),o=createEl("a",{text:n.label,parent:r,cls:`label ${((i=n.link)==null?void 0:i.className)||""}`});if(o.ariaLabel=n.label,n.link){let s=n.link;o.setAttribute("data-href",s.href||"#"),o.setAttribute("href",s.href||"#"),o.setAttribute("target",s.target||"_blank"),o.setAttribute("rel",s.rel||"noopener")}o.onclick=s=>{n.open&&(s.preventDefault(),n.open(s))}})}var Jl=class extends Or{constructor(){super()}graphType(){return"calendar"}render(e,n){let r=this.createGraphEl(e),o=this.createMainEl(r,n);n.title&&n.title.trim()!=""&&this.renderTitle(n,o);let i=createDiv({cls:["charts","calendar"],parent:o});this.renderCellRuleIndicator(n,o);let s=this.renderActivityContainer(n,o),a=this.generateContributionData(n).filter(c=>c.date!="$HOLE$");if(a.length>0){let c=a[0],y=$.fromISO(c.date),v=y.startOf("month");for(let S=y.day-1;S>=v.day;S--){let p=v.plus({days:S-v.day});a.unshift({date:"$HOLE$",weekDay:p.weekday==7?0:p.weekday,month:p.month-1,monthDate:p.day,year:p.year,value:0})}}if(a.length>0){let c=a[a.length-1],y=$.fromISO(c.date),v=y.endOf("month");for(let S=y.day+1;S<=v.day;S++){let p=y.plus({days:S-y.day});a.push({date:"$HOLE$",weekDay:p.weekday==7?0:p.weekday,month:p.month-1,monthDate:p.day,year:p.year,value:0})}}let l=ci(a,c=>`${c.year}-${c.month+1}`,c=>c.value,(c,y)=>c+y),u=this.getCellRules(n),f="",d,m=null;for(let c=0;c<a.length;c++){let y=a[c],v=`${y.year}-${y.month+1}`;if(v!=f){f=v,d=document.createElement("div"),d.className="month-container",i.appendChild(d);let p=document.createElement("div");p.className="month-indicator",y.month==0?p.innerText=Ql(y.year,y.month):p.innerText=fi(y.month),d.appendChild(p),this.bindMonthTips(p,y,l);let h=createDiv({cls:["row","week-indicator-container"],parent:d});for(let D=0;D<7;D++){let x=document.createElement("div");x.className="cell week-indicator",this.applyCellGlobalStylePartial(x,n,["minWidth","minHeight"]);let O=Kl(((n.startOfWeek||0)+7+D)%7,2);x.innerText=O,h.appendChild(x)}m=document.createElement("div"),m.className="row",d==null||d.appendChild(m);let E=$l(n.startOfWeek||0,y.weekDay);for(let D=0;D<E;D++){let x=document.createElement("div");x.className="cell",this.applyCellGlobalStylePartial(x,n,["minWidth","minHeight"]),m==null||m.appendChild(x)}}(m==null||y.weekDay==(n.startOfWeek||0))&&(m=document.createElement("div"),m.className="row",d==null||d.appendChild(m));let S=document.createElement("div");if(m==null||m.appendChild(S),S.className="cell",y.date=="$HOLE$"?(S.innerText="\xB7\xB7\xB7",S.className="cell",this.applyCellGlobalStylePartial(S,n,["minWidth","minHeight"])):y.value==0?(S.className="cell empty",this.applyCellGlobalStyle(S,n),this.applyCellStyleRule(S,y,u),this.bindCellAttribute(S,y)):(S.className="cell",this.applyCellGlobalStyle(S,n),this.applyCellStyleRule(S,y,u,()=>u[0]),this.bindCellAttribute(S,y),this.bindCellClickEvent(S,y,n,s),this.bindCellTips(S,y)),c+1<a.length){if(a[c+1].month!=y.month){let h=cd(n.startOfWeek||0,y.weekDay);for(let E=0;E<h;E++){let D=document.createElement("div");D.className="cell",this.applyCellGlobalStylePartial(D,n,["minWidth","minHeight"]),m==null||m.appendChild(D)}}}else if(c+1==a.length){let p=cd(n.startOfWeek||0,y.weekDay);for(let h=0;h<p;h++){let E=document.createElement("div");E.className="cell",this.applyCellGlobalStylePartial(E,n,["minWidth","minHeight"]),m==null||m.appendChild(E)}}}}};var Xl=class extends Or{constructor(){super()}graphType(){return"month-track"}render(e,n){let r=this.createGraphEl(e),o=this.createMainEl(r,n);n.title&&n.title.trim()!=""&&this.renderTitle(n,o);let i=createDiv({cls:["charts","month-track"],parent:o});this.renderCellRuleIndicator(n,o);let s=createDiv({cls:"row",parent:i});s.appendChild(createDiv({cls:"cell month-indicator",text:""})),this.renderMonthDateIndicator(s,n);let a=this.renderActivityContainer(n,o),l=this.generateContributionData(n).filter(c=>c.date!="$HOLE$"),u=ci(l,c=>`${c.year}-${c.month+1}`,c=>c.value,(c,y)=>c+y),f=this.getCellRules(n),d,m="";for(let c=0;c<l.length;c++){let y=l[c],v=`${y.year}-${y.month}`;if(v!=m){if(c>0){let E=31-l[c-1].monthDate;for(let D=0;D<E;D++){let x=document.createElement("div");x.className="cell",this.applyCellGlobalStylePartial(x,n,["minWidth","minHeight"]),d==null||d.appendChild(x)}}d=document.createElement("div"),d.className="row",i.appendChild(d),m=v;let p=document.createElement("div");p.className="cell month-indicator",p.innerText=y.month==0?Ql(y.year,y.month):fi(y.month),this.bindMonthTips(p,y,u),d.appendChild(p)}if(c==0){let h=new Date(y.date).getDate()-1;for(let E=0;E<h;E++){let D=document.createElement("div");D.className="cell",D.innerText="\xB7\xB7\xB7",this.applyCellGlobalStylePartial(D,n,["minWidth","minHeight"]),d==null||d.appendChild(D)}}let S=document.createElement("div");this.applyCellGlobalStyle(S,n),d==null||d.appendChild(S),y.value==0?(S.className="cell empty",this.applyCellStyleRule(S,y,f),this.bindCellAttribute(S,y)):(S.className="cell",this.applyCellStyleRule(S,y,f,()=>f[0]),this.bindCellAttribute(S,y),this.bindCellClickEvent(S,y,n,a),this.bindCellTips(S,y))}if(l.length>0){let c=l[l.length-1],y=$.fromISO(c.date),v=y.endOf("month").day;for(let S=y.day;S<v;S++){let p=document.createElement("div");p.className="cell",this.applyCellGlobalStylePartial(p,n,["minWidth","minHeight"]),d==null||d.appendChild(p)}}}renderMonthDateIndicator(e,n){for(let r=0;r<31;r++){let o=document.createElement("div");o.className="cell date-indicator",this.applyCellGlobalStylePartial(o,n,["minWidth","minHeight"]),o.innerText=`${r+1}`,e.appendChild(o)}}};var eu=class extends Or{constructor(){super()}graphType(){return"default"}render(e,n){let r=this.createGraphEl(e),o=this.createMainEl(r,n);n.title&&n.title.trim()!=""&&this.renderTitle(n,o);let i=createDiv({cls:["charts","default"],parent:o});this.renderCellRuleIndicator(n,o);let s=this.renderActivityContainer(n,o),a=createDiv({cls:"column",parent:i});this.renderWeekIndicator(a,n);let l=this.generateContributionData(n);if(l.length>0){let c=new Date(l[0].date).getDay(),y=$l(n.startOfWeek||0,c);for(let v=0;v<y;v++)l.unshift({date:"$HOLE$",weekDay:-1,month:-1,monthDate:-1,year:-1,value:0})}let u=ci(l,m=>`${m.year}-${m.month+1}`,m=>m.value,(m,c)=>m+c),f=this.getCellRules(n),d;for(let m=0;m<l.length;m++){m%7==0&&(d=document.createElement("div"),d.className="column",i.appendChild(d));let c=l[m];if(c.monthDate==1){let v=createDiv({cls:"month-indicator",parent:d,text:""});v.innerText=fi(c.month),this.bindMonthTips(v,c,u)}let y=document.createElement("div");d==null||d.appendChild(y),c.value==0?c.date!="$HOLE$"?(y.className="cell empty",this.applyCellGlobalStyle(y,n),this.applyCellStyleRule(y,c,f),this.bindCellAttribute(y,c)):(y.className="cell",this.applyCellGlobalStylePartial(y,n,["minWidth","minHeight"])):(y.className="cell",this.applyCellGlobalStyle(y,n),this.applyCellStyleRule(y,c,f,()=>f[0]),this.bindCellAttribute(y,c),this.bindCellClickEvent(y,c,n,s),this.bindCellTips(y,c))}}renderWeekIndicator(e,n){let r=n.startOfWeek||0;for(let o=0;o<7;o++){let i=document.createElement("div");switch(i.className="cell week-indicator",this.applyCellGlobalStyle(i,n),o){case 1:case 3:case 5:i.innerText=Kl((o+r||0)%7);break;default:break}e.appendChild(i)}}};var tu=class{static render(e,n){n.graphType===void 0&&(n.graphType="default");let r=this.renders.find(o=>o.graphType()===n.graphType);r?r.render(e,n):this.renderErrorTips(e,`invalid graphType "${n.graphType}"`,[`please set graphType to one of ${tu.renders.map(o=>o.graphType()).join(", ")}`])}static renderErrorTips(e,n,r){e.empty();let o=createDiv({cls:"contribution-graph-render-error-container",parent:e});createEl("p",{text:n,cls:"summary",parent:o}),r&&r.forEach(i=>{createEl("pre",{text:i,cls:"recommend",parent:o})})}static renderError(e,{summary:n,recommends:r}){tu.renderErrorTips(e,n,r)}},mn=tu;mn.renders=[new Jl,new Xl,new eu];var sD=require("obsidian");function di(t,e){return{summary:t,recommends:e}}var nu=t=>di("Empty Graph, please add config, for example",[`title: 'Contributions'
days: 365
dataSource: '#tag' # means all notes with tag 'tag'
  type: "page" # or "task"
  value: '""' # means all notes in folder`]),xg=t=>di("please set dataSource or data property, for example",[`dataSource: '#tag' # means all notes with tag 'tag'
  type: "page" # or "task"
  value: '""' # means all notes
days: 365`,`dataSource: '#tag and "folder"' # means all notes with tag 'tag' and in folder 'folder', folder should surrounded by quotes
  type: "page" # or "task"
	value: '""' # means all notes
fromDate: '2023-01-01' 
toDate: '2023-12-31'  `]),Tg=t=>di(`graphType "${t}" is invalid, value must be one of [default, month-track, calendar], for example`,[`graphType: 'default'
days: 365
dataSource: '#tag' # means all notes with tag 'tag'
  type: "page" # or "task"
  value: '""' # means all notes in folde `]),Fg=t=>di("please set dateRangeValue or fromDate and toDate property, for example",[`dateRangeType: LATEST_DAYS
dateRangeValue: 365
dataSource: '#tag' # means all notes with tag 'tag'
  type: "page" # or "task"
  value: '""' # means all notes in folde `,`dateRangeType: FIXED_DATE_RANGE
fromDate: '2023-01-01'
toDate: '2023-12-31'
dataSource: '#tag' # means all notes with tag 'tag'
  type: "page" # or "task"
  value: '""' # means all notes in folde`]),fd=t=>di(`"${t}" is invalid, fromDate and toDate must be yyyy-MM-dd, for example`,[`fromDate: '2023-01-01'
toDate: '2023-12-31'
data: []`]),dd=t=>di(`startOfWeek value ${t} is invalid, should be 0~6, 0=Sunday, 1=Monday, 2=Thursday and etc. for example`,[`fromDate: '2023-01-01'
toDate: '2023-12-31'
data: []
startOfWeek: 1`]);var Ne=class{constructor({summary:e,recommends:n}){this.summary=e,this.recommends=n||[]}};var qd=require("obsidian"),iD=B(Gd());var Pn=class{constructor(e,n){this.date=n,this.raw=e}};function rD(t){return t==null||t==null?!1:typeof t=="object"&&"isLuxonDateTime"in t&&t.isLuxonDateTime===!0}var Yd=class{filter(e,n,r){if(!e.countField||e.countField.type=="DEFAULT")return!0;let o=MF(e.countField.type),i=r.getValueByCustomizeProperty(n,o,e.countField.value||"");return!(i==null||i==null)}},Zd=class{filter(e,n,r){var o;if(e.dateField&&e.dateField.value){let i=e.dateField.value,s=e.dateField.type;if(s=="FILE_CTIME"||s=="FILE_MTIME"||s=="FILE_NAME")return!0;let a=IF((o=e.dateField)==null?void 0:o.type);if(!r.getValueByCustomizeProperty(n,a,i))return!1}return!0}},oD=[new Yd,new Zd];function MF(t){switch(t){case"PAGE_PROPERTY":return"PAGE";case"TASK_PROPERTY":return"TASK";default:return"UNKNOWN"}}function IF(t){switch(t){case"FILE_CTIME":case"FILE_MTIME":case"FILE_NAME":case"PAGE_PROPERTY":return"PAGE";case"TASK_PROPERTY":return"TASK";default:return"UNKNOWN"}}var Ci=class{query(e,n){this.reconcileSourceValueIfNotExists(e);let r=this.checkAndGetApi(n),o=this.doQuery(r,e),i=this.mapToQueryData(o,e),s=i.filter(l=>!l.date);return s.length>0&&console.warn(s.length+" data can't be converted to date, please check the date field format",s),i.filter(l=>l.date!=null).groupBy(l=>{var u;return(u=l.date)==null?void 0:u.toFormat("yyyy-MM-dd")}).map(l=>{var d,m;let u=this.countSumValueByCustomizeProperty(l.rows,(d=e.countField)==null?void 0:d.type,(m=e.countField)==null?void 0:m.value),f=l.rows.map(c=>{var S,p,h,E;let y;e.type=="PAGE"?y=c.raw.file.name:y=c.raw.text;let v=this.getAndConvertValueByCustomizeProperty(c,(S=e.countField)==null?void 0:S.type,(p=e.countField)==null?void 0:p.value);return((h=e.countField)==null?void 0:h.type)=="PAGE_PROPERTY"&&(y+=` [${(E=e.countField)==null?void 0:E.value}:${v}]`),{label:y,value:v,link:{href:c.raw.file.path,className:"internal-link",rel:"noopener"},open:D=>AF(D,e,c,n)}}).array();return{date:l.key,value:u,items:f}}).array()}reconcileSourceValueIfNotExists(e){e.value||(e.value='""')}checkAndGetApi(e){let n=(0,iD.getAPI)(e);if(!n)throw new Ne({summary:"Initialize Dataview failed",recommends:["Please install Dataview plugin"]});return n}mapToQueryData(e,n){if(n.dateField&&n.dateField.type){let r=n.dateField.value,o=n.dateField.type,i=n.dateField.format;return e.filter(s=>oD.every(a=>a.filter(n,s,this))).map(s=>{var l;let a=s.file.name;if(o=="FILE_CTIME")return new Pn(s,s.file.ctime);if(o=="FILE_MTIME")return new Pn(s,s.file.mtime);if(o=="FILE_NAME"){let u=this.toDateTime(a,a,i);return u?new Pn(s,u):new Pn(s)}else{let u=this.getPropertySourceByDateFieldType((l=n.dateField)==null?void 0:l.type),f=this.getValueByCustomizeProperty(s,u,r||"");if(rD(f))return new Pn(s,f);{let d=this.toDateTime(a,f,i);return new Pn(s,d)}}})}else return e.map(r=>new Pn(r,r.file.ctime))}toDateTime(e,n,r){if(typeof n!="string"){console.warn("can't parse date, it's a valid format? "+n+" in page "+e);return}try{let o=null;if(r&&(o=$.fromFormat(n,r),o.isValid)||(o=$.fromISO(n),o.isValid)||(o=$.fromRFC2822(n),o.isValid)||(o=$.fromHTTP(n),o.isValid)||(o=$.fromSQL(n),o.isValid)||(o=$.fromFormat(n,"yyyy-MM-dd HH:mm"),o.isValid)||(o=$.fromFormat(n,"yyyy-MM-dd'T'HH:mm"),o.isValid))return o}catch(o){console.warn("can't parse date, it's a valid format? "+n+" in page "+e)}}countSumValueByCustomizeProperty(e,n,r){return!n||n=="DEFAULT"?e.length:r?e.map(o=>this.getAndConvertValueByCustomizeProperty(o,n,r)).array().reduce((o,i)=>o+i,0):e.length}getAndConvertValueByCustomizeProperty(e,n,r){if(r){let o;switch(n){case"PAGE_PROPERTY":o="PAGE";break;case"TASK_PROPERTY":o="TASK";break;default:o="UNKNOWN";break}let i=this.getValueByCustomizeProperty(e.raw,o,r);if(i==null||i==null)return 0;if(i instanceof Array)return i.length;if(typeof i=="number"||i instanceof Number)return i;if(typeof i=="string"||i instanceof String){let s=vg(i);return s!=null?s:i.trim()===""?0:1}return typeof i=="boolean"||i instanceof Boolean?i?1:0:1}else return 1}getPropertySourceByCountFieldType(e){switch(e){case"PAGE_PROPERTY":return"PAGE";case"TASK_PROPERTY":return"TASK";default:return"UNKNOWN"}}getPropertySourceByDateFieldType(e){switch(e){case"FILE_CTIME":case"FILE_MTIME":case"FILE_NAME":case"PAGE_PROPERTY":return"PAGE";case"TASK_PROPERTY":return"TASK";default:return"UNKNOWN"}}};function AF(t,e,n,r){var o;if(e.type!="PAGE"){let i={eState:{cursor:{from:{line:n.raw.line,ch:n.raw.position.start.col},to:{line:n.raw.line+n.raw.lineCount-1,ch:n.raw.position.end.col}},line:n.raw.line}};r.workspace.openLinkText(n.raw.link.toFile().obsidianLink(),n.raw.path,t.ctrlKey||t.metaKey&&qd.Platform.isMacOS,i)}else r.workspace.openLinkText((o=n.raw.file)==null?void 0:o.path,"",t.ctrlKey||t.metaKey&&qd.Platform.isMacOS)}var Cu=class extends Ci{accept(e){return e.type==="PAGE"}doQuery(e,n){return e.pages(n.value)}getValueByCustomizeProperty(e,n,r){if(n==="PAGE")return e[r]}};var xu=class extends Ci{accept(e){return e.type==="ALL_TASK"||e.type==="TASK_IN_SPECIFIC_PAGE"}doQuery(e,n){let r;n.type==="ALL_TASK"?r=e.pages('""'):r=e.pages(n.value);let o=r.filter(i=>i.file.tasks.length>0).flatMap(i=>i.file.tasks.map(a=>({...a,file:i.file})).array());return!n.filters||n.filters.length===0?o:o.filter(i=>n.filters?n.filters.every(s=>{switch(s.type){case"NONE":return!0;case"STATUS_IS":return this.filterByStatusIs(s,i);case"STATUS_IN":return this.filterByStatusIn(s,i);case"CONTAINS_ANY_TAG":return this.filterByContainsAnyTag(s,i);default:return!0}}):!0)}filterByStatusIn(e,n){return e.value.some(o=>o=="COMPLETED"?n.completed:o=="INCOMPLETE"?n.status==" ":o=="CANCELED"?n.status=="-":o=="ANY"?!0:o=="FULLY_COMPLETED"?n.fullyCompleted:n.status===o)}filterByStatusIs(e,n){return e.value=="COMPLETED"?n.completed:e.value=="INCOMPLETE"?n.status==" ":e.value=="CANCELED"?n.status=="-":e.value=="ANY"?!0:e.value=="FULLY_COMPLETED"?n.fullyCompleted:n.status===e.value}filterByContainsAnyTag(e,n){if((e==null?void 0:e.value)instanceof Array){let r=e==null?void 0:e.value;return r.length===0?!0:n.tags.some(o=>r.find(i=>i.toLowerCase()===o.toLowerCase())!==void 0)}else return!0}getValueByCustomizeProperty(e,n,r){if(n==="PAGE"&&e.file)return e.file[r];if(n==="TASK")return e[r]}};var Tu=class{constructor(){this.dataSourceQueries=[new Cu,new xu]}query(e,n){let r=this.dataSourceQueries.find(o=>o.accept(e));if(!r)throw new Ne({summary:"Unsupported data source",recommends:["Please use supported data source"]});return r.query(e,n)}};var So=class{constructor(){this.title="Contributions",this.graphType="default",this.dateRangeValue=180,this.dateRangeType="LATEST_DAYS",this.startOfWeek=Ks()?1:0,this.showCellRuleIndicators=!0,this.titleStyle={textAlign:"left",fontSize:"1.5em",fontWeight:"normal"},this.dataSource={type:"PAGE",value:"",dateField:{}},this.fillTheScreen=!1,this.enableMainContainerShadow=!1,this.query=void 0,this.dateFieldFormat=void 0,this.dateField=void 0,this.days=void 0}static toContributionGraphConfig(e){let{query:n,dateField:r,...o}=e;if(e.dateRangeType!="FIXED_DATE_RANGE"){if(e.dateRangeType=="LATEST_DAYS")return{days:e.dateRangeValue,...o};if(e.dateRangeType=="LATEST_MONTH"){let{start:i,end:s}=gg(e.dateRangeValue||0);return{...o,days:void 0,fromDate:qs(i),toDate:qs(s)}}if(e.dateRangeType=="LATEST_YEAR"){let{start:i,end:s}=yg(e.dateRangeValue||0);return{...o,days:void 0,fromDate:qs(i),toDate:qs(s)}}}return o}static validate(e){if(!e)throw new Ne(nu());if(!e.dataSource&&!e.data)throw new Ne(xg());if(e.graphType&&!["default","month-track","calendar"].includes(e.graphType))throw new Ne(Tg(e.graphType));if(!e.dateRangeValue&&(!e.fromDate||!e.toDate))throw new Ne(Fg());if(e.fromDate||e.toDate){let n=/^\d{4}-\d{2}-\d{2}$/;if(e.fromDate&&!n.test(e.fromDate))throw new Ne(fd(e.fromDate));if(e.toDate&&!n.test(e.toDate))throw new Ne(fd(e.toDate))}if(e.startOfWeek){let n=[0,1,2,3,4,5,6];if(typeof e.startOfWeek!="number")try{e.startOfWeek=parseInt(e.startOfWeek)}catch(r){throw new Ne(dd(e.startOfWeek))}if(!n.includes(e.startOfWeek))throw new Ne(dd(e.startOfWeek))}}};var Mr=class{constructor(){}static reconcile(e){return Mr.reconcile_from_0_4_0(e)}static reconcile_from_0_4_0(e){if(e.dataSource||(e.dataSource={type:"PAGE",value:e.query||'""',filters:[],dateField:{type:"PAGE_PROPERTY",value:e.dateField,format:e.dateFieldFormat},countField:{type:"DEFAULT"}}),!e.dateRangeType){let r=e.days!==void 0?"LATEST_DAYS":"FIXED_DATE_RANGE";e.dateRangeType=r}return e.dateRangeValue||(e.dateRangeValue=e.days),e.query=void 0,e.dateField=void 0,e.dateFieldFormat=void 0,e}};var aD=B(Gd()),xi=class{constructor(){this.dataSourceQuery=new Tu}async renderFromCodeBlock(e,n,r,o){try{let i=this.loadYamlConfig(n,e);await this.renderFromYaml(i,n,o)}catch(i){if(i instanceof Ne)mn.renderErrorTips(n,i.summary,i.recommends);else{console.error(i);let s="unexpected error: "+i.message;mn.renderErrorTips(n,s)}}}async renderFromYaml(e,n,r){let o=()=>{try{So.validate(e);let s=this.dataSourceQuery.query(e.dataSource,r),a=[];e.data&&a.push(...e.data),a.push(...s),e.data=a,mn.render(n,So.toContributionGraphConfig(e))}catch(s){if(s instanceof Ne)mn.renderErrorTips(n,s.summary,s.recommends);else{console.error(s);let a="unexpected error: "+s.message;mn.renderErrorTips(n,a)}}},i=(0,aD.getAPI)(r);if(!i)throw new Ne({summary:"Initialize Dataview failed",recommends:["Please install Dataview plugin"]});i.index.initialized?o():r.metadataCache.on("dataview:index-ready",()=>{o()})}loadYamlConfig(e,n){var r;if(n==null||n.trim()=="")throw new Ne(nu());try{let o=(0,sD.parseYaml)(n);return Mr.reconcile(o)}catch(o){throw(r=o.mark)!=null&&r.line?new Ne({summary:"yaml parse error at line "+(o.mark.line+1)+", please check the format"}):new Ne({summary:"content parse error, please check the format(such as blank, indent)"})}}};var sn=require("obsidian"),LS=B(se()),PS=B(Hw());var El=B(se());var hh=B(se()),Kc=B(X());function $w(t){let{onChoose:e}=t,[n,r]=(0,hh.useState)(t.options),[o,i]=(0,hh.useState)(t.defaultValue),s=a=>{i(a.value),e(a)};return(0,Kc.jsx)("div",{className:"contribution-graph-choose",children:n.map(a=>(0,Kc.jsx)("div",{className:a.value==o?"item choosed":"item","ariea-label":a.tip,onClick:l=>s(a),children:(0,Kc.jsx)("div",{className:"icon",children:a.icon})},a.value))})}var re=B(se(),1),ll=B(se(),1);function jn(t){return Gw(t)?(t.nodeName||"").toLowerCase():"#document"}function Dt(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function $n(t){var e;return(e=(Gw(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function Gw(t){return t instanceof Node||t instanceof Dt(t).Node}function Pe(t){return t instanceof Element||t instanceof Dt(t).Element}function kt(t){return t instanceof HTMLElement||t instanceof Dt(t).HTMLElement}function Qc(t){return typeof ShadowRoot=="undefined"?!1:t instanceof ShadowRoot||t instanceof Dt(t).ShadowRoot}function ts(t){let{overflow:e,overflowX:n,overflowY:r,display:o}=vt(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(o)}function Yw(t){return["table","td","th"].includes(jn(t))}function Jc(t){let e=Xc(),n=vt(t);return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!e&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!e&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Zw(t){let e=mr(t);for(;kt(e)&&!to(e);){if(Jc(e))return e;e=mr(e)}return null}function Xc(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function to(t){return["html","body","#document"].includes(jn(t))}function vt(t){return Dt(t).getComputedStyle(t)}function nl(t){return Pe(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function mr(t){if(jn(t)==="html")return t;let e=t.assignedSlot||t.parentNode||Qc(t)&&t.host||$n(t);return Qc(e)?e.host:e}function qw(t){let e=mr(t);return to(e)?t.ownerDocument?t.ownerDocument.body:t.body:kt(e)&&ts(e)?e:qw(e)}function Ht(t,e,n){var r;e===void 0&&(e=[]),n===void 0&&(n=!0);let o=qw(t),i=o===((r=t.ownerDocument)==null?void 0:r.body),s=Dt(o);return i?e.concat(s,s.visualViewport||[],ts(o)?o:[],s.frameElement&&n?Ht(s.frameElement):[]):e.concat(o,Ht(o,[],n))}function yh(t,e){if(!t||!e)return!1;let n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&Qc(n)){let r=e;for(;r;){if(t===r)return!0;r=r.parentNode||r.host}}return!1}function Kw(t){return"nativeEvent"in t}function Qw(t){return t.matches("html,body")}function gh(t){return(t==null?void 0:t.ownerDocument)||document}function ef(t,e){if(e==null)return!1;if("composedPath"in t)return t.composedPath().includes(e);let n=t;return n.target!=null&&e.contains(n.target)}function ns(t){return"composedPath"in t?t.composedPath()[0]:t.target}var Jw=["top","right","bottom","left"];var pr=Math.min,nn=Math.max,ol=Math.round,il=Math.floor,hr=t=>({x:t,y:t}),o2={left:"right",right:"left",bottom:"top",top:"bottom"},i2={start:"end",end:"start"};function Dh(t,e,n){return nn(t,pr(e,n))}function Bo(t,e){return typeof t=="function"?t(e):t}function Gn(t){return t.split("-")[0]}function sl(t){return t.split("-")[1]}function vh(t){return t==="x"?"y":"x"}function Eh(t){return t==="y"?"height":"width"}function rs(t){return["top","bottom"].includes(Gn(t))?"y":"x"}function wh(t){return vh(rs(t))}function Xw(t,e,n){n===void 0&&(n=!1);let r=sl(t),o=wh(t),i=Eh(o),s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return e.reference[i]>e.floating[i]&&(s=rl(s)),[s,rl(s)]}function e1(t){let e=rl(t);return[tf(t),e,tf(e)]}function tf(t){return t.replace(/start|end/g,e=>i2[e])}function s2(t,e,n){let r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(t){case"top":case"bottom":return n?e?o:r:e?r:o;case"left":case"right":return e?i:s;default:return[]}}function t1(t,e,n,r){let o=sl(t),i=s2(Gn(t),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),e&&(i=i.concat(i.map(tf)))),i}function rl(t){return t.replace(/left|right|bottom|top/g,e=>o2[e])}function a2(t){return{top:0,right:0,bottom:0,left:0,...t}}function Sh(t){return typeof t!="number"?a2(t):{top:t,right:t,bottom:t,left:t}}function yr(t){return{...t,top:t.y,left:t.x,right:t.x+t.width,bottom:t.y+t.height}}function n1(t,e,n){let{reference:r,floating:o}=t,i=rs(e),s=wh(e),a=Eh(s),l=Gn(e),u=i==="y",f=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,m=r[a]/2-o[a]/2,c;switch(l){case"top":c={x:f,y:r.y-o.height};break;case"bottom":c={x:f,y:r.y+r.height};break;case"right":c={x:r.x+r.width,y:d};break;case"left":c={x:r.x-o.width,y:d};break;default:c={x:r.x,y:r.y}}switch(sl(e)){case"start":c[s]-=m*(n&&u?-1:1);break;case"end":c[s]+=m*(n&&u?-1:1);break}return c}var i1=async(t,e,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(e)),u=await s.getElementRects({reference:t,floating:e,strategy:o}),{x:f,y:d}=n1(u,r,l),m=r,c={},y=0;for(let v=0;v<a.length;v++){let{name:S,fn:p}=a[v],{x:h,y:E,data:D,reset:x}=await p({x:f,y:d,initialPlacement:r,placement:m,strategy:o,middlewareData:c,rects:u,platform:s,elements:{reference:t,floating:e}});if(f=h!=null?h:f,d=E!=null?E:d,c={...c,[S]:{...c[S],...D}},x&&y<=50){y++,typeof x=="object"&&(x.placement&&(m=x.placement),x.rects&&(u=x.rects===!0?await s.getElementRects({reference:t,floating:e,strategy:o}):x.rects),{x:f,y:d}=n1(u,m,l)),v=-1;continue}}return{x:f,y:d,placement:m,strategy:o,middlewareData:c}};async function no(t,e){var n;e===void 0&&(e={});let{x:r,y:o,platform:i,rects:s,elements:a,strategy:l}=t,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:m=!1,padding:c=0}=Bo(e,t),y=Sh(c),S=a[m?d==="floating"?"reference":"floating":d],p=yr(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(S)))==null||n?S:S.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:f,strategy:l})),h=d==="floating"?{...s.floating,x:r,y:o}:s.reference,E=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),D=await(i.isElement==null?void 0:i.isElement(E))?await(i.getScale==null?void 0:i.getScale(E))||{x:1,y:1}:{x:1,y:1},x=yr(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({rect:h,offsetParent:E,strategy:l}):h);return{top:(p.top-x.top+y.top)/D.y,bottom:(x.bottom-p.bottom+y.bottom)/D.y,left:(p.left-x.left+y.left)/D.x,right:(x.right-p.right+y.right)/D.x}}var gr=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var n,r;let{placement:o,middlewareData:i,rects:s,initialPlacement:a,platform:l,elements:u}=e,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:m,fallbackStrategy:c="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:v=!0,...S}=Bo(t,e);if((n=i.arrow)!=null&&n.alignmentOffset)return{};let p=Gn(o),h=Gn(a)===a,E=await(l.isRTL==null?void 0:l.isRTL(u.floating)),D=m||(h||!v?[rl(a)]:e1(a));!m&&y!=="none"&&D.push(...t1(a,v,y,E));let x=[a,...D],O=await no(e,S),F=[],k=((r=i.flip)==null?void 0:r.overflows)||[];if(f&&F.push(O[p]),d){let oe=Xw(o,s,E);F.push(O[oe[0]],O[oe[1]])}if(k=[...k,{placement:o,overflows:F}],!F.every(oe=>oe<=0)){var P,M;let oe=(((P=i.flip)==null?void 0:P.index)||0)+1,b=x[oe];if(b)return{data:{index:oe,overflows:k},reset:{placement:b}};let _=(M=k.filter(R=>R.overflows[0]<=0).sort((R,W)=>R.overflows[1]-W.overflows[1])[0])==null?void 0:M.placement;if(!_)switch(c){case"bestFit":{var ee;let R=(ee=k.map(W=>[W.placement,W.overflows.filter(Q=>Q>0).reduce((Q,K)=>Q+K,0)]).sort((W,Q)=>W[1]-Q[1])[0])==null?void 0:ee[0];R&&(_=R);break}case"initialPlacement":_=a;break}if(o!==_)return{reset:{placement:_}}}return{}}}};function r1(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function o1(t){return Jw.some(e=>t[e]>=0)}var al=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:r="referenceHidden",...o}=Bo(t,e);switch(r){case"referenceHidden":{let i=await no(e,{...o,elementContext:"reference"}),s=r1(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:o1(s)}}}case"escaped":{let i=await no(e,{...o,altBoundary:!0}),s=r1(i,n.floating);return{data:{escapedOffsets:s,escaped:o1(s)}}}default:return{}}}}};function s1(t){let e=pr(...t.map(i=>i.left)),n=pr(...t.map(i=>i.top)),r=nn(...t.map(i=>i.right)),o=nn(...t.map(i=>i.bottom));return{x:e,y:n,width:r-e,height:o-n}}function l2(t){let e=t.slice().sort((o,i)=>o.y-i.y),n=[],r=null;for(let o=0;o<e.length;o++){let i=e[o];!r||i.y-r.y>r.height/2?n.push([i]):n[n.length-1].push(i),r=i}return n.map(o=>yr(s1(o)))}var Dr=function(t){return t===void 0&&(t={}),{name:"inline",options:t,async fn(e){let{placement:n,elements:r,rects:o,platform:i,strategy:s}=e,{padding:a=2,x:l,y:u}=Bo(t,e),f=Array.from(await(i.getClientRects==null?void 0:i.getClientRects(r.reference))||[]),d=l2(f),m=yr(s1(f)),c=Sh(a);function y(){if(d.length===2&&d[0].left>d[1].right&&l!=null&&u!=null)return d.find(S=>l>S.left-c.left&&l<S.right+c.right&&u>S.top-c.top&&u<S.bottom+c.bottom)||m;if(d.length>=2){if(rs(n)==="y"){let M=d[0],ee=d[d.length-1],oe=Gn(n)==="top",b=M.top,_=ee.bottom,R=oe?M.left:ee.left,W=oe?M.right:ee.right,Q=W-R,K=_-b;return{top:b,bottom:_,left:R,right:W,width:Q,height:K,x:R,y:b}}let S=Gn(n)==="left",p=nn(...d.map(M=>M.right)),h=pr(...d.map(M=>M.left)),E=d.filter(M=>S?M.left===h:M.right===p),D=E[0].top,x=E[E.length-1].bottom,O=h,F=p,k=F-O,P=x-D;return{top:D,bottom:x,left:O,right:F,width:k,height:P,x:O,y:D}}return m}let v=await i.getElementRects({reference:{getBoundingClientRect:y},floating:r.floating,strategy:s});return o.reference.x!==v.reference.x||o.reference.y!==v.reference.y||o.reference.width!==v.reference.width||o.reference.height!==v.reference.height?{reset:{rects:v}}:{}}}};async function u2(t,e){let{placement:n,platform:r,elements:o}=t,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=Gn(n),a=sl(n),l=rs(n)==="y",u=["left","top"].includes(s)?-1:1,f=i&&l?-1:1,d=Bo(e,t),{mainAxis:m,crossAxis:c,alignmentAxis:y}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...d};return a&&typeof y=="number"&&(c=a==="end"?y*-1:y),l?{x:c*f,y:m*u}:{x:m*u,y:c*f}}var Yn=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var n,r;let{x:o,y:i,placement:s,middlewareData:a}=e,l=await u2(e,t);return s===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:s}}}}},vr=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:S=>{let{x:p,y:h}=S;return{x:p,y:h}}},...l}=Bo(t,e),u={x:n,y:r},f=await no(e,l),d=rs(Gn(o)),m=vh(d),c=u[m],y=u[d];if(i){let S=m==="y"?"top":"left",p=m==="y"?"bottom":"right",h=c+f[S],E=c-f[p];c=Dh(h,c,E)}if(s){let S=d==="y"?"top":"left",p=d==="y"?"bottom":"right",h=y+f[S],E=y-f[p];y=Dh(h,y,E)}let v=a.fn({...e,[m]:c,[d]:y});return{...v,data:{x:v.x-n,y:v.y-r}}}}};function d1(t){let e=vt(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,o=kt(t),i=o?t.offsetWidth:n,s=o?t.offsetHeight:r,a=ol(n)!==i||ol(r)!==s;return a&&(n=i,r=s),{width:n,height:r,$:a}}function Ch(t){return Pe(t)?t:t.contextElement}function os(t){let e=Ch(t);if(!kt(e))return hr(1);let n=e.getBoundingClientRect(),{width:r,height:o,$:i}=d1(e),s=(i?ol(n.width):n.width)/r,a=(i?ol(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}var f2=hr(0);function m1(t){let e=Dt(t);return!Xc()||!e.visualViewport?f2:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function d2(t,e,n){return e===void 0&&(e=!1),!n||e&&n!==Dt(t)?!1:e}function Vo(t,e,n,r){e===void 0&&(e=!1),n===void 0&&(n=!1);let o=t.getBoundingClientRect(),i=Ch(t),s=hr(1);e&&(r?Pe(r)&&(s=os(r)):s=os(t));let a=d2(i,n,r)?m1(i):hr(0),l=(o.left+a.x)/s.x,u=(o.top+a.y)/s.y,f=o.width/s.x,d=o.height/s.y;if(i){let m=Dt(i),c=r&&Pe(r)?Dt(r):r,y=m.frameElement;for(;y&&r&&c!==m;){let v=os(y),S=y.getBoundingClientRect(),p=vt(y),h=S.left+(y.clientLeft+parseFloat(p.paddingLeft))*v.x,E=S.top+(y.clientTop+parseFloat(p.paddingTop))*v.y;l*=v.x,u*=v.y,f*=v.x,d*=v.y,l+=h,u+=E,y=Dt(y).frameElement}}return yr({width:f,height:d,x:l,y:u})}function m2(t){let{rect:e,offsetParent:n,strategy:r}=t,o=kt(n),i=$n(n);if(n===i)return e;let s={scrollLeft:0,scrollTop:0},a=hr(1),l=hr(0);if((o||!o&&r!=="fixed")&&((jn(n)!=="body"||ts(i))&&(s=nl(n)),kt(n))){let u=Vo(n);a=os(n),l.x=u.x+n.clientLeft,l.y=u.y+n.clientTop}return{width:e.width*a.x,height:e.height*a.y,x:e.x*a.x-s.scrollLeft*a.x+l.x,y:e.y*a.y-s.scrollTop*a.y+l.y}}function p2(t){return Array.from(t.getClientRects())}function p1(t){return Vo($n(t)).left+nl(t).scrollLeft}function h2(t){let e=$n(t),n=nl(t),r=t.ownerDocument.body,o=nn(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),i=nn(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),s=-n.scrollLeft+p1(t),a=-n.scrollTop;return vt(r).direction==="rtl"&&(s+=nn(e.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:a}}function y2(t,e){let n=Dt(t),r=$n(t),o=n.visualViewport,i=r.clientWidth,s=r.clientHeight,a=0,l=0;if(o){i=o.width,s=o.height;let u=Xc();(!u||u&&e==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:i,height:s,x:a,y:l}}function g2(t,e){let n=Vo(t,!0,e==="fixed"),r=n.top+t.clientTop,o=n.left+t.clientLeft,i=kt(t)?os(t):hr(1),s=t.clientWidth*i.x,a=t.clientHeight*i.y,l=o*i.x,u=r*i.y;return{width:s,height:a,x:l,y:u}}function a1(t,e,n){let r;if(e==="viewport")r=y2(t,n);else if(e==="document")r=h2($n(t));else if(Pe(e))r=g2(e,n);else{let o=m1(t);r={...e,x:e.x-o.x,y:e.y-o.y}}return yr(r)}function h1(t,e){let n=mr(t);return n===e||!Pe(n)||to(n)?!1:vt(n).position==="fixed"||h1(n,e)}function D2(t,e){let n=e.get(t);if(n)return n;let r=Ht(t,[],!1).filter(a=>Pe(a)&&jn(a)!=="body"),o=null,i=vt(t).position==="fixed",s=i?mr(t):t;for(;Pe(s)&&!to(s);){let a=vt(s),l=Jc(s);!l&&a.position==="fixed"&&(o=null),(i?!l&&!o:!l&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||ts(s)&&!l&&h1(t,s))?r=r.filter(f=>f!==s):o=a,s=mr(s)}return e.set(t,r),r}function v2(t){let{element:e,boundary:n,rootBoundary:r,strategy:o}=t,s=[...n==="clippingAncestors"?D2(e,this._c):[].concat(n),r],a=s[0],l=s.reduce((u,f)=>{let d=a1(e,f,o);return u.top=nn(d.top,u.top),u.right=pr(d.right,u.right),u.bottom=pr(d.bottom,u.bottom),u.left=nn(d.left,u.left),u},a1(e,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function E2(t){return d1(t)}function w2(t,e,n){let r=kt(e),o=$n(e),i=n==="fixed",s=Vo(t,!0,i,e),a={scrollLeft:0,scrollTop:0},l=hr(0);if(r||!r&&!i)if((jn(e)!=="body"||ts(o))&&(a=nl(e)),r){let u=Vo(e,!0,i,e);l.x=u.x+e.clientLeft,l.y=u.y+e.clientTop}else o&&(l.x=p1(o));return{x:s.left+a.scrollLeft-l.x,y:s.top+a.scrollTop-l.y,width:s.width,height:s.height}}function l1(t,e){return!kt(t)||vt(t).position==="fixed"?null:e?e(t):t.offsetParent}function y1(t,e){let n=Dt(t);if(!kt(t))return n;let r=l1(t,e);for(;r&&Yw(r)&&vt(r).position==="static";)r=l1(r,e);return r&&(jn(r)==="html"||jn(r)==="body"&&vt(r).position==="static"&&!Jc(r))?n:r||Zw(t)||n}var S2=async function(t){let{reference:e,floating:n,strategy:r}=t,o=this.getOffsetParent||y1,i=this.getDimensions;return{reference:w2(e,await o(n),r),floating:{x:0,y:0,...await i(n)}}};function C2(t){return vt(t).direction==="rtl"}var nf={convertOffsetParentRelativeRectToViewportRelativeRect:m2,getDocumentElement:$n,getClippingRect:v2,getOffsetParent:y1,getElementRects:S2,getClientRects:p2,getDimensions:E2,getScale:os,isElement:Pe,isRTL:C2};function x2(t,e){let n=null,r,o=$n(t);function i(){clearTimeout(r),n&&n.disconnect(),n=null}function s(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),i();let{left:u,top:f,width:d,height:m}=t.getBoundingClientRect();if(a||e(),!d||!m)return;let c=il(f),y=il(o.clientWidth-(u+d)),v=il(o.clientHeight-(f+m)),S=il(u),h={rootMargin:-c+"px "+-y+"px "+-v+"px "+-S+"px",threshold:nn(0,pr(1,l))||1},E=!0;function D(x){let O=x[0].intersectionRatio;if(O!==l){if(!E)return s();O?s(!1,O):r=setTimeout(()=>{s(!1,1e-7)},100)}E=!1}try{n=new IntersectionObserver(D,{...h,root:o.ownerDocument})}catch(x){n=new IntersectionObserver(D,h)}n.observe(t)}return s(!0),i}function ro(t,e,n,r){r===void 0&&(r={});let{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=Ch(t),f=o||i?[...u?Ht(u):[],...Ht(e)]:[];f.forEach(p=>{o&&p.addEventListener("scroll",n,{passive:!0}),i&&p.addEventListener("resize",n)});let d=u&&a?x2(u,n):null,m=-1,c=null;s&&(c=new ResizeObserver(p=>{let[h]=p;h&&h.target===u&&c&&(c.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{c&&c.observe(e)})),n()}),u&&!l&&c.observe(u),c.observe(e));let y,v=l?Vo(t):null;l&&S();function S(){let p=Vo(t);v&&(p.x!==v.x||p.y!==v.y||p.width!==v.width||p.height!==v.height)&&n(),v=p,y=requestAnimationFrame(S)}return n(),()=>{f.forEach(p=>{o&&p.removeEventListener("scroll",n),i&&p.removeEventListener("resize",n)}),d&&d(),c&&c.disconnect(),c=null,l&&cancelAnimationFrame(y)}}var rf=(t,e,n)=>{let r=new Map,o={platform:nf,...n},i={...o.platform,_c:r};return i1(t,e,{...o,platform:i})};var ze=B(se(),1),af=B(se(),1),v1=B(Zc(),1);var of=typeof document!="undefined"?af.useLayoutEffect:af.useEffect;function sf(t,e){if(t===e)return!0;if(typeof t!=typeof e)return!1;if(typeof t=="function"&&t.toString()===e.toString())return!0;let n,r,o;if(t&&e&&typeof t=="object"){if(Array.isArray(t)){if(n=t.length,n!=e.length)return!1;for(r=n;r--!==0;)if(!sf(t[r],e[r]))return!1;return!0}if(o=Object.keys(t),n=o.length,n!==Object.keys(e).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(e,o[r]))return!1;for(r=n;r--!==0;){let i=o[r];if(!(i==="_owner"&&t.$$typeof)&&!sf(t[i],e[i]))return!1}return!0}return t!==t&&e!==e}function E1(t){return typeof window=="undefined"?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function g1(t,e){let n=E1(t);return Math.round(e*n)/n}function D1(t){let e=ze.useRef(t);return of(()=>{e.current=t}),e}function w1(t){t===void 0&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:a=!0,whileElementsMounted:l,open:u}=t,[f,d]=ze.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[m,c]=ze.useState(r);sf(m,r)||c(r);let[y,v]=ze.useState(null),[S,p]=ze.useState(null),h=ze.useCallback(W=>{W!=O.current&&(O.current=W,v(W))},[v]),E=ze.useCallback(W=>{W!==F.current&&(F.current=W,p(W))},[p]),D=i||y,x=s||S,O=ze.useRef(null),F=ze.useRef(null),k=ze.useRef(f),P=D1(l),M=D1(o),ee=ze.useCallback(()=>{if(!O.current||!F.current)return;let W={placement:e,strategy:n,middleware:m};M.current&&(W.platform=M.current),rf(O.current,F.current,W).then(Q=>{let K={...Q,isPositioned:!0};oe.current&&!sf(k.current,K)&&(k.current=K,v1.flushSync(()=>{d(K)}))})},[m,e,n,M]);of(()=>{u===!1&&k.current.isPositioned&&(k.current.isPositioned=!1,d(W=>({...W,isPositioned:!1})))},[u]);let oe=ze.useRef(!1);of(()=>(oe.current=!0,()=>{oe.current=!1}),[]),of(()=>{if(D&&(O.current=D),x&&(F.current=x),D&&x){if(P.current)return P.current(D,x,ee);ee()}},[D,x,ee,P]);let b=ze.useMemo(()=>({reference:O,floating:F,setReference:h,setFloating:E}),[h,E]),_=ze.useMemo(()=>({reference:D,floating:x}),[D,x]),R=ze.useMemo(()=>{let W={position:n,left:0,top:0};if(!_.floating)return W;let Q=g1(_.floating,f.x),K=g1(_.floating,f.y);return a?{...W,transform:"translate("+Q+"px, "+K+"px)",...E1(_.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:Q,top:K}},[n,a,_.floating,f.x,f.y]);return ze.useMemo(()=>({...f,update:ee,refs:b,elements:_,floatingStyles:R}),[f,ee,b,_,R])}var F1=B(Zc(),1);var T2=re["useInsertionEffect".toString()],F2=T2||(t=>t());function is(t){let e=re.useRef(()=>{});return F2(()=>{e.current=t}),re.useCallback(function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return e.current==null?void 0:e.current(...r)},[])}var _2="ArrowUp",k2="ArrowDown",O2="ArrowLeft",b2="ArrowRight";var _1=typeof document!="undefined"?ll.useLayoutEffect:ll.useEffect;var R2=[O2,b2],N2=[_2,k2],LM=[...R2,...N2];var xh=!1,M2=0,S1=()=>"floating-ui-"+M2++;function I2(){let[t,e]=re.useState(()=>xh?S1():void 0);return _1(()=>{t==null&&e(S1())},[]),re.useEffect(()=>{xh||(xh=!0)},[]),t}var A2=re["useId".toString()],k1=A2||I2;function L2(){let t=new Map;return{emit(e,n){var r;(r=t.get(e))==null||r.forEach(o=>o(n))},on(e,n){t.set(e,[...t.get(e)||[],n])},off(e,n){var r;t.set(e,((r=t.get(e))==null?void 0:r.filter(o=>o!==n))||[])}}}var P2=re.createContext(null),B2=re.createContext(null),O1=()=>{var t;return((t=re.useContext(P2))==null?void 0:t.id)||null},b1=()=>re.useContext(B2);function V2(t){return"data-floating-ui-"+t}function Th(t,e){let n=t.filter(o=>{var i;return o.parentId===e&&((i=o.context)==null?void 0:i.open)}),r=n;for(;r.length;)r=t.filter(o=>{var i;return(i=r)==null?void 0:i.some(s=>{var a;return o.parentId===s.id&&((a=o.context)==null?void 0:a.open)})}),n=n.concat(r);return n}var W2={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},H2={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},C1=t=>{var e,n;return{escapeKey:typeof t=="boolean"?t:(e=t==null?void 0:t.escapeKey)!=null?e:!1,outsidePress:typeof t=="boolean"?t:(n=t==null?void 0:t.outsidePress)!=null?n:!0}};function ss(t,e){e===void 0&&(e={});let{open:n,onOpenChange:r,nodeId:o,elements:{reference:i,domReference:s,floating:a},dataRef:l}=t,{enabled:u=!0,escapeKey:f=!0,outsidePress:d=!0,outsidePressEvent:m="pointerdown",referencePress:c=!1,referencePressEvent:y="pointerdown",ancestorScroll:v=!1,bubbles:S,capture:p}=e,h=b1(),E=is(typeof d=="function"?d:()=>!1),D=typeof d=="function"?E:d,x=re.useRef(!1),O=re.useRef(!1),{escapeKey:F,outsidePress:k}=C1(S),{escapeKey:P,outsidePress:M}=C1(p),ee=is(R=>{if(!n||!u||!f||R.key!=="Escape")return;let W=h?Th(h.nodesRef.current,o):[];if(!F&&(R.stopPropagation(),W.length>0)){let Q=!0;if(W.forEach(K=>{var Ue;if((Ue=K.context)!=null&&Ue.open&&!K.context.dataRef.current.__escapeKeyBubbles){Q=!1;return}}),!Q)return}r(!1,Kw(R)?R.nativeEvent:R,"escape-key")}),oe=is(R=>{var W;let Q=()=>{var K;ee(R),(K=ns(R))==null||K.removeEventListener("keydown",Q)};(W=ns(R))==null||W.addEventListener("keydown",Q)}),b=is(R=>{let W=x.current;x.current=!1;let Q=O.current;if(O.current=!1,m==="click"&&Q||W||typeof D=="function"&&!D(R))return;let K=ns(R),Ue="["+V2("inert")+"]",Sr=gh(a).querySelectorAll(Ue),bn=Pe(K)?K:null;for(;bn&&!to(bn);){let Te=mr(bn);if(to(Te)||!Pe(Te))break;bn=Te}if(Sr.length&&Pe(K)&&!Qw(K)&&!yh(K,a)&&Array.from(Sr).every(Te=>!yh(bn,Te)))return;if(kt(K)&&a){let Te=K.clientWidth>0&&K.scrollWidth>K.clientWidth,an=K.clientHeight>0&&K.scrollHeight>K.clientHeight,so=an&&R.offsetX>K.clientWidth;if(an&&vt(K).direction==="rtl"&&(so=R.offsetX<=K.offsetWidth-K.clientWidth),so||Te&&R.offsetY>K.clientHeight)return}let io=h&&Th(h.nodesRef.current,o).some(Te=>{var an;return ef(R,(an=Te.context)==null?void 0:an.elements.floating)});if(ef(R,a)||ef(R,s)||io)return;let tt=h?Th(h.nodesRef.current,o):[];if(tt.length>0){let Te=!0;if(tt.forEach(an=>{var so;if((so=an.context)!=null&&so.open&&!an.context.dataRef.current.__outsidePressBubbles){Te=!1;return}}),!Te)return}r(!1,R,"outside-press")}),_=is(R=>{var W;let Q=()=>{var K;b(R),(K=ns(R))==null||K.removeEventListener(m,Q)};(W=ns(R))==null||W.addEventListener(m,Q)});return re.useEffect(()=>{if(!n||!u)return;l.current.__escapeKeyBubbles=F,l.current.__outsidePressBubbles=k;function R(K){r(!1,K,"ancestor-scroll")}let W=gh(a);f&&W.addEventListener("keydown",P?oe:ee,P),D&&W.addEventListener(m,M?_:b,M);let Q=[];return v&&(Pe(s)&&(Q=Ht(s)),Pe(a)&&(Q=Q.concat(Ht(a))),!Pe(i)&&i&&i.contextElement&&(Q=Q.concat(Ht(i.contextElement)))),Q=Q.filter(K=>{var Ue;return K!==((Ue=W.defaultView)==null?void 0:Ue.visualViewport)}),Q.forEach(K=>{K.addEventListener("scroll",R,{passive:!0})}),()=>{f&&W.removeEventListener("keydown",P?oe:ee,P),D&&W.removeEventListener(m,M?_:b,M),Q.forEach(K=>{K.removeEventListener("scroll",R)})}},[l,a,s,i,f,D,m,n,r,v,u,F,k,ee,P,oe,b,M,_]),re.useEffect(()=>{x.current=!1},[D,m]),re.useMemo(()=>u?{reference:{onKeyDown:ee,[W2[y]]:R=>{c&&r(!1,R.nativeEvent,"reference-press")}},floating:{onKeyDown:ee,onMouseDown(){O.current=!0},onMouseUp(){O.current=!0},[H2[m]]:()=>{x.current=!0}}}:{},[u,c,m,y,r,ee])}function as(t){var e;t===void 0&&(t={});let{open:n=!1,onOpenChange:r,nodeId:o}=t;if(!1){var i;if((i=t.elements)!=null&&i.reference&&!Pe(t.elements.reference)){var s;if(!((s=devMessageSet)!=null&&s.has(k)))var a}}let[l,u]=re.useState(null),f=((e=t.elements)==null?void 0:e.reference)||l,d=w1(t),m=b1(),c=O1()!=null,y=is((k,P,M)=>{k&&(S.current.openEvent=P),p.emit("openchange",{open:k,event:P,reason:M,nested:c}),r==null||r(k,P,M)}),v=re.useRef(null),S=re.useRef({}),p=re.useState(()=>L2())[0],h=k1(),E=re.useCallback(k=>{let P=Pe(k)?{getBoundingClientRect:()=>k.getBoundingClientRect(),contextElement:k}:k;d.refs.setReference(P)},[d.refs]),D=re.useCallback(k=>{(Pe(k)||k===null)&&(v.current=k,u(k)),(Pe(d.refs.reference.current)||d.refs.reference.current===null||k!==null&&!Pe(k))&&d.refs.setReference(k)},[d.refs]),x=re.useMemo(()=>({...d.refs,setReference:D,setPositionReference:E,domReference:v}),[d.refs,D,E]),O=re.useMemo(()=>({...d.elements,domReference:f}),[d.elements,f]),F=re.useMemo(()=>({...d,refs:x,elements:O,dataRef:S,nodeId:o,floatingId:h,events:p,open:n,onOpenChange:y}),[d,o,h,p,n,y,x,O]);return _1(()=>{let k=m==null?void 0:m.nodesRef.current.find(P=>P.id===o);k&&(k.context=F)}),re.useMemo(()=>({...d,context:F,refs:x,elements:O}),[d,x,O,F])}var x1="active",T1="selected";function Fh(t,e,n){let r=new Map,o=n==="item",i=t;if(o&&t){let{[x1]:s,[T1]:a,...l}=t;i=l}return{...n==="floating"&&{tabIndex:-1},...i,...e.map(s=>{let a=s?s[n]:null;return typeof a=="function"?t?a(t):null:a}).concat(t).reduce((s,a)=>(a&&Object.entries(a).forEach(l=>{let[u,f]=l;if(!(o&&[x1,T1].includes(u)))if(u.indexOf("on")===0){if(r.has(u)||r.set(u,[]),typeof f=="function"){var d;(d=r.get(u))==null||d.push(f),s[u]=function(){for(var m,c=arguments.length,y=new Array(c),v=0;v<c;v++)y[v]=arguments[v];return(m=r.get(u))==null?void 0:m.map(S=>S(...y)).find(S=>S!==void 0)}}}else s[u]=f}),s),{})}}function ls(t){t===void 0&&(t=[]);let e=t,n=re.useCallback(i=>Fh(i,t,"reference"),e),r=re.useCallback(i=>Fh(i,t,"floating"),e),o=re.useCallback(i=>Fh(i,t,"item"),t.map(i=>i==null?void 0:i.item));return re.useMemo(()=>({getReferenceProps:n,getFloatingProps:r,getItemProps:o}),[n,r,o])}var z2=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]);function R1(t,e){var n;e===void 0&&(e={});let{open:r,floatingId:o}=t,{enabled:i=!0,role:s="dialog"}=e,a=(n=z2.get(s))!=null?n:s,l=k1(),f=O1()!=null;return re.useMemo(()=>{if(!i)return{};let d={id:o,...a&&{role:a}};return a==="tooltip"||s==="label"?{reference:{["aria-"+(s==="label"?"labelledby":"describedby")]:r?o:void 0},floating:d}:{reference:{"aria-expanded":r?"true":"false","aria-haspopup":a==="alertdialog"?"dialog":a,"aria-controls":r?o:void 0,...a==="listbox"&&{role:"combobox"},...a==="menu"&&{id:l},...a==="menu"&&f&&{role:"menuitem"},...s==="select"&&{"aria-autocomplete":"none"},...s==="combobox"&&{"aria-autocomplete":"list"}},floating:{...d,...a==="menu"&&{"aria-labelledby":l}},item(m){let{active:c,selected:y}=m,v={role:"option",...c&&{id:o+"-option"}};switch(s){case"select":return{...v,"aria-selected":c&&y};case"combobox":return{...v,...c&&{"aria-selected":!0}}}return{}}}},[i,s,a,r,o,l,f])}function V(){return V=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},V.apply(this,arguments)}var us=255,_h=360,_n=100,cs=t=>{var{r:e,g:n,b:r,a:o}=t,i=Math.max(e,n,r),s=i-Math.min(e,n,r),a=s?i===e?(n-r)/s:i===n?2+(r-e)/s:4+(e-n)/s:0;return{h:60*(a<0?a+6:a),s:i?s/i*_n:0,v:i/us*_n,a:o}};var lf=t=>{var{h:e,s:n,l:r,a:o}=uf(t);return"hsla("+e+", "+n+"%, "+r+"%, "+o+")"};var ul=t=>{var{h:e,s:n,l:r,a:o}=t;return n*=(r<50?r:_n-r)/_n,{h:e,s:n>0?2*n/(r+n)*_n:0,v:r+n,a:o}},uf=t=>{var{h:e,s:n,v:r,a:o}=t,i=(200-n)*r/_n;return{h:e,s:i>0&&i<200?n*r/_n/(i<=_n?i:200-i)*_n:0,l:i/2,a:o}};var HM={grad:_h/400,turn:_h,rad:_h/(Math.PI*2)};var N1=t=>{var{r:e,g:n,b:r}=t,o=e<<16|n<<8|r;return"#"+(i=>new Array(7-i.length).join("0")+i)(o.toString(16))},M1=t=>{var{r:e,g:n,b:r,a:o}=t,i=typeof o=="number"&&(o*255|1<<8).toString(16).slice(1);return""+N1({r:e,g:n,b:r,a:o})+(i||"")},Er=t=>cs(U2(t)),U2=t=>{var e=t.replace("#","");/^#?/.test(t)&&e.length===3&&(t="#"+e.charAt(0)+e.charAt(0)+e.charAt(1)+e.charAt(1)+e.charAt(2)+e.charAt(2));var n=new RegExp("[A-Za-z0-9]{2}","g"),[r,o,i=0,s]=t.match(n).map(a=>parseInt(a,16));return{r,g:o,b:i,a:(s!=null?s:255)/us}},fs=t=>{var{h:e,s:n,v:r,a:o}=t,i=e/60,s=n/_n,a=r/_n,l=Math.floor(i)%6,u=i-Math.floor(i),f=us*a*(1-s),d=us*a*(1-s*u),m=us*a*(1-s*(1-u));a*=us;var c={};switch(l){case 0:c.r=a,c.g=m,c.b=f;break;case 1:c.r=d,c.g=a,c.b=f;break;case 2:c.r=f,c.g=a,c.b=m;break;case 3:c.r=f,c.g=d,c.b=a;break;case 4:c.r=m,c.g=f,c.b=a;break;case 5:c.r=a,c.g=f,c.b=d;break}return c.r=Math.round(c.r),c.g=Math.round(c.g),c.b=Math.round(c.b),V({},c,{a:o})};var I1=t=>{var{r:e,g:n,b:r,a:o}=fs(t);return"rgba("+e+", "+n+", "+r+", "+o+")"},j2=t=>{var{r:e,g:n,b:r}=t;return{r:e,g:n,b:r}},$2=t=>{var{h:e,s:n,l:r}=t;return{h:e,s:n,l:r}},cl=t=>N1(fs(t)),A1=t=>M1(fs(t)),G2=t=>{var{h:e,s:n,v:r}=t;return{h:e,s:n,v:r}},dt=t=>{var e,n,r,o,i,s,a,l;return typeof t=="string"&&fl(t)?(s=Er(t),a=t):typeof t!="string"&&(s=t),s&&(r=G2(s),i=uf(s),o=fs(s),l=M1(o),a=cl(s),n=$2(i),e=j2(o)),{rgb:e,hsl:n,hsv:r,rgba:o,hsla:i,hsva:s,hex:a,hexa:l}};var fl=t=>/^#?([A-Fa-f0-9]{3,4}){1,2}$/.test(t);function Se(t,e){if(t==null)return{};var n={},r=Object.keys(t),o,i;for(i=0;i<r.length;i++)o=r[i],!(e.indexOf(o)>=0)&&(n[o]=t[o]);return n}var W1=B(se());var Ot=B(se());var ds=B(se());function kh(t){var e=(0,ds.useRef)(t);return(0,ds.useEffect)(()=>{e.current=t}),(0,ds.useCallback)((n,r)=>e.current&&e.current(n,r),[])}var ms=t=>"touches"in t,Oh=t=>{!ms(t)&&t.preventDefault&&t.preventDefault()},L1=function(e,n,r){return n===void 0&&(n=0),r===void 0&&(r=1),e>r?r:e<n?n:e},bh=(t,e)=>{var n=t.getBoundingClientRect(),r=ms(e)?e.touches[0]:e;return{left:L1((r.pageX-(n.left+window.pageXOffset))/n.width),top:L1((r.pageY-(n.top+window.pageYOffset))/n.height),width:n.width,height:n.height,x:r.pageX-(n.left+window.pageXOffset),y:r.pageY-(n.top+window.pageYOffset)}};var P1=B(X()),Y2=["prefixCls","className","onMove","onDown"],B1=Ot.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-interactive",className:r,onMove:o,onDown:i}=t,s=Se(t,Y2),a=(0,Ot.useRef)(null),l=(0,Ot.useRef)(!1),[u,f]=(0,Ot.useState)(!1),d=kh(o),m=kh(i),c=h=>l.current&&!ms(h)?!1:(l.current=ms(h),!0),y=(0,Ot.useCallback)(h=>{Oh(h);var E=ms(h)?h.touches.length>0:h.buttons>0;E&&a.current?d&&d(bh(a.current,h),h):f(!1)},[d]),v=(0,Ot.useCallback)(()=>f(!1),[]),S=(0,Ot.useCallback)(h=>{var E=h?window.addEventListener:window.removeEventListener;E(l.current?"touchmove":"mousemove",y),E(l.current?"touchend":"mouseup",v)},[]);(0,Ot.useEffect)(()=>(S(u),()=>{u&&S(!1)}),[u,S]);var p=(0,Ot.useCallback)(h=>{Oh(h.nativeEvent),c(h.nativeEvent)&&(m&&m(bh(a.current,h.nativeEvent),h.nativeEvent),f(!0))},[m]);return(0,P1.jsx)("div",V({},s,{className:[n,r||""].filter(Boolean).join(" "),style:V({},s.style,{touchAction:"none"}),ref:a,tabIndex:0,onMouseDown:p,onTouchStart:p}))});B1.displayName="Interactive";var cf=B1;var JM=B(se()),Rh=B(X()),Z2=["className","prefixCls","left","top","style","fillProps"],V1=t=>{var{className:e,prefixCls:n,left:r,top:o,style:i,fillProps:s}=t,a=Se(t,Z2),l=V({},i,{position:"absolute",left:r,top:o}),u=V({width:18,height:18,boxShadow:"var(--alpha-pointer-box-shadow)",borderRadius:"50%",backgroundColor:"var(--alpha-pointer-background-color)"},s==null?void 0:s.style,{transform:r?"translate(-9px, -1px)":"translate(-1px, -9px)"});return(0,Rh.jsx)("div",V({className:n+"-pointer "+(e||""),style:l},a,{children:(0,Rh.jsx)("div",V({className:n+"-fill"},s,{style:u}))}))};var ff=B(X()),H1=B(X()),q2=["prefixCls","className","hsva","background","bgProps","innerProps","pointerProps","radius","width","height","direction","style","onChange","pointer"],K2="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==",z1=W1.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-alpha",className:r,hsva:o,background:i,bgProps:s={},innerProps:a={},pointerProps:l={},radius:u=0,width:f,height:d=16,direction:m="horizontal",style:c,onChange:y,pointer:v}=t,S=Se(t,q2),p=F=>{y&&y(V({},o,{a:m==="horizontal"?F.left:F.top}),F)},h=lf(Object.assign({},o,{a:1})),E="linear-gradient(to "+(m==="horizontal"?"right":"bottom")+", rgba(244, 67, 54, 0) 0%, "+h+" 100%)",D={};m==="horizontal"?D.left=o.a*100+"%":D.top=o.a*100+"%";var x=V({"--alpha-background-color":"#fff","--alpha-pointer-background-color":"rgb(248, 248, 248)","--alpha-pointer-box-shadow":"rgb(0 0 0 / 37%) 0px 1px 4px 0px",borderRadius:u,background:"url("+K2+") left center",backgroundColor:"var(--alpha-background-color)"},{width:f,height:d},c,{position:"relative"}),O=v&&typeof v=="function"?v(V({prefixCls:n},l,D)):(0,ff.jsx)(V1,V({},l,{prefixCls:n},D));return(0,H1.jsxs)("div",V({},S,{className:[n,n+"-"+m,r||""].filter(Boolean).join(" "),style:x,ref:e,children:[(0,ff.jsx)("div",V({},s,{style:V({inset:0,position:"absolute",background:i||E,borderRadius:u},s.style)})),(0,ff.jsx)(cf,V({},a,{style:V({},a.style,{inset:0,zIndex:1,position:"absolute"}),onMove:p,onDown:p,children:O}))]}))});z1.displayName="Alpha";var dl=z1;var U1=B(se()),ps=B(se()),Nh=B(X()),j1=B(X()),Q2=["prefixCls","placement","label","value","className","style","labelStyle","inputStyle","onChange","onBlur"],J2=t=>/^#?([A-Fa-f0-9]{3,4}){1,2}$/.test(t),X2=t=>Number(String(t).replace(/%/g,"")),$1=U1.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-editable-input",placement:r="bottom",label:o,value:i,className:s,style:a,labelStyle:l,inputStyle:u,onChange:f,onBlur:d}=t,m=Se(t,Q2),[c,y]=(0,ps.useState)(i),v=(0,ps.useRef)(!1);(0,ps.useEffect)(()=>{t.value!==c&&(v.current||y(t.value))},[t.value]);function S(x,O){var F=(O||x.target.value).trim().replace(/^#/,"");J2(F)&&f&&f(x,F);var k=X2(F);isNaN(k)||f&&f(x,k),y(F)}function p(x){v.current=!1,y(t.value),d&&d(x)}var h={};r==="bottom"&&(h.flexDirection="column"),r==="top"&&(h.flexDirection="column-reverse"),r==="left"&&(h.flexDirection="row-reverse");var E=V({"--editable-input-label-color":"rgb(153, 153, 153)","--editable-input-box-shadow":"rgb(204 204 204) 0px 0px 0px 1px inset","--editable-input-color":"#666",position:"relative",alignItems:"center",display:"flex",fontSize:11},h,a),D=V({width:"100%",paddingTop:2,paddingBottom:2,paddingLeft:3,paddingRight:3,fontSize:11,background:"transparent",boxSizing:"border-box",border:"none",color:"var(--editable-input-color)",boxShadow:"var(--editable-input-box-shadow)"},u);return(0,j1.jsxs)("div",{className:[n,s||""].filter(Boolean).join(" "),style:E,children:[(0,Nh.jsx)("input",V({ref:e,value:c,onChange:S,onBlur:p,autoComplete:"off",onFocus:()=>v.current=!0},m,{style:D})),o&&(0,Nh.jsx)("span",{style:V({color:"var(--editable-input-label-color)",textTransform:"capitalize"},l),children:o})]})});$1.displayName="EditableInput";var Wo=$1;var G1=B(se());var ml=B(X()),Y1=B(X()),eO=["prefixCls","hsva","placement","rProps","gProps","bProps","aProps","className","style","onChange"],Z1=G1.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-editable-input-rgba",hsva:r,placement:o="bottom",rProps:i={},gProps:s={},bProps:a={},aProps:l={},className:u,style:f,onChange:d}=t,m=Se(t,eO),c=r?fs(r):{};function y(S){var p=Number(S.target.value);p&&p>255&&(S.target.value="255"),p&&p<0&&(S.target.value="0")}var v=(S,p,h)=>{typeof S=="number"&&(p==="a"&&(S<0&&(S=0),S>100&&(S=100),d&&d(dt(cs(V({},c,{a:S/100}))))),S>255&&(S=255,h.target.value="255"),S<0&&(S=0,h.target.value="0"),p==="r"&&d&&d(dt(cs(V({},c,{r:S})))),p==="g"&&d&&d(dt(cs(V({},c,{g:S})))),p==="b"&&d&&d(dt(cs(V({},c,{b:S})))))};return(0,Y1.jsxs)("div",V({ref:e,className:[n,u||""].filter(Boolean).join(" ")},m,{style:V({fontSize:11,display:"flex"},f),children:[(0,ml.jsx)(Wo,V({label:"R",value:c.r||0,onBlur:y,placement:o,onChange:(S,p)=>v(p,"r",S)},i,{style:V({},i.style)})),(0,ml.jsx)(Wo,V({label:"G",value:c.g||0,onBlur:y,placement:o,onChange:(S,p)=>v(p,"g",S)},s,{style:V({marginLeft:5},i.style)})),(0,ml.jsx)(Wo,V({label:"B",value:c.b||0,onBlur:y,placement:o,onChange:(S,p)=>v(p,"b",S)},a,{style:V({marginLeft:5},a.style)})),l&&(0,ml.jsx)(Wo,V({label:"A",value:c.a?parseInt(String(c.a*100),10):0,onBlur:y,placement:o,onChange:(S,p)=>v(p,"a",S)},l,{style:V({marginLeft:5},l.style)}))]}))});Z1.displayName="EditableInputRGBA";var df=Z1;var oo=B(se());var Mh=B(X()),q1=B(X()),tO=["prefixCls","className","color","colors","style","rectProps","onChange","addonAfter","addonBefore","rectRender"],K1=oo.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-swatch",className:r,color:o,colors:i=[],style:s,rectProps:a={},onChange:l,addonAfter:u,addonBefore:f,rectRender:d}=t,m=Se(t,tO),c=V({"--swatch-background-color":"rgb(144, 19, 254)",background:"var(--swatch-background-color)",height:15,width:15,marginRight:5,marginBottom:5,cursor:"pointer",position:"relative",outline:"none",borderRadius:2},a.style),y=(v,S)=>{l&&l(Er(v),dt(Er(v)),S)};return(0,q1.jsxs)("div",V({ref:e},m,{className:[n,r||""].filter(Boolean).join(" "),style:V({display:"flex",flexWrap:"wrap",position:"relative"},s),children:[f&&oo.default.isValidElement(f)&&f,i&&Array.isArray(i)&&i.map((v,S)=>{var p="",h="";typeof v=="string"&&(p=v,h=v),typeof v=="object"&&v.color&&(p=v.title||v.color,h=v.color);var E=o&&o.toLocaleLowerCase()===h.toLocaleLowerCase(),D=d&&d({title:p,color:h,checked:!!E,style:V({},c,{background:h}),onClick:O=>y(h,O)});if(D)return(0,Mh.jsx)(oo.Fragment,{children:D},S);var x=a.children&&oo.default.isValidElement(a.children)?oo.default.cloneElement(a.children,{color:h,checked:E}):null;return(0,Mh.jsx)("div",V({tabIndex:0,title:p,onClick:O=>y(h,O)},a,{children:x,style:V({},c,{background:h})}),S)}),u&&oo.default.isValidElement(u)&&u]}))});K1.displayName="Swatch";var Q1=K1;var ys=B(se());function Ih(t){if(t==null)throw new TypeError("Cannot destructure "+t)}var mf=B(se());var Ah=B(se()),J1=B(se()),X1=B(X()),nO={marginRight:0,marginBottom:0,borderRadius:0,boxSizing:"border-box",height:25,width:25};function Lh(t){var{style:e,title:n,checked:r,color:o,onClick:i,rectProps:s}=t,a=(0,J1.useRef)(null),l=(0,Ah.useCallback)(()=>{a.current.style.zIndex="2",a.current.style.outline="#fff solid 2px",a.current.style.boxShadow="rgb(0 0 0 / 25%) 0 0 5px 2px"},[]),u=(0,Ah.useCallback)(()=>{r||(a.current.style.zIndex="0",a.current.style.outline="initial",a.current.style.boxShadow="initial")},[r]),f=r?{zIndex:1,outline:"#fff solid 2px",boxShadow:"rgb(0 0 0 / 25%) 0 0 5px 2px"}:{zIndex:0};return(0,X1.jsx)("div",V({ref:a,title:n},s,{onClick:i,onMouseEnter:l,onMouseLeave:u,style:V({},e,{marginRight:0,marginBottom:0,borderRadius:0,boxSizing:"border-box",height:25,width:25},nO,f,s==null?void 0:s.style)}))}var pl=B(X()),eS=B(X()),rO=["prefixCls","placement","className","style","color","colors","rectProps","onChange","rectRender"],oO=["#B80000","#DB3E00","#FCCB00","#008B02","#006B76","#1273DE","#004DCF","#5300EB","#EB9694","#FAD0C3","#FEF3BD","#C1E1C5","#BEDADC","#C4DEF6","#BED3F3","#D4C4FB"],Be=function(t){return t.Left="L",t.LeftTop="LT",t.LeftBottom="LB",t.Right="R",t.RightTop="RT",t.RightBottom="RB",t.Top="T",t.TopRight="TR",t.TopLeft="TL",t.Bottom="B",t.BottomLeft="BL",t.BottomRight="BR",t}({}),tS=mf.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-github",placement:r=Be.TopRight,className:o,style:i,color:s,colors:a=oO,rectProps:l={},onChange:u,rectRender:f}=t,d=Se(t,rO),m=typeof s=="string"&&fl(s)?Er(s):s,c=s?cl(m):"",y=D=>u&&u(dt(D)),v=V({"--github-border":"1px solid rgba(0, 0, 0, 0.2)","--github-background-color":"#fff","--github-box-shadow":"rgb(0 0 0 / 15%) 0px 3px 12px","--github-arrow-border-color":"rgba(0, 0, 0, 0.15)",width:200,borderRadius:4,background:"var(--github-background-color)",boxShadow:"var(--github-box-shadow)",border:"var(--github-border)",position:"relative",padding:5},i),S={borderStyle:"solid",position:"absolute"},p=V({},S),h=V({},S);/^T/.test(r)&&(p.borderWidth="0 8px 8px",p.borderColor="transparent transparent var(--github-arrow-border-color)",h.borderWidth="0 7px 7px",h.borderColor="transparent transparent var(--github-background-color)"),r===Be.TopRight&&(p.top=-8,h.top=-7),r===Be.Top&&(p.top=-8,h.top=-7),r===Be.TopLeft&&(p.top=-8,h.top=-7),/^B/.test(r)&&(p.borderWidth="8px 8px 0",p.borderColor="var(--github-arrow-border-color) transparent transparent",h.borderWidth="7px 7px 0",h.borderColor="var(--github-background-color) transparent transparent",r===Be.BottomRight&&(p.top="100%",h.top="100%"),r===Be.Bottom&&(p.top="100%",h.top="100%"),r===Be.BottomLeft&&(p.top="100%",h.top="100%")),/^(B|T)/.test(r)&&((r===Be.Top||r===Be.Bottom)&&(p.left="50%",p.marginLeft=-8,h.left="50%",h.marginLeft=-7),(r===Be.TopRight||r===Be.BottomRight)&&(p.right=10,h.right=11),(r===Be.TopLeft||r===Be.BottomLeft)&&(p.left=7,h.left=8)),/^L/.test(r)&&(p.borderWidth="8px 8px 8px 0",p.borderColor="transparent var(--github-arrow-border-color) transparent transparent",h.borderWidth="7px 7px 7px 0",h.borderColor="transparent var(--github-background-color) transparent transparent",p.left=-8,h.left=-7),/^R/.test(r)&&(p.borderWidth="8px 0 8px 8px",p.borderColor="transparent transparent transparent var(--github-arrow-border-color)",h.borderWidth="7px 0 7px 7px",h.borderColor="transparent transparent transparent var(--github-background-color)",p.right=-8,h.right=-7),/^(L|R)/.test(r)&&((r===Be.RightTop||r===Be.LeftTop)&&(p.top=5,h.top=6),(r===Be.Left||r===Be.Right)&&(p.top="50%",h.top="50%",p.marginTop=-8,h.marginTop=-7),(r===Be.LeftBottom||r===Be.RightBottom)&&(p.top="100%",h.top="100%",p.marginTop=-21,h.marginTop=-20));var E=D=>{var x=V({},(Ih(D),D)),O=f&&f(V({},x));return O||(0,pl.jsx)(Lh,V({},x,{rectProps:l}))};return(0,pl.jsx)(Q1,V({ref:e,className:[n,o].filter(Boolean).join(" "),colors:a,color:c,rectRender:E},d,{onChange:y,style:v,rectProps:{style:{marginRight:0,marginBottom:0,borderRadius:0,height:25,width:25}},addonBefore:(0,eS.jsxs)(mf.Fragment,{children:[(0,pl.jsx)("div",{style:p}),(0,pl.jsx)("div",{style:h})]})}))});tS.displayName="Github";var nS=tS;var pf=B(se());var RI=B(se()),rS=B(se()),Ph=B(X()),oS=t=>{var{className:e,color:n,left:r,top:o,prefixCls:i}=t,s={position:"absolute",top:o,left:r},a={"--saturation-pointer-box-shadow":"rgb(255 255 255) 0px 0px 0px 1.5px, rgb(0 0 0 / 30%) 0px 0px 1px 1px inset, rgb(0 0 0 / 40%) 0px 0px 1px 2px",width:6,height:6,transform:"translate(-3px, -3px)",boxShadow:"var(--saturation-pointer-box-shadow)",borderRadius:"50%",backgroundColor:n};return(0,rS.useMemo)(()=>(0,Ph.jsx)("div",{className:i+"-pointer "+(e||""),style:s,children:(0,Ph.jsx)("div",{className:i+"-fill",style:a})}),[o,r,n,e,i])};var Bh=B(X()),iO=["prefixCls","radius","pointer","className","hue","style","hsva","onChange"],iS=pf.default.forwardRef((t,e)=>{var n,{prefixCls:r="w-color-saturation",radius:o=0,pointer:i,className:s,hue:a=0,style:l,hsva:u,onChange:f}=t,d=Se(t,iO),m=V({width:200,height:200,borderRadius:o},l,{position:"relative"}),c=(v,S)=>{f&&u&&f({h:u.h,s:v.left*100,v:(1-v.top)*100,a:u.a})},y=(0,pf.useMemo)(()=>{if(!u)return null;var v={top:100-u.v+"%",left:u.s+"%",color:lf(u)};return i&&typeof i=="function"?i(V({prefixCls:r},v)):(0,Bh.jsx)(oS,V({prefixCls:r},v))},[u,i,r]);return(0,Bh.jsx)(cf,V({className:[r,s||""].filter(Boolean).join(" ")},d,{style:V({position:"absolute",inset:0,cursor:"crosshair",backgroundImage:"linear-gradient(0deg, #000, transparent), linear-gradient(90deg, #fff, hsl("+((n=u==null?void 0:u.h)!=null?n:a)+", 100%, 50%))"},m),ref:e,onMove:c,onDown:c,children:y}))});iS.displayName="Saturation";var sS=iS;var aS=B(se());var lS=B(X()),sO=["prefixCls","className","hue","onChange","direction"],uS=aS.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-hue",className:r,hue:o=0,onChange:i,direction:s="horizontal"}=t,a=Se(t,sO);return(0,lS.jsx)(dl,V({ref:e,className:n+" "+(r||"")},a,{direction:s,background:"linear-gradient(to "+(s==="horizontal"?"right":"bottom")+", rgb(255, 0, 0) 0%, rgb(255, 255, 0) 17%, rgb(0, 255, 0) 33%, rgb(0, 255, 255) 50%, rgb(0, 0, 255) 67%, rgb(255, 0, 255) 83%, rgb(255, 0, 0) 100%)",hsva:{h:o,s:100,v:100,a:o/360},onChange:(l,u)=>{i&&i({h:s==="horizontal"?360*u.left:360*u.top})}}))});uS.displayName="Hue";var cS=uS;var fS=B(se());var dS=B(X()),aO=["prefixCls","hsva","hProps","sProps","lProps","aProps","className","onChange"],mS=fS.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-editable-input-hsla",hsva:r,hProps:o={},sProps:i={},lProps:s={},aProps:a={},className:l,onChange:u}=t,f=Se(t,aO),d=r?uf(r):{h:0,s:0,l:0,a:0},m=(c,y,v)=>{typeof c=="number"&&(y==="h"&&(c<0&&(c=0),c>360&&(c=360),u&&u(dt(ul(V({},d,{h:c}))))),y==="s"&&(c<0&&(c=0),c>100&&(c=100),u&&u(dt(ul(V({},d,{s:c}))))),y==="l"&&(c<0&&(c=0),c>100&&(c=100),u&&u(dt(ul(V({},d,{l:c}))))),y==="a"&&(c<0&&(c=0),c>1&&(c=1),u&&u(dt(ul(V({},d,{a:c}))))))};return(0,dS.jsx)(df,V({ref:e,hsva:r,rProps:V({label:"H",value:Math.round(d.h)},o,{onChange:(c,y)=>m(y,"h",c)}),gProps:V({label:"S",value:Math.round(d.s)+"%"},i,{onChange:(c,y)=>m(y,"s",c)}),bProps:V({label:"L",value:Math.round(d.l)+"%"},s,{onChange:(c,y)=>m(y,"l",c)}),aProps:V({label:"A",value:Math.round(d.a*100)/100},a,{onChange:(c,y)=>m(y,"a",c)}),className:[n,l||""].filter(Boolean).join(" ")},f))});mS.displayName="EditableInputHSLA";var pS=mS;var hS=B(se());var hl=B(se()),hf=B(X()),lO=["style"];function Vh(t){var{style:e}=t,n=Se(t,lO),r=(0,hl.useRef)(null),o=(0,hl.useCallback)(()=>{r.current.style.backgroundColor="var(--chrome-arrow-background-color)"},[]),i=(0,hl.useCallback)(()=>{r.current.style.backgroundColor="transparent"},[]);return(0,hf.jsx)("div",V({ref:r,style:V({marginLeft:5,cursor:"pointer",transition:"background-color .3s",borderRadius:2},e)},n,{onMouseEnter:o,onMouseLeave:i,children:(0,hf.jsx)("svg",{viewBox:"0 0 1024 1024",width:"24",height:"24",style:{display:"block"},children:(0,hf.jsx)("path",{d:"M373.888 576h276.224c9.322667 0 14.293333 11.178667 9.173333 18.773333l-1.258666 1.557334-138.112 146.858666a10.709333 10.709333 0 0 1-14.293334 1.365334l-1.536-1.365334-138.112-146.858666c-6.592-6.997333-2.666667-18.645333 5.973334-20.16l1.941333-0.170667h276.224-276.224z m146.026667-295.189333l138.112 146.858666c7.04 7.509333 2.069333 20.330667-7.914667 20.330667H373.888c-9.984 0-14.976-12.821333-7.914667-20.330667l138.112-146.858666a10.730667 10.730667 0 0 1 15.829334 0z",fill:"var(--chrome-arrow-fill)"})})}))}var rn=B(X()),hs=B(X()),uO=["prefixCls","className","style","color","inputType","rectProps","onChange"],Zn=function(t){return t.HEXA="hexa",t.RGBA="rgba",t.HSLA="hsla",t}({}),yS=ys.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-chrome",className:r,style:o,color:i,inputType:s=Zn.RGBA,rectProps:a={},onChange:l}=t,u=Se(t,uO),f=typeof i=="string"&&fl(i)?Er(i):i||{h:0,s:0,l:0,a:0},d=E=>l&&l(dt(E)),[m,c]=(0,hS.useState)(s),y=()=>{m===Zn.RGBA&&c(Zn.HSLA),m===Zn.HSLA&&c(Zn.HEXA),m===Zn.HEXA&&c(Zn.RGBA)},v={paddingTop:6},S={textAlign:"center",paddingTop:4,paddingBottom:4},p=V({"--chrome-arrow-fill":"#333","--chrome-arrow-background-color":"#e8e8e8",borderRadius:0,flexDirection:"column",width:230,padding:0},o),h={"--chrome-alpha-box-shadow":"rgb(0 0 0 / 25%) 0px 0px 1px inset",borderRadius:"50%",background:I1(f),boxShadow:"var(--chrome-alpha-box-shadow)"};return(0,rn.jsx)(nS,V({ref:e,color:f,style:p,colors:void 0,className:[n,r].filter(Boolean).join(" "),placement:Be.TopLeft},u,{addonAfter:(0,hs.jsxs)(ys.Fragment,{children:[(0,rn.jsx)(sS,{hsva:f,style:{width:"100%",height:130},onChange:E=>{d(V({},f,E,{a:f.a}))}}),(0,hs.jsxs)("div",{style:{padding:15,display:"flex",alignItems:"center"},children:[(0,rn.jsx)(dl,{width:24,height:24,hsva:f,radius:2,style:{marginRight:15,borderRadius:"50%"},bgProps:{style:{background:"transparent"}},innerProps:{style:h},pointer:()=>(0,rn.jsx)(ys.Fragment,{})}),(0,hs.jsxs)("div",{style:{flex:1},children:[(0,rn.jsx)(cS,{hue:f.h,style:{width:"100%"},bgProps:{style:{borderRadius:2}},onChange:E=>{d(V({},f,E))}}),(0,rn.jsx)(dl,{hsva:f,style:{marginTop:10},bgProps:{style:{borderRadius:2}},onChange:E=>{d(V({},f,E))}})]})]}),(0,hs.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",padding:"0 15px 15px 15px",userSelect:"none"},children:[(0,hs.jsxs)("div",{style:{flex:1},children:[m==Zn.RGBA&&(0,rn.jsx)(df,{hsva:f,rProps:{labelStyle:v,inputStyle:S},gProps:{labelStyle:v,inputStyle:S},bProps:{labelStyle:v,inputStyle:S},aProps:{labelStyle:v,inputStyle:S},onChange:E=>d(E.hsva)}),m===Zn.HEXA&&(0,rn.jsx)(Wo,{label:"HEX",labelStyle:v,inputStyle:S,value:f.a>0&&f.a<1?A1(f).toLocaleUpperCase():cl(f).toLocaleUpperCase(),onChange:(E,D)=>{typeof D=="string"&&d(Er(/^#/.test(D)?D:"#"+D))}}),m===Zn.HSLA&&(0,rn.jsx)(pS,{hsva:f,hProps:{labelStyle:v,inputStyle:S},sProps:{labelStyle:v,inputStyle:S},lProps:{labelStyle:v,inputStyle:S},aProps:{labelStyle:v,inputStyle:S},onChange:E=>d(E.hsva)})]}),(0,rn.jsx)(Vh,{onClick:y})]})]}),rectRender:()=>(0,rn.jsx)(ys.Fragment,{})}))});yS.displayName="Chrome";var yl=yS;var Wh=B(se()),Et=B(X());function gS(t){let[e,n]=(0,Wh.useState)(t.rule),[r,o]=(0,Wh.useState)(!1),i=(d,m)=>{let c={...e,[d]:m};n(c),t.onChange(c)},{refs:s,floatingStyles:a,context:l}=as({open:r,onOpenChange:d=>o(d),middleware:[Yn(6),gr(),vr(),Dr()],whileElementsMounted:ro}),u=ss(l),{getFloatingProps:f}=ls([u]);return(0,Et.jsx)("div",{className:"form-item",children:(0,Et.jsxs)("div",{className:"form-content",ref:s.setReference,children:[(0,Et.jsx)("button",{className:"list-remove-button",onClick:()=>t.onRemove(t.rule.id),children:"x"}),(0,Et.jsx)("input",{type:"number",defaultValue:t.rule.min,placeholder:"min",className:"cell-rule-value",onChange:d=>i("min",d.target.value)}),(0,Et.jsx)("span",{children:"\u2264"}),(0,Et.jsx)("span",{children:"contributions"}),(0,Et.jsx)("span",{children:"\uFF1C"}),(0,Et.jsx)("input",{type:"number",defaultValue:t.rule.max,placeholder:"max",className:"cell-rule-value",onChange:d=>i("max",d.target.value)}),(0,Et.jsx)("span",{children:"="}),(0,Et.jsx)("span",{className:"color-indicator",style:{backgroundColor:t.rule.color},onClick:()=>o(!r)}),r?(0,Et.jsx)("div",{ref:s.setFloating,style:{...a},...f(),children:(0,Et.jsx)(yl,{color:t.rule.color,onChange:d=>{i("color",d.hexa)}})}):null,(0,Et.jsx)("input",{type:"text",defaultValue:t.rule.text,placeholder:"emoji",className:"cell-rule-text",onChange:d=>i("text",d.target.value)})]})})}var Hh=[{name:q.get().form_theme_placeholder,description:"",rules:[]},{name:"default",description:"",rules:Gl},{name:"Ocean",description:"",rules:yf("Ocean","#8dd1e2","#63a1be","#376d93","#012f60")},{name:"Halloween",description:"",rules:yf("Halloween","#fdd577","#faaa53","#f07c44","#d94e49")},{name:"Lovely",description:"",rules:yf("Lovely","#fedcdc","#fdb8bf","#f892a9","#ec6a97")},{name:"Wine",description:"",rules:yf("Wine","#d8b0b3","#c78089","#ac4c61","#830738")}];function yf(t,e,n,r,o){return[{id:`${t}_a`,color:e,min:1,max:2},{id:`${t}_b`,color:n,min:2,max:3},{id:`${t}_c`,color:r,min:3,max:5},{id:`${t}_d`,color:o,min:5,max:9999}]}var et=B(X()),on={CODE:(0,et.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"lucide lucide-code-2",children:[(0,et.jsx)("path",{d:"m18 16 4-4-4-4"}),(0,et.jsx)("path",{d:"m6 8-4 4 4 4"}),(0,et.jsx)("path",{d:"m14.5 4-5 16"})]}),ALIGN_LEFT:(0,et.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"svg-icon lucide lucide-align-left",children:[(0,et.jsx)("line",{x1:"21",x2:"3",y1:"6",y2:"6"}),(0,et.jsx)("line",{x1:"15",x2:"3",y1:"12",y2:"12"}),(0,et.jsx)("line",{x1:"17",x2:"3",y1:"18",y2:"18"})]}),ALIGN_CENTER:(0,et.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"svg-icon lucide lucide-align-center",children:[(0,et.jsx)("line",{x1:"21",x2:"3",y1:"6",y2:"6"}),(0,et.jsx)("line",{x1:"17",x2:"7",y1:"12",y2:"12"}),(0,et.jsx)("line",{x1:"19",x2:"5",y1:"18",y2:"18"})]}),ALIGN_RIGHT:(0,et.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"svg-icon lucide lucide-align-right",children:[(0,et.jsx)("line",{x1:"21",x2:"3",y1:"6",y2:"6"}),(0,et.jsx)("line",{x1:"21",x2:"9",y1:"12",y2:"12"}),(0,et.jsx)("line",{x1:"21",x2:"7",y1:"18",y2:"18"})]})};var DS=[{tip:"left",icon:on.ALIGN_LEFT,value:"left"},{tip:"center",icon:on.ALIGN_CENTER,value:"center"},{tip:"right",icon:on.ALIGN_RIGHT,value:"right"}],zh=[{label:q.get().form_graph_type_git,value:"default",selected:!0},{label:q.get().form_graph_type_month_track,value:"month-track"},{label:q.get().form_graph_type_calendar,value:"calendar"}],Uh=[{label:q.get().weekday_sunday,value:"0",selected:!Ks()},{label:q.get().weekday_monday,value:"1",selected:Ks()},{label:q.get().weekday_tuesday,value:"2"},{label:q.get().weekday_wednesday,value:"3"},{label:q.get().weekday_thursday,value:"4"},{label:q.get().weekday_friday,value:"5"},{label:q.get().weekday_saturday,value:"6"}],jh=[{label:q.get().form_cell_shape_rounded,value:"",selected:!0},{label:q.get().form_cell_shape_square,value:"0%"},{label:q.get().form_cell_shape_circle,value:"50%"}],vS=[{label:q.get().form_datasource_type_page,value:"PAGE",selected:!0},{label:q.get().form_datasource_type_all_task,value:"ALL_TASK"},{label:q.get().form_datasource_type_task_in_specific_page,value:"TASK_IN_SPECIFIC_PAGE"}];function ES(t){return t==="PAGE"?[]:[{label:q.get().form_datasource_filter_type_none,value:"NONE"},{label:q.get().form_datasource_filter_type_status_is,value:"STATUS_IS"},{label:q.get().form_datasource_filter_type_status_in,value:"STATUS_IN"},{label:q.get().form_datasource_filter_type_contains_any_tag,value:"CONTAINS_ANY_TAG"}]}var wS=t=>{let e=[{label:q.get().form_count_field_count_field_type_default,value:"DEFAULT"},{label:q.get().form_count_field_count_field_type_page_prop,value:"PAGE_PROPERTY"}];return(t==="ALL_TASK"||t==="TASK_IN_SPECIFIC_PAGE")&&e.push({label:q.get().form_count_field_count_field_type_task_prop,value:"TASK_PROPERTY"}),e},SS=t=>{let e=[{label:q.get().form_date_field_type_file_ctime,value:"FILE_CTIME"},{label:q.get().form_date_field_type_file_mtime,value:"FILE_MTIME"},{label:q.get().form_date_field_type_file_name,value:"FILE_NAME"},{label:q.get().form_date_field_type_file_specific_page_property,value:"PAGE_PROPERTY"}];return(t==="ALL_TASK"||t==="TASK_IN_SPECIFIC_PAGE")&&e.push({label:q.get().form_date_field_type_file_specific_task_property,value:"TASK_PROPERTY"}),e},CS=[{label:q.get().form_datasource_filter_task_status_completed,value:"COMPLETED",selected:!0},{label:q.get().form_datasource_filter_task_status_fully_completed,value:"FULLY_COMPLETED"},{label:q.get().form_datasource_filter_task_status_incomplete,value:"INCOMPLETE"},{label:q.get().form_datasource_filter_task_status_canceled,value:"CANCELED"},{label:q.get().form_datasource_filter_task_status_any,value:"ANY"}],xS=[{label:q.get().form_date_range_latest_days,value:"LATEST_DAYS"},{label:q.get().form_date_range_fixed_date,value:"FIXED_DATE_RANGE"},{label:q.get().form_date_range_latest_month,value:"LATEST_MONTH"},{label:q.get().form_date_range_latest_year,value:"LATEST_YEAR"}];var vl=B(se());var gl=require("obsidian");function $h(t,e){let n=new Map,r=t==null?void 0:t.toLowerCase();return e.vault.getAllLoadedFiles().forEach(o=>{if(o instanceof gl.TFile){let i=e.metadataCache.getCache(o.path);if(i){for(let s in i.frontmatter)if(!n.has(s)&&(!r||s.toLowerCase().includes(r))){let a=i.frontmatter[s];n.set(s,{name:s,sampleValue:a})}}}}),Array.from(n.values())}function TS(t,e){let n=e.vault.getAllLoadedFiles(),r=[],o=t.toLowerCase();return n.forEach(i=>{if(i instanceof gl.TFile){let s=this.app.metadataCache.getCache(i.path);if(s){let a=(0,gl.getAllTags)(s);a==null||a.forEach(l=>{l.toLowerCase().contains(o)&&!r.includes(l)&&r.push(l)})}}}),r.map(i=>({id:i,label:i,value:i}))}var Dl=B(se());var FS=require("obsidian"),Gh=B(se()),Ho=B(se()),kn=B(X());function gf(t){let{query:e,onOpenChange:n,anchorElement:r,showSuggest:o}=t,[i,s]=Gh.useState(-1),[a,l]=Gh.useState([]);(0,Ho.useEffect)(()=>{if(e==null||e==null)return;let v=(0,FS.debounce)(S=>{l(t.getItems(S))},300,!0);return v(e),()=>{v.cancel()}},[e,t.getItems]),(0,Ho.useEffect)(()=>{s(-1)},[e]),(0,Ho.useEffect)(()=>{t.onSelectChange&&(i==null?t.onSelectChange(null,-1):t.onSelectChange(a[i],i))},[i,a]);let{refs:u,floatingStyles:f,context:d}=as({open:o,onOpenChange:n,middleware:[Yn(6),gr(),vr(),Dr()],whileElementsMounted:ro,elements:{reference:r}}),m=ss(d),c=R1(d,{role:"tooltip"}),{getFloatingProps:y}=ls([m,c]);return(0,Ho.useEffect)(()=>{if(!o)return;function v(S){if(S.key==="ArrowDown")S.preventDefault(),s(p=>(p+1)%a.length);else if(S.key==="ArrowUp")S.preventDefault(),s(p=>(p-1+a.length)%a.length);else if(S.key==="Enter"){if(i<0||i>=a.length)return;a.length>0&&(S.preventDefault(),t.onSelected(a[i],i))}else S.key==="Escape"&&(S.preventDefault(),al())}return window.addEventListener("keydown",v),()=>{window.removeEventListener("keydown",v)}},[i,a,o]),(0,Ho.useLayoutEffect)(()=>{var p;if(!o||i==null)return;let v=(p=u.floating)==null?void 0:p.current,S=v==null?void 0:v.children[i];S&&v&&cO(v,S)},[i,o]),(0,kn.jsx)(kn.Fragment,{children:o&&a.length>0&&(0,kn.jsx)("div",{className:"suggest-container",ref:u.setFloating,style:{...f},...y(),children:a.map((v,S)=>(0,kn.jsxs)("div",{className:`suggest-item ${S===i?"selected":""}`,onClick:p=>{p.preventDefault(),t.onSelected(v,S),s(S)},children:[v.icon&&(0,kn.jsx)("div",{className:"suggest-icon",children:v.icon}),(0,kn.jsxs)("div",{className:"suggest-content",children:[(0,kn.jsx)("div",{className:"suggest-label",children:v.label}),(0,kn.jsx)("div",{className:"suggest-description",children:v.description})]})]},v.id))})})}var cO=(t,e)=>{let n=t.offsetHeight,r=e?e.offsetHeight:0,o=e.offsetTop,i=o+r;o<t.scrollTop?t.scrollTop-=t.scrollTop-o+5:i>n+t.scrollTop&&(t.scrollTop+=i-n-t.scrollTop+5)};var zo=B(X());function Yh(t){let{inputPlaceholder:e}=t,[n,r]=Dl.useState(t.defaultInputValue),[o,i]=Dl.useState(!1),s=Dl.useRef(null);return(0,zo.jsxs)(zo.Fragment,{children:[(0,zo.jsx)("input",{type:"text",placeholder:e||"",ref:s,onChange:a=>{t.onInputChange(a.target.value),r(a.target.value),i(!0)},value:n}),s.current&&(0,zo.jsx)(gf,{query:n||"",showSuggest:o,getItems:t.getItems,onSelected:a=>{t.onSelected(a),r(a.value),i(!1)},anchorElement:s.current,onOpenChange:a=>i(a)})]})}var gs=B(se());var zt=B(X());function Zh(t){let[e,n]=(0,gs.useState)(""),[r,o]=(0,gs.useState)(!1),[i,s]=(0,gs.useState)(!1),{tags:a}=t,l=(0,gs.useRef)(null),u=m=>{let c=a.filter(y=>y.id!==m);t.onChange(c)},f=m=>{let c=new Date().getTime().toString()+"_"+m,y=[...a,{id:c,value:m}];t.onChange(y)},d=m=>{var y,v;if(r)return;let{key:c}=m;if(!((y=t.excludeTriggerKeys)!=null&&y.includes(c))&&(c==="Tab"||c==="Enter"||c===" ")){m.preventDefault(),s(!1);let S=(v=l.current)==null?void 0:v.value;S&&(f(S),l.current.value="")}};return(0,zt.jsxs)(zt.Fragment,{children:[(0,zt.jsxs)("div",{className:"suggest-input-tags",children:[(0,zt.jsx)("div",{className:"tags",children:a==null?void 0:a.map((m,c)=>(0,zt.jsxs)("div",{className:"tag",children:[(0,zt.jsx)("span",{className:"icon",children:m.icon}),(0,zt.jsx)("span",{children:m.value}),(0,zt.jsx)("span",{className:"remove-button",onClick:()=>u(m.id),children:"x"})]},m.id))}),(0,zt.jsx)("input",{ref:l,className:"input",placeholder:t.inputPlaceholder,onFocus:()=>s(!0),onKeyDown:m=>d(m),onChange:m=>{n(m.target.value),i||s(!0)}})]}),l.current&&(0,zt.jsx)(gf,{query:e||"",showSuggest:i,getItems:()=>t.getItems?t.getItems(e):[],onSelected:(m,c)=>{c>=0&&(f(m.value),l.current.value=""),i&&s(!1)},onSelectChange:(m,c)=>{c>=0?o(!0):o(!1)},anchorElement:l.current,onOpenChange:m=>s(m)})]})}var te=B(X());function _S(t){var S,p,h,E,D,x,O,F,k,P,M,ee,oe;let{dataSource:e}=t,[n,r]=(0,vl.useState)((S=e.dateField)!=null&&S.format?"manual":"smart_detect"),[o,i]=(0,vl.useState)(e.type||"PAGE"),s=(b,_)=>{let R={...e,[b]:_};t.onChange(R)},a=(b,_)=>{let R={...e.dateField,[b]:_};s("dateField",R)},l=(b,_)=>{let R={...e.countField,[b]:_};s("countField",R)},u=(b,_,R)=>{var Q;let W=(Q=e.filters)==null?void 0:Q.map(K=>K.id==b?_=="type"&&R=="STATUS_IS"?{...K,[_]:R,value:"COMPLETED"}:{...K,[_]:R}:K);s("filters",W)},f=b=>{let _=e.filters||[];_.push({id:b,type:"NONE"}),s("filters",_)},d=b=>{var R;let _=(R=e.filters)==null?void 0:R.filter(W=>W.id!=b);s("filters",_||[])},m=b=>{var R,W;let _={...e,type:b};b==="PAGE"&&(_.filters=[],((R=_.dateField)==null?void 0:R.type)==="TASK_PROPERTY"&&(_.dateField={type:"FILE_CTIME"}),((W=e.countField)==null?void 0:W.type)==="TASK_PROPERTY"&&(l("type","DEFAULT"),l("value",void 0),_.countField={type:"DEFAULT"})),t.onChange(_)},c=b=>b.type!="CONTAINS_ANY_TAG"&&b.type!="STATUS_IN"?[]:b.value instanceof Array?b.value.map(_=>({id:_,label:_,value:_})):[],y=["ALL_TASK","TASK_IN_SPECIFIC_PAGE"],v=q.get();return(0,te.jsxs)(vl.Fragment,{children:[(0,te.jsxs)("div",{className:"form-item",children:[(0,te.jsx)("span",{className:"label",children:v.form_data_source_value}),(0,te.jsxs)("div",{className:"form-content",children:[(0,te.jsx)("select",{defaultValue:e.type||"PAGE",onChange:b=>{i(b.target.value),m(b.target.value)},children:vS.map(b=>(0,te.jsx)("option",{value:b.value,children:b.label},b.value))}),o!="ALL_TASK"&&(0,te.jsx)("input",{type:"text",defaultValue:e.value,placeholder:v.form_query_placeholder,onChange:b=>{s("value",b.target.value)}})]})]}),y.includes(e.type)&&(0,te.jsxs)("div",{className:"form-item",children:[(0,te.jsx)("span",{className:"label",children:v.form_data_source_filter_label}),(0,te.jsxs)("div",{className:"form-vertical-content",children:[(p=e.filters)==null?void 0:p.map((b,_)=>(0,te.jsxs)("div",{className:"form-content",children:[(0,te.jsx)("select",{value:b.type||"NONE",onChange:R=>{u(b.id,"type",R.target.value)},children:ES("TASK").map(R=>(0,te.jsx)("option",{value:R.value,children:R.label},R.value))}),b.type=="STATUS_IS"&&(0,te.jsx)("select",{defaultValue:(b==null?void 0:b.value)||"NONE",onChange:R=>{u(b.id,"value",R.target.value)},children:CS.map(R=>(0,te.jsx)("option",{value:R.value,children:R.label},R.value))}),(b==null?void 0:b.type)=="CONTAINS_ANY_TAG"?(0,te.jsx)(Zh,{tags:c(b),onChange:R=>{u(b.id,"value",R.map(W=>W.value))},onRemove:R=>{var W;(b==null?void 0:b.value)instanceof Array&&u(b.id,"value",(W=b==null?void 0:b.value)==null?void 0:W.filter(Q=>Q!=R.value))},getItems:R=>TS(R,t.app),inputPlaceholder:v.form_datasource_filter_contains_tag_input_placeholder}):null,(b==null?void 0:b.type)=="STATUS_IN"?(0,te.jsx)(Zh,{tags:c(b),onChange:R=>{u(b.id,"value",R.map(W=>W.value))},onRemove:R=>{var W;(b==null?void 0:b.value)instanceof Array&&u(b.id,"value",(W=b==null?void 0:b.value)==null?void 0:W.filter(Q=>Q!=R.value))},getItems:R=>[{id:"CANCELED",label:v.form_datasource_filter_task_status_canceled,value:"CANCELED",icon:on.CODE},{id:"COMPLETED",label:v.form_datasource_filter_task_status_completed,value:"COMPLETED",icon:on.CODE},{id:"INCOMPLETE",label:v.form_datasource_filter_task_status_incomplete,value:"INCOMPLETE",icon:on.CODE},{id:"ANY",label:v.form_datasource_filter_task_status_any,value:"ANY",icon:on.CODE},{id:"FULLY_COMPLETED",label:v.form_datasource_filter_task_status_fully_completed,value:"FULLY_COMPLETED",icon:on.CODE}],inputPlaceholder:v.form_datasource_filter_contains_tag_input_placeholder}):null,(0,te.jsx)("button",{className:"list-remove-button",onClick:R=>d(b.id),children:"x"})]},b.id)),(0,te.jsx)("div",{className:"form-content",children:(0,te.jsx)("button",{className:"list-add-button",onClick:b=>f(Date.now().toString()),children:"+"})})]})]}),(0,te.jsxs)("div",{className:"form-item",children:[(0,te.jsx)("span",{className:"label",children:v.form_date_field}),(0,te.jsxs)("div",{className:"form-content",children:[(0,te.jsx)("select",{defaultValue:((h=e.dateField)==null?void 0:h.type)||"FILE_CTIME",onChange:b=>{a("type",b.target.value)},children:SS(e.type).map(b=>(0,te.jsx)("option",{value:b.value,children:b.label},b.value))}),((E=e.dateField)==null?void 0:E.type)=="PAGE_PROPERTY"&&(0,te.jsx)(Yh,{defaultInputValue:(D=e.dateField)==null?void 0:D.value,onInputChange:b=>{a("value",b)},inputPlaceholder:v.form_date_field_placeholder,getItems:b=>$h(b,t.app).map((_,R)=>({id:_.name,value:_.name,label:_.name,icon:on.CODE,description:_.sampleValue||""})),onSelected:b=>{a("value",b.value)}}),((x=e.dateField)==null?void 0:x.type)=="TASK_PROPERTY"&&(0,te.jsx)("input",{type:"text",defaultValue:((O=e.dateField)==null?void 0:O.value)||"",placeholder:v.form_date_field_placeholder,onChange:b=>{a("value",b.target.value)}})]})]}),(0,te.jsxs)("div",{className:"form-item",children:[(0,te.jsx)("span",{className:"label",children:v.form_date_field_format}),(0,te.jsxs)("div",{className:"form-vertical-content",children:[(0,te.jsxs)("select",{defaultValue:n,onChange:b=>{r(b.target.value),b.target.value=="smart_detect"&&a("format",void 0)},children:[(0,te.jsx)("option",{value:"smart_detect",children:v.form_date_field_format_type_smart}),(0,te.jsx)("option",{value:"manual",children:v.form_date_field_format_type_manual})]}),n=="manual"?(0,te.jsxs)(te.Fragment,{children:[(0,te.jsx)("input",{type:"text",defaultValue:((F=e.dateField)==null?void 0:F.format)||"",name:"dateFieldFormat",placeholder:v.form_date_field_format_placeholder,onChange:b=>{a("format",b.target.value)}}),(0,te.jsxs)("div",{className:"form-description",children:[(0,te.jsx)("a",{href:"https://moment.github.io/luxon/#/formatting?id=table-of-tokens",children:"Luxon Format"})," "+v.form_date_field_format_sample,":"," "+$.fromJSDate(new Date("2024-01-01 00:00:00")).toFormat(((k=e.dateField)==null?void 0:k.format)||"yyyy-MM-dd'T'HH:mm:ss")]})]}):null]})]}),(0,te.jsxs)("div",{className:"form-item",children:[(0,te.jsx)("span",{className:"label",children:v.form_count_field_count_field_label}),(0,te.jsxs)("div",{className:"form-vertical-content",children:[(0,te.jsx)("select",{defaultValue:((P=e.countField)==null?void 0:P.type)||"DEFAULT",onChange:b=>{l("type",b.target.value)},children:wS(e.type).map(b=>(0,te.jsx)("option",{value:b.value,children:b.label},b.value))}),((M=e.countField)==null?void 0:M.type)=="PAGE_PROPERTY"||((ee=e.countField)==null?void 0:ee.type)=="TASK_PROPERTY"?(0,te.jsx)(Yh,{defaultInputValue:((oe=e.countField)==null?void 0:oe.value)||"",onInputChange:b=>{l("value",b)},inputPlaceholder:v.form_count_field_count_field_input_placeholder,getItems:b=>$h(b,t.app).map((_,R)=>({id:_.name,value:_.name,label:_.name,icon:on.CODE,description:_.sampleValue||""})),onSelected:b=>{l("value",b.value)}}):null]})]})]})}var OS=B(se());var wr=B(X());function kS(t){return(0,wr.jsxs)("div",{className:"contribution-graph-divider",children:[(0,wr.jsx)("div",{}),t.text&&(0,wr.jsxs)(wr.Fragment,{children:[(0,wr.jsx)("span",{children:t.text}),(0,wr.jsx)("div",{})]})]})}var Ut=B(X());function Df(t){let[e,n]=(0,OS.useState)(t.activeIndex||0);return(0,Ut.jsxs)("div",{className:"tab-container",children:[(0,Ut.jsx)("div",{className:"tab-titles",children:t.tabs.map((r,o)=>(0,Ut.jsx)(fO,{active:o==e,...r,onClick:()=>{var i;n(o),(i=r.onClick)==null||i.call(r)}},o))}),(0,Ut.jsx)(kS,{}),(0,Ut.jsx)("div",{className:"tab-items",children:t.tabs.map((r,o)=>(0,Ut.jsx)(dO,{title:r.title,icon:r.icon,active:o==e,onClick:()=>{var i;n(o),(i=r.onClick)==null||i.call(r)},children:r.children},o))})]})}function fO(t){let{title:e,icon:n,active:r}=t;return(0,Ut.jsxs)("div",{className:`tab-item-title ${r?"active":""}`,onClick:o=>{var i;return(i=t.onClick)==null?void 0:i.call(t)},children:[n&&(0,Ut.jsx)("span",{children:n}),(0,Ut.jsx)("span",{children:e})]})}function dO(t){let{children:e,active:n}=t;return(0,Ut.jsx)("div",{className:`tab-item ${n?"active":""}`,children:(0,Ut.jsx)("div",{className:"tab-item-content",children:e})})}var qh=require("obsidian"),vf=B(se());var RS=B(X()),mO=t=>{let[e,n]=(0,vf.useState)(t.defaultValue.toString()),r=q.get(),o=i=>{let s=i.target.value,a=/^-?\d*$/;if(s===""){n("");return}if(a.test(s)){if(t.min!==void 0&&Number(s)<t.min){new qh.Notice(r.form_number_input_min_warning.replace("{value}",t.min.toString())),n(t.min.toString());return}if(t.max!==void 0&&Number(s)>t.max){new qh.Notice(r.form_number_input_max_warning.replace("{value}",t.max.toString())),n(t.max.toString());return}n(s)}};return(0,vf.useEffect)(()=>{e==""?t.onChange(t.defaultValue):t.onChange(parseInt(e))},[e]),(0,RS.jsx)("input",{type:"text",value:e,onChange:o,placeholder:t.placeholder})},bS=mO;var NS=B(se()),On=B(X());function MS(t){var l;let[e,n]=(0,NS.useState)(!1),{refs:r,floatingStyles:o,context:i}=as({open:e,onOpenChange:u=>n(u),middleware:[Yn(6),gr(),vr(),Dr()],whileElementsMounted:ro}),s=ss(i),{getFloatingProps:a}=ls([s]);return(0,On.jsxs)(On.Fragment,{children:[(0,On.jsx)("span",{className:"color-indicator",style:{backgroundColor:t.color},onClick:()=>n(!e)}),t.color&&(0,On.jsxs)("div",{className:"color-label",children:[(0,On.jsx)("span",{children:(l=t.color)!=null?l:""}),(0,On.jsx)("span",{className:"color-reset-button",onClick:()=>{t.onReset?t.onReset(t.defaultColor):t.onChange(t.defaultColor)},children:"x"})]}),e?(0,On.jsx)("div",{ref:r.setFloating,style:{...o},...a(),children:(0,On.jsx)(yl,{color:t.color||"#FFFFFF",onChange:u=>{t.onChange(u.hexa)}})}):null]})}var Ze=B(X());function IS(){let t=q.get();return(0,Ze.jsxs)("div",{className:"about-container",children:[(0,Ze.jsxs)("div",{className:"about-item",children:[(0,Ze.jsx)("div",{className:"label",children:t.form_contact_me}),(0,Ze.jsx)("a",{href:"https://mp.weixin.qq.com/s/k5usslOZwWNFT5rlq3lAPA",children:"\u5FAE\u4FE1\u516C\u4F17\u53F7"}),(0,Ze.jsx)("a",{href:"https://github.com/vran-dev",children:"Github"})]}),(0,Ze.jsxs)("div",{className:"about-item",children:[(0,Ze.jsx)("div",{className:"label",children:t.form_project_url}),(0,Ze.jsx)("div",{children:(0,Ze.jsx)("a",{href:"https://github.com/vran-dev/obsidian-contribution-graph",children:"https://github.com/vran-dev/obsidian-contribution-graph"})})]}),(0,Ze.jsxs)("div",{className:"about-item",children:[(0,Ze.jsx)("div",{className:"label",children:t.form_sponsor}),(0,Ze.jsx)("div",{children:(0,Ze.jsx)(Df,{activeIndex:0,tabs:[{title:"\u5FAE\u4FE1",children:(0,Ze.jsx)("a",{href:"https://mp.weixin.qq.com/s/k5usslOZwWNFT5rlq3lAPA",children:(0,Ze.jsx)("img",{src:"https://s2.loli.net/2022/05/23/phDIKagHwjZl3kA.jpg"})})},{title:"Buy me a coffee",children:(0,Ze.jsx)("a",{href:"https://www.buymeacoffee.com/vran",children:(0,Ze.jsx)("img",{src:"https://cdn.buymeacoffee.com/buttons/v2/default-yellow.png"})})}]})})]})]})}var A=B(X());function AS(t){var h,E,D,x,O,F,k,P,M,ee,oe,b;let{yamlConfig:e}=t,n=q.get(),r=(0,El.useRef)(null),[o,i]=(0,El.useState)(e),[s,a]=(0,El.useState)(e.cellStyleRules||[]),l=_=>{let{name:R,value:W}=_.target;m(R,W)},u=_=>{let{value:R}=_.target,W=Hh.find(Q=>Q.name==R);W&&(m("cellStyleRules",W.rules),a(W.rules))},f=_=>{let{value:R}=_.target;m("cellStyle",{...o.cellStyle,borderRadius:R})},d=()=>{var _;return o.cellStyle&&o.cellStyle.borderRadius&&((_=jh.find(R=>{var W;return R.value==((W=o.cellStyle)==null?void 0:W.borderRadius)}))==null?void 0:_.value)||""},m=(_,R)=>{i(W=>({...W,[_]:R}))},c=()=>{let _={id:new Date().getTime(),min:1,max:2,color:"#63aa82",text:""};a([...s,_])},y=()=>{o.cellStyleRules=s,t.onSubmit(o)},v=()=>{if(r.current){r.current.empty();let _=new xi,R=JSON.parse(JSON.stringify(o));R.cellStyleRules=s,_.renderFromYaml(R,r.current,t.app)}},S=()=>{if(o.titleStyle&&o.titleStyle.fontSize){let _=o.titleStyle.fontSize;return parseInt(_.replace(/[^0-9]/,""))}return 16},p=(_,R)=>{if(!_)return 0;let W=_.replace(/[^0-9]/,"")||"0";return parseInt(W)};return(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)(Df,{activeIndex:0,tabs:[{title:n.form_basic_settings,children:(0,A.jsx)("div",{className:"contribution-graph-modal-form",children:(0,A.jsxs)("div",{className:"form-group",children:[(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_title}),(0,A.jsxs)("div",{className:"form-content",children:[(0,A.jsx)("input",{name:"title",type:"text",defaultValue:o.title,placeholder:n.form_title_placeholder,onChange:l,style:{...o.titleStyle,fontSize:"inherits",fontWeight:((h=o.titleStyle)==null?void 0:h.fontWeight)||"normal",textAlign:((E=o.titleStyle)==null?void 0:E.textAlign)||"left"}}),(0,A.jsx)($w,{options:DS,defaultValue:((D=o.titleStyle)==null?void 0:D.textAlign)||"left",onChoose:_=>{m("titleStyle",{...o.titleStyle,textAlign:_.value})}})]})]}),(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_graph_type}),(0,A.jsx)("div",{className:"form-content",children:(0,A.jsx)("select",{name:"graphType",defaultValue:o.graphType||((x=zh.find(_=>_.selected))==null?void 0:x.value),onChange:l,children:zh.map(_=>(0,A.jsx)("option",{value:_.value,children:_.label},_.value))})})]}),(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_date_range}),(0,A.jsx)("div",{className:"form-content",children:(0,A.jsx)("select",{defaultValue:o.dateRangeType||"LATEST_DAYS",onChange:_=>{m("dateRangeType",_.target.value),_.target.type!="FIXED_DATE_RANGE"?(m("fromDate",void 0),m("toDate",void 0)):m("dateRangeValue",void 0)},children:xS.map(_=>(0,A.jsx)("option",{value:_.value,children:_.label},_.value))})})]}),(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label"}),(0,A.jsx)("div",{className:"form-content",children:o.dateRangeType!="FIXED_DATE_RANGE"?(0,A.jsx)(A.Fragment,{children:(0,A.jsx)("input",{type:"number",defaultValue:o.dateRangeValue,min:1,placeholder:n.form_date_range_input_placeholder,onChange:_=>m("dateRangeValue",parseInt(_.target.value))})}):(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("input",{id:"fromDate",name:"fromDate",type:"date",defaultValue:o.fromDate,placeholder:"from date, such as 2023-01-01",onChange:l}),"\xA0-\xA0",(0,A.jsx)("input",{id:"toDate",name:"toDate",type:"date",defaultValue:o.toDate,placeholder:"to date, such as 2023-12-31",onChange:l})]})})]}),(0,A.jsx)(_S,{dataSource:o.dataSource,onChange:_=>{m("dataSource",_)},app:t.app})]})})},{title:n.form_style_settings,children:(0,A.jsx)("div",{className:"contribution-graph-modal-form",children:(0,A.jsxs)("div",{className:"form-group",children:[(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_title_font_size_label}),(0,A.jsx)("div",{className:"form-content",children:(0,A.jsx)(bS,{defaultValue:S(),onChange:_=>{m("titleStyle",{...o.titleStyle,fontSize:_+"px"})},min:1,max:128})})]}),(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_fill_the_screen_label}),(0,A.jsx)("div",{className:"form-content",children:(0,A.jsx)("input",{type:"checkbox",className:"checkbox",defaultChecked:o.fillTheScreen,onChange:()=>m("fillTheScreen",!o.fillTheScreen)})})]}),o.graphType=="month-track"?null:(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_start_of_week}),(0,A.jsx)("div",{className:"form-content",children:(0,A.jsx)("select",{id:"startOfWeek",name:"startOfWeek",defaultValue:o.startOfWeek!=null?o.startOfWeek:(O=Uh.find(_=>_.selected))==null?void 0:O.value,onChange:l,children:Uh.map(_=>(0,A.jsx)("option",{value:_.value,children:_.label},_.value))})})]}),(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_main_container_bg_color}),(0,A.jsx)("div",{className:"form-content",children:(0,A.jsx)(MS,{color:(F=o.mainContainerStyle)==null?void 0:F.backgroundColor,onChange:_=>{m("mainContainerStyle",{...o.mainContainerStyle,backgroundColor:_})},onReset:_=>{m("mainContainerStyle",{...o.mainContainerStyle,backgroundColor:_})}})})]}),(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_enable_main_container_shadow}),(0,A.jsx)("div",{className:"form-content",children:(0,A.jsx)("input",{name:"enableMainContainerShadow",type:"checkbox",className:"checkbox",defaultChecked:o.enableMainContainerShadow,onChange:_=>{_.target.checked?m("enableMainContainerShadow",!0):m("enableMainContainerShadow",!1)}})})]}),(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_show_cell_indicators}),(0,A.jsx)("div",{className:"form-content",children:(0,A.jsx)("input",{name:"showCellRuleIndicators",type:"checkbox",className:"checkbox",defaultChecked:o.showCellRuleIndicators,onChange:()=>m("showCellRuleIndicators",!o.showCellRuleIndicators)})})]}),(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_cell_shape}),(0,A.jsx)("div",{className:"form-content",children:(0,A.jsx)("select",{name:"cellShape",defaultValue:d(),onChange:f,children:jh.map(_=>(0,A.jsx)("option",{value:_.value,children:_.label},_.label))})})]}),(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_cell_min_width}),(0,A.jsxs)("div",{className:"form-content",children:[(0,A.jsx)("input",{type:"range",min:4,max:64,defaultValue:p((k=o.cellStyle)==null?void 0:k.minWidth,8),onChange:_=>{m("cellStyle",{...o.cellStyle,minWidth:_.target.value+"px"})}}),(0,A.jsx)("span",{className:"input-range-value-label",onClick:_=>{m("cellStyle",{...o.cellStyle,minWidth:void 0})},children:(P=o.cellStyle)!=null&&P.minWidth?(M=o.cellStyle)==null?void 0:M.minWidth:n.default})]})]}),(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_cell_min_height}),(0,A.jsxs)("div",{className:"form-content",children:[(0,A.jsx)("input",{type:"range",min:4,max:64,defaultValue:p((ee=o.cellStyle)==null?void 0:ee.minHeight,8),onChange:_=>{m("cellStyle",{...o.cellStyle,minHeight:_.target.value+"px"})}}),(0,A.jsx)("span",{className:"input-range-value-label",onClick:_=>{m("cellStyle",{...o.cellStyle,minHeight:void 0})},children:(oe=o.cellStyle)!=null&&oe.minHeight?(b=o.cellStyle)==null?void 0:b.minHeight:n.default})]})]}),(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_theme}),(0,A.jsx)("div",{className:"form-content",children:(0,A.jsx)("select",{name:"theme","aria-placeholder":"select theme to generate style",onChange:u,children:Hh.map(_=>(0,A.jsx)("option",{value:_.name,children:_.name},_.name))})})]}),(0,A.jsxs)("div",{className:"form-item",children:[(0,A.jsx)("span",{className:"label",children:n.form_cell_style_rules}),(0,A.jsxs)("div",{className:"form-vertical-content",children:[s.map(_=>(0,A.jsx)(gS,{rule:_,onChange:R=>{let W=s.map(Q=>Q.id==R.id?R:Q);a(W)},onRemove:R=>{let W=s.filter(Q=>Q.id!=R);a(W)}},_.id)),(0,A.jsx)("button",{onClick:()=>c(),className:"list-add-button",children:"+"})]})]})]})})},{title:n.form_about,children:(0,A.jsx)(IS,{})}]}),(0,A.jsxs)("div",{className:"contribution-graph-modal-form",children:[(0,A.jsx)("div",{className:"preview-container",ref:r}),(0,A.jsx)("div",{className:"form-item",children:(0,A.jsxs)("div",{className:"form-content",children:[(0,A.jsx)("button",{className:"button",onClick:v,children:n.form_button_preview}),(0,A.jsx)("button",{className:"button",onClick:y,children:n.form_button_save})]})})]})]})}var Kh=B(X()),Uo=class extends sn.Modal{constructor(n,r,o){super(n);this.root=null;this.originalConfigContent=r,this.onSave=o}async onOpen(){let{contentEl:n}=this,r=createDiv({parent:n}),o,i=!1;this.originalConfigContent?o=this.parseFromOriginalConfig():(o=this.parseFromSelecttion(),o&&(i=!0)),o||(o=new So);let s;this.onSave?s=a=>{this.close(),this.onSave((0,sn.stringifyYaml)(a))}:s=a=>{let l=this.app.workspace.getActiveViewOfType(sn.MarkdownView);if(!l)return;let u=l.editor;if(this.close(),i)u.replaceSelection((0,sn.stringifyYaml)(a));else{let f=`\`\`\`contributionGraph
${(0,sn.stringifyYaml)(a)}
\`\`\`
`;u.replaceSelection(f)}},o=Mr.reconcile(o),this.root=(0,PS.createRoot)(r),this.root.render((0,Kh.jsx)(LS.StrictMode,{children:(0,Kh.jsx)(AS,{yamlConfig:o,onSubmit:s,app:this.app})}))}async onClose(){var r;(r=this.root)==null||r.unmount();let{contentEl:n}=this;n.empty()}parseFromOriginalConfig(){if(this.originalConfigContent&&this.originalConfigContent.trim()!="")try{return(0,sn.parseYaml)(this.originalConfigContent)}catch(n){return null}else return null}parseFromSelecttion(){let n=this.app.workspace.getActiveViewOfType(sn.MarkdownView);if(!n)return null;let o=n.editor.getSelection();if(o&&o.trim()!="")try{return(0,sn.parseYaml)(o)}catch(i){return null}else return null}};var jo=require("obsidian");function BS(t,e,n){let r=document.createElement("div");r.className="contribution-graph-codeblock-edit-button";let o=(0,jo.getIcon)("gantt-chart");return o&&r.appendChild(o),n.addEventListener("mouseover",()=>{let i=t.workspace.getActiveViewOfType(jo.MarkdownView);i&&i.getMode()!=="preview"&&(r.style.opacity="1",pO(n,r))}),n.addEventListener("mouseout",()=>{r.style.opacity="0"}),r.onclick=()=>{new Uo(this.app,e,i=>{let s=this.app.workspace.getActiveViewOfType(jo.MarkdownView);if(!s){new jo.Notice("No markdown view is active");return}let l=s.editor.cm,f=l.posAtDOM(n)+21;l.dispatch({changes:{from:f,to:f+(e?e.length:0),insert:i}})}).open()},n.appendChild(r),r}function pO(t,e){var o;let n=t.getElementsByClassName("edit-block-button"),r;n.length>0&&(r=(o=n[0].computedStyleMap().get("top"))==null?void 0:o.toString()),r?e.style.top=r:e.style.top="0"}var Ef=class extends VS.Plugin{async onload(){this.registerGlobalRenderApi(),this.registerCodeblockProcessor(),this.registerContributionGraphCreateCommand(),this.registerContextMenu()}onunload(){window.renderContributionGraph=void 0}registerContextMenu(){this.registerEvent(this.app.workspace.on("editor-menu",(e,n,r)=>{e.addItem(o=>{o.setTitle(q.get().context_menu_create),o.setIcon("gantt-chart"),o.onClick(()=>{new Uo(this.app).open()})})}))}registerGlobalRenderApi(){window.renderContributionGraph=(e,n)=>{mn.render(e,n)}}registerCodeblockProcessor(){this.registerMarkdownCodeBlockProcessor("contributionGraph",(e,n,r)=>{new xi().renderFromCodeBlock(e,n,r,this.app),n.parentElement&&BS(this.app,e,n.parentElement)})}registerContributionGraphCreateCommand(){this.addCommand({id:"create-graph",name:q.get().context_menu_create,editorCallback:(e,n)=>{new Uo(this.app).open()}})}};
/*! Bundled license information:

react/cjs/react.production.min.js:
  (**
   * @license React
   * react.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

scheduler/cjs/scheduler.production.min.js:
  (**
   * @license React
   * scheduler.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-dom/cjs/react-dom.production.min.js:
  (**
   * @license React
   * react-dom.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react/cjs/react-jsx-runtime.production.min.js:
  (**
   * @license React
   * react-jsx-runtime.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/

/* nosourcemap */