---
date: 2025-07-01
---
# 本月回顾 2025-07

## 本月日记列表
```dataview
TABLE file.day AS "日期", file.tags AS "标签"
FROM "04-Periodic/Dailies"
WHERE startswith(file.name, substring(this.file.name, 0, 7))
SORT file.day ASC
```

## 本月高亮时刻
> 这里可以手动填写总结

## 本月展讯

| 展览名称              | 日期          | 地点                                | 意愿  |
| :---------------- | :---------- | :-------------------------------- | --- |
| 希腊人：从阿伽门农到亚历山大    | 7.9 - 10.26 | 世博会博物馆 蒙自路818号                    | 5   |
| 华彩六盘：宁夏固原文物精品展    | 7.9 - 11.17 | 上海博物馆东馆 人民大道201号                  | 5   |
| 天行意动·第三届中国国际动态雕塑展 | 7.6 - 8.31  | 中华艺术宫 0米层17号                      | 0   |
| 肢体的密码             | 7.5 - 8.31  | 璃空间 北苏州路1040号                     | 0   |
| 黎家齐：所见与所得         | 7.05 - 8.16 | 工作室画廊 长乐路888号                     | 0   |
| 灰烬里的金             | 6.28 - 8.17 | SIGMA 番禺路222弄 上生新所2期16号楼102室      | 0   |
| 地球记得所有事物          | 7.12 - 8.24 | 池社 龙腾大道2555-1号                    | 0   |
| 如果褶皱可以颤动屋脊        | 7.5 - 9.5   | Third Street Gallery 曲阜路9弄 B1-09号 | 0   |
| 蝉鸣之隙              | 7.5 - 8.16  | 胶囊画廊 安福路275弄16号1层                 | 0   |
| 何处有夏：应晶晶个展        | 7.9 - 8.31  | 文Azusa堂 建国西路619号茂龄别墅14号           | 0   |
| 董金玲：破河谷           | 6.22 - 8.21 | CHAPTER6 安福路228弄6号                | 0   |
| 玉山白雪飘零            | 6.28 - 8.28 | 周空间 余庆路41弄2号楼                     | 0   |
| Stay with Me      | 7.12 - 9.11 | 八大画廊 莫干山路50号4号楼101                | 0   |
| 弗弗FotoFleek摄影展    | 7.11 - 7.20 | E.SCAPE艺术空间 武康路40弄3号              | 0   |
| 视界——青年艺术家联展第二季    | 7.5 - 8.10  | 芊荷艺术空间 愚园路716号101室                | 0   |
| 容器                | 6.28 - 8.03 | 狮语画廊 武康路376号武康庭内三层                | 0   |
| 牟雪：那里有一个花园        | 7.10 - 8.30 | 香格纳M50 莫干山路50号16号楼                | 0   |

## 本月任务分析

### 📊 任务概览统计
```dataviewjs
// 获取本月所有任务
let currentMonth = dv.current().file.day.month;
let currentYear = dv.current().file.day.year;

let allTasks = dv.pages("#daily")
    .file.tasks
    .where(t => {
        try {
            if (!t.path) return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return taskDate.month === currentMonth && taskDate.year === currentYear;
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

// 统计任务状态
let completedCount = allTasks.where(t => t.completed).length;
let abandonedCount = allTasks.where(t => t.status === "-").length;
let inProgressCount = allTasks.where(t => t.status === "/").length;
let uncompletedCount = allTasks.where(t => !t.completed && t.status !== "-" && t.status !== "/").length;
let totalCount = allTasks.length;

// 计算完成率（不包括已放弃的任务）
let effectiveTotal = totalCount - abandonedCount;
let completionRate = effectiveTotal > 0 ? Math.round((completedCount / effectiveTotal) * 100) : 0;

// 创建统计表格
dv.table(["状态", "数量", "占比"], [
    ["✅ 已完成", completedCount, effectiveTotal > 0 ? `${Math.round((completedCount / effectiveTotal) * 100)}%` : "0%"],
    ["🔄 进行中", inProgressCount, effectiveTotal > 0 ? `${Math.round((inProgressCount / effectiveTotal) * 100)}%` : "0%"],
    ["📝 未完成", uncompletedCount, effectiveTotal > 0 ? `${Math.round((uncompletedCount / effectiveTotal) * 100)}%` : "0%"],
    ["❌ 已放弃", abandonedCount, totalCount > 0 ? `${Math.round((abandonedCount / totalCount) * 100)}%` : "0%"],
    ["📊 总计", totalCount, "100%"],
    ["🎯 完成率", `${completionRate}%`, "(不含已放弃任务)"]
]);
```

### 📋 按状态分类的任务
```dataview
TASK
FROM "04-Periodic/Dailies"
WHERE startswith(file.name, substring(this.file.name, 0, 7))
GROUP BY completed
```

### ❌ 本月已放弃任务分析
```dataviewjs
// 获取本月已放弃的任务
let currentMonth = dv.current().file.day.month;
let currentYear = dv.current().file.day.year;

let abandonedTasks = dv.pages("#daily")
    .file.tasks
    .where(t => {
        try {
            if (!t.path || t.status !== "-") return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return taskDate.month === currentMonth && taskDate.year === currentYear;
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

if (abandonedTasks.length > 0) {
    // 分析放弃原因
    let reasonStats = {};
    let abandonedData = [];

    for (let task of abandonedTasks) {
        let taskText = task.text;

        // 提取放弃原因
        let reasonMatch = taskText.match(/\[reason::\s*([^\]]+)\]/);
        let reason = reasonMatch ? reasonMatch[1] : "未指定原因";

        // 提取放弃日期
        let abandonedMatch = taskText.match(/\[abandoned::\s*([^\]]+)\]/);
        let abandonedDate = abandonedMatch ? abandonedMatch[1] : "未知日期";

        // 提取预估时间
        let estMatch = taskText.match(/\[est::\s*([^\]]+)\]/);
        let estimatedTime = estMatch ? estMatch[1] : "";

        // 清理任务文本
        let cleanText = taskText
            .replace(/\[reason::[^\]]*\]/g, '')
            .replace(/\[abandoned::[^\]]*\]/g, '')
            .replace(/\[est::[^\]]*\]/g, '')
            .replace(/\[act::[^\]]*\]/g, '')
            .trim();

        // 统计原因
        if (!reasonStats[reason]) {
            reasonStats[reason] = 0;
        }
        reasonStats[reason]++;

        // 获取任务所在日期
        let taskDate = "未知";
        try {
            if (task.path) {
                const page = dv.page(task.path);
                if (page && page.file.day) {
                    taskDate = dv.date(page.file.day).toFormat("MM-dd");
                }
            }
        } catch (error) {
            taskDate = "格式错误";
        }

        abandonedData.push([cleanText, reason, estimatedTime, taskDate]);
    }

    // 显示放弃原因统计
    dv.header(4, "放弃原因统计");
    let reasonTable = Object.entries(reasonStats)
        .sort((a, b) => b[1] - a[1])
        .map(([reason, count]) => [reason, count, `${Math.round((count / abandonedTasks.length) * 100)}%`]);

    dv.table(["放弃原因", "数量", "占比"], reasonTable);

    // 显示详细的已放弃任务列表
    dv.header(4, "已放弃任务详情");
    dv.table(["任务", "放弃原因", "预估时间", "日期"], abandonedData);

} else {
    dv.paragraph("✅ 本月没有已放弃的任务。");
}
```

### 本月时间投入分析

#### 详细列表
```dataviewjs
// 获取本月所有包含时间跟踪的已完成任务
let currentMonth = dv.current().file.day.month;
let currentYear = dv.current().file.day.year;

let tasks = dv.pages("#daily")
    .file.tasks
    .where(t => t.completed && t.text.includes("[act::") && t.completion)
    .where(t => {
        try {
            const completionDate = dv.date(t.completion);
            return completionDate.month === currentMonth && completionDate.year === currentYear;
        } catch (error) {
            console.error("日期解析错误:", error, t.completion);
            return false;
        }
    });

// 创建表格数据
let tableData = tasks.map(task => {
    let text = task.text;

    // 简单的文本清理
    let cleanText = text.replace(/\[est::[^\]]*\]/g, '')
                       .replace(/\[act::[^\]]*\]/g, '')
                       .replace(/✅.*$/g, '')
                       .trim();

    // 提取时间信息
    let actMatch = text.match(/\[act::\s*([^\]]+)\]/);
    let estMatch = text.match(/\[est::\s*([^\]]+)\]/);

    // 安全的日期处理
    let completionDate = "未知";
    if (task.completion) {
        try {
            completionDate = dv.date(task.completion).toFormat("MM-dd");
        } catch (error) {
            console.error("日期格式化错误:", error);
            completionDate = "格式错误";
        }
    }

    return [
        cleanText,
        actMatch ? actMatch[1] : "",
        estMatch ? estMatch[1] : "",
        completionDate
    ];
}).filter(row => row[0] && row[0].trim() !== ""); // 过滤空任务

// 安全的排序实现
tableData.sort((a, b) => {
    const dateA = a[3] || "";
    const dateB = b[3] || "";
    return dateB.toString().localeCompare(dateA.toString());
});

dv.table(["任务", "实际耗时", "预估耗时", "日期"], tableData);
```

#### 本月总结
```dataviewjs
// 获取本月已完成的带时间跟踪的任务
let currentMonth = dv.current().file.day.month;
let currentYear = dv.current().file.day.year;

let completedTasks = dv.pages("#daily")
    .file.tasks
    .where(t => t.completed && t.text.includes("[act::") && t.completion)
    .where(t => {
        try {
            const completionDate = dv.date(t.completion);
            return completionDate.month === currentMonth && completionDate.year === currentYear;
        } catch (error) {
            console.error("日期解析错误:", error, t.completion);
            return false;
        }
    });

// 获取本月所有任务统计
let allMonthTasks = dv.pages("#daily")
    .file.tasks
    .where(t => {
        try {
            if (!t.path) return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return taskDate.month === currentMonth && taskDate.year === currentYear;
        } catch (error) {
            return false;
        }
    });

let totalTasks = allMonthTasks.length;
let completedCount = allMonthTasks.where(t => t.completed).length;
let abandonedCount = allMonthTasks.where(t => t.status === "-").length;
let effectiveTotal = totalTasks - abandonedCount;
let completionRate = effectiveTotal > 0 ? Math.round((completedCount / effectiveTotal) * 100) : 0;

// 创建总结表格
dv.table(["指标", "数值"], [
    ["本月总任务数", totalTasks],
    ["已完成任务数", completedCount],
    ["已放弃任务数", abandonedCount],
    ["有效任务数", effectiveTotal],
    ["完成率", `${completionRate}%`],
    ["带时间跟踪的完成任务", completedTasks.length]
]);
```