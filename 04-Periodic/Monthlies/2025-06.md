---
date: 2025-06-26
---
# 本月回顾

## 本月日记列表
```dataview
TABLE file.day AS "日期", file.tags AS "标签"
FROM #daily
WHERE file.day AND date(file.day).month = this.file.day.month AND date(file.day).year = this.file.day.year
SORT file.day ASC
```

## 本月高亮时刻
> 这里可以手动填写总结

## 本月所有任务
```dataview
TASK
FROM #daily
WHERE file.day AND date(file.day).month = this.file.day.month AND date(file.day).year = this.file.day.year
GROUP BY completed
```

### 本月时间投入分析

#### 详细列表
```dataviewjs
// 获取所有包含时间跟踪的已完成任务
let tasks = dv.pages("#daily")
    .file.tasks
    .where(t => t.completed && t.text.includes("[act::") && t.completion)
    .where(t => {
        try {
            const completionDate = dv.date(t.completion);
            return completionDate.month === 6 && completionDate.year === 2025;
        } catch (error) {
            console.error("日期解析错误:", error, t.completion);
            return false;
        }
    });

// 创建表格数据
let tableData = tasks.map(task => {
    let text = task.text;

    // 简单的文本清理
    let cleanText = text.replace(/\[est::[^\]]*\]/g, '')
                       .replace(/\[act::[^\]]*\]/g, '')
                       .replace(/✅.*$/g, '')
                       .trim();

    // 提取时间信息
    let actMatch = text.match(/\[act::\s*([^\]]+)\]/);
    let estMatch = text.match(/\[est::\s*([^\]]+)\]/);

    // 安全的日期处理
    let completionDate = "未知";
    if (task.completion) {
        try {
            completionDate = dv.date(task.completion).toFormat("MM-dd");
        } catch (error) {
            console.error("日期格式化错误:", error);
            completionDate = "格式错误";
        }
    }

    return [
        cleanText,
        actMatch ? actMatch[1] : "",
        estMatch ? estMatch[1] : "",
        completionDate
    ];
}).filter(row => row[0] && row[0].trim() !== ""); // 过滤空任务

dv.table(["任务", "实际耗时", "预估耗时", "日期"], tableData);
```

#### 本月总结
```dataviewjs
// 获取本月已完成的带时间跟踪的任务
let completedTasks = dv.pages("#daily")
    .file.tasks
    .where(t => t.completed && t.text.includes("[act::") && t.completion)
    .where(t => {
        try {
            const completionDate = dv.date(t.completion);
            return completionDate.month === 6 && completionDate.year === 2025;
        } catch (error) {
            console.error("日期解析错误:", error, t.completion);
            return false;
        }
    });

dv.paragraph(`**本月已完成任务数**: ${completedTasks.length}`);
```