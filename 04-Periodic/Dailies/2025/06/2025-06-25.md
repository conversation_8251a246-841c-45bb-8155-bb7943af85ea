---
tags: daily
date: 2025-06-25
---

# 每日笔记 2025-06-25

## 任务列表

## 今日记录
### 种子项目
1. 服务治理（锻炼讲故事的好苗子）
	1. 通过腾讯云日志分析和自建skywalking 生成性能瓶颈调研结果
	2. 发现循环调用，拉产品和客服介入，讨论出方案
	3. 代码修改，引入 redis（？）技术
	4. 数据支撑：rui 在群里和周会有分享，这里注意贴上来
2. APP 双 token 鉴权改造，风险账号验证系统
	1. 这个使用到的技术和逻辑我比较清楚
	2. 待深挖业务价值
	3. 待对比业内实现标准
	4. 数据支撑：全员站内信里法务有
3. 营销管理平台系统，涉及复杂的任务调度和批处理，还有任务补偿，定时报表
	1. 技术栈：redis（记录规则）+ mq（解耦业务+削峰）+ 自实现的优先级队列（解决有的任务需要优先被处理的问题），没有使用规则引擎，有的技术点可能因为不熟悉所以觉得难，但是可能不适合写出来？写出来的一定要自己讲的清楚，不然很败好感，比如 mysql 里配置的数据，怎么反序列化？ 
	2. 需要哪些信息来证明这个系统“很厉害”？解决了业务的哪些问题，技术上有哪些突破？
	3. 这个其实可以和业内通用设计做对比（我们的项目做了大量的魔改，需要筛选哪些说出来能证明技术，哪些说出来会被别人认为草台班子）
4. 拼团项目，最适合包装成高并发
5. 呱呱小屋成就系统，作为复杂业务设计的备选（实际业务很简单，看一下能不能包装后，提升难度）
6. 首页改版，实际是业务背景很复杂，技术很简单，收益也不好（这个看怎么包装）

## 回顾
- 开始整理项目