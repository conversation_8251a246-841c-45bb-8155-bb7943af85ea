---
tags: daily
date: 2025-07-04
---

# 每日笔记 2025-07-04

## 任务列表


## 今日记录
如果继续用 ai 做计划，又会进入计划-部分行动超出能力范围-行动瘫痪-再计划的误区。
今天采取每件事规定时间完成的策略，时间一到记录阶段性结果，开始时间片轮转，挽救时间盲导致的“无限 DDL”幻觉。

> [!warn]  警告
> 不要深陷到细节里。

我要做的事有两个主线：
1. 梳理项目，目标是填充到简历和面试准备。我要做的工作是整理文档、源码、证明结果的基本数据。面临的风险是，似乎总有无限的数据我没有办法梳理出来，并且每个项目梳理下来的时间很长，今天必须明确下来，数据的优先级和可获得性。
	1. 服务治理
		1. 核心服务代码仓库
			1. 主要业务服务
			2. 支撑服务
			3. 其他相关服务
	2. 双 token 鉴权
	3. 风险账号验证
	4. APP 签名
	5. 拼团活动
	6. 营销管理平台
	7. 学习 tab 首页改造
	8. 交易重构 https://jiliguala.feishu.cn/drive/folder/fldcn9Y1c46QuNiP2WahdTSp00d?from=space_personal_filelist
	9. 东东分享过的迁移文档 https://jiliguala.feishu.cn/wiki/ZTF3wxNbDiapmaknbuTc0BSEnHf
	10. 购买 tab 重构 https://jiliguala.feishu.cn/wiki/T9sDwJtXUiXuFDkCksmcBIUlnLe
	11. 资产账户体系 https://jiliguala.feishu.cn/wiki/VX45wJCcniw8b8kYdYFc6Ce9nIg

2. 保存日后工作能参考的文档，学习设计思路和扩充业务领域经验。例如「交易重构」。这里需要合理管控预期，不要指望能从一篇文档发散出大而全的设计思路，不要深陷到细节里。

### 数据收集注意事项
1. 代码仓库集中放到一起，在项目文档文件夹存放代码库清单即可。
2. apollo 配置文件，线上环境不可得
3. 数据库脚本，一般都在文档里
4. 部署文件（并非主要内容），运维负责，可以尝试拉取某个服务的某个配置看一下
5. 技术文档，done
6. 监控和日志
	1. 腾讯云日志：服务调用链路日志
	2. 性能监控：CPU、内存、QPS 等指标
	3. 链路追踪：traceId 相关查询（这里具体指的是什么）
	4. 告警配置：服务健康检测配置（这里可能是对应 K8s的检测内容？）我们可以跳过
7. 测试数据，这里也是知识盲区，可能要梳理 or 编造压测数据相关

### 操作步骤
- [ ] 7,8,9,10 项目的文档 download
- [ ] 1-10项目代码 download & 清单梳理
- [ ] 1-10 监控和日志保存和截图

## 5天紧急技术资源抢救计划 ⏰

> [!important] 核心原则
> 5天后离职，失去所有内部资源访问权限。必须在时间窗口内完成关键技术资源的获取，避免"无限延伸"陷阱。

### 项目优先级重新排序

#### 第一层：核心项目（必须获取完整技术细节）⭐⭐⭐⭐⭐
1. **服务治理** - 主导角色，完整面试故事，架构能力展现
2. **双token鉴权** - 安全架构，技术深度体现

#### 第二层：支撑项目（需要关键技术证据）⭐⭐⭐⭐
3. **交易重构** - 分布式事务，核心业务架构
4. **风险账号验证** - 风控算法，业务理解
5. **资产账户体系** - 账户模型，数据一致性

#### 第三层：补充项目（仅需基础信息）⭐⭐
6. APP签名 7. 购买tab重构 8. 学习tab首页改造 9. 拼团活动 10. 营销管理平台 11. 迁移文档

### 5天时间盒执行计划

#### Day 1（今日）：核心项目技术抢救 ⏰
**上午（3小时）：服务治理项目**
- [x] 下载核心服务代码：`systemlesson`、`portrait.biz`、`userbiz` ✅ 2025-07-04
- [x] 截图监控大盘：QPS、响应时间、调用链路 ✅ 2025-07-04
- [x] 导出Apollo配置：数据库、Redis、Feign配置 ✅ 2025-07-04

**下午（3小时）：双token鉴权项目**
- [x] 获取认证服务相关代码 ✅ 2025-07-04
- [x] 收集JWT配置和安全策略文档 ✅ 2025-07-04
- [x] 截图认证流程的监控数据 ✅ 2025-07-04

**晚上（1小时）：成果整理**
- [x] 按项目分类保存技术资源 ✅ 2025-07-04
- [x] 记录明日重点获取清单 ✅ 2025-07-04

#### Day 2：支撑项目文档获取 ⏰
**上午（4小时）：**
- [ ] 交易重构文档：https://jiliguala.feishu.cn/drive/folder/fldcn9Y1c46QuNiP2WahdTSp00d
- [ ] 资产账户体系文档：https://jiliguala.feishu.cn/wiki/VX45wJCcniw8b8kYdYFc6Ce9nIg

**下午（3小时）：**
- [ ] 服务治理项目补充：K8s配置、CI/CD流水线
- [ ] 风险账号验证：风控代码和算法逻辑

#### Day 3：监控部署知识突击 ⏰
**上午（3小时）：监控体系学习**
- [ ] 腾讯云监控大盘完整截图和配置
- [ ] 链路追踪实际应用案例
- [ ] 告警规则和故障处理流程

**下午（4小时）：**
- [ ] K8s集群配置和部署文件
- [ ] 补充项目快速获取：购买tab重构、迁移文档

#### Day 4：技术证据整理 ⏰
**上午（3小时）：**
- [ ] 按11个项目分类整理技术资源
- [ ] 制作技术亮点清单和代码片段

**下午（3小时）：剩余项目处理**
- [ ] APP签名、拼团活动、营销管理平台、学习tab首页改造
- [ ] 每个项目30-45分钟，获取核心技术点

#### Day 5：最终整理 ⏰
**上午（4小时）：**
- [ ] 技术细节速查手册制作
- [ ] 面试故事基于真实数据调整

**下午（2小时）：离职准备**
- [ ] 确保技术资源完整备份
- [ ] 制作离职后学习计划

### 关键技术资源获取清单

#### 第一层项目（完整技术细节）
**服务治理：**
- [ ] 核心服务完整代码
- [ ] 监控大盘截图和配置
- [ ] Apollo配置文件
- [ ] 架构图和服务依赖关系
- [ ] 性能优化前后对比数据

**双token鉴权：**
- [ ] 认证服务代码
- [ ] JWT配置和密钥管理
- [ ] 安全策略文档

#### 第二层项目（关键技术证据）
- [ ] 交易重构：完整技术文档 + 核心代码片段
- [ ] 风险账号验证：风控算法 + 业务规则配置
- [ ] 资产账户体系：账户模型 + 数据一致性方案

#### 第三层项目（基础信息）
- [ ] 项目背景和业务价值
- [ ] 核心技术栈
- [ ] 个人贡献点

### 风险控制措施

**资源获取失败备选方案：**
- 代码获取失败 → 通过技术文档推导核心实现
- 监控数据获取失败 → 基于服务规模合理估算
- 配置文件获取失败 → 重点准备架构设计思路

**时间不足应对策略：**
- 进度落后 → 立即砍掉第三层项目
- 核心项目困难 → 增加第二层项目投入
- 保证至少2个项目有完整技术细节支撑

> [!warning] 硬性原则
> 每个时间盒到时间立即停止，不允许延长。80%完成度即可进入下一阶段。


## 回顾
拿不拿结婚了啊，把别人做为幻想对象，无异于在废墟上前进。