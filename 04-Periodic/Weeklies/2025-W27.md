# 本周回顾 2025-W27

## 本周日记列表
```dataviewjs
// 获取当前周报的日期信息
let currentDate = dv.current().file.day || dv.date("today");

// 计算本周的日期范围（周一到周日）
let currentDayOfWeek = currentDate.weekday; // 1=周一, 7=周日
let mondayOffset = currentDayOfWeek - 1; // 距离周一的天数
let weekStart = currentDate.minus({days: mondayOffset}); // 本周周一
let weekEnd = weekStart.plus({days: 6}); // 本周周日

dv.paragraph(`**本周范围**: ${weekStart.toFormat("MM-dd")} (周一) 到 ${weekEnd.toFormat("MM-dd")} (周日)`);

// 获取本周的日记文件
let weeklyPages = dv.pages("#daily")
    .where(p => p.file.day &&
                dv.date(p.file.day) >= weekStart &&
                dv.date(p.file.day) <= weekEnd)
    .sort(p => p.file.day);

dv.list(weeklyPages.map(p => `[[${p.file.name}|${dv.date(p.file.day).toFormat("MM-dd")} (${dv.date(p.file.day).toFormat("ccc")})]]`));
```

## 本周任务分析

### 🚨 逾期和紧急任务
```dataviewjs
// 获取当前周报的日期信息
let currentDate = dv.current().file.day || dv.date("today");
let today = dv.date("today");

// 计算本周的日期范围（周一到周日）
let currentDayOfWeek = currentDate.weekday; // 1=周一, 7=周日
let mondayOffset = currentDayOfWeek - 1; // 距离周一的天数
let weekStart = currentDate.minus({days: mondayOffset}); // 本周周一
let weekEnd = weekStart.plus({days: 6}); // 本周周日

// 获取逾期和紧急任务
let urgentTasks = dv.pages("#daily")
    .file.tasks
    .where(t => {
        try {
            if (!t.path || t.completed || t.status === "-") return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);

            // 检查是否在本周范围内
            if (taskDate < weekStart || taskDate > weekEnd) return false;

            // 检查是否逾期或高优先级
            let isOverdue = t.due && dv.date(t.due) < today;
            let isHighPriority = t.text.includes("⏫") || t.text.includes("🔼");
            let isDueThisWeek = t.due && dv.date(t.due) >= weekStart && dv.date(t.due) <= weekEnd;

            return isOverdue || isHighPriority || isDueThisWeek;
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

if (urgentTasks.length > 0) {
    // 按紧急程度分组
    let overdueGroup = [];
    let highPriorityGroup = [];
    let dueThisWeekGroup = [];

    for (let task of urgentTasks) {
        let taskText = task.text;
        let isOverdue = task.due && dv.date(task.due) < today;
        let isHighPriority = taskText.includes("⏫") || taskText.includes("🔼");

        if (isOverdue) {
            overdueGroup.push(`🚨 ${taskText} (逾期: ${task.due})`);
        } else if (isHighPriority) {
            highPriorityGroup.push(`⚡ ${taskText}`);
        } else {
            dueThisWeekGroup.push(`📅 ${taskText} (截止: ${task.due})`);
        }
    }

    if (overdueGroup.length > 0) {
        dv.header(4, "逾期任务");
        dv.list(overdueGroup);
    }
    if (highPriorityGroup.length > 0) {
        dv.header(4, "高优先级任务");
        dv.list(highPriorityGroup);
    }
    if (dueThisWeekGroup.length > 0) {
        dv.header(4, "本周截止任务");
        dv.list(dueThisWeekGroup);
    }
} else {
    dv.paragraph("✅ 本周没有逾期或紧急任务。");
}
```

### 📋 按优先级分类的未完成任务
```dataviewjs
// 获取本周所有未完成任务（排除已放弃的任务）
let currentDate = dv.current().file.day || dv.date("today");
let currentDayOfWeek = currentDate.weekday;
let mondayOffset = currentDayOfWeek - 1;
let weekStart = currentDate.minus({days: mondayOffset});
let weekEnd = weekStart.plus({days: 6});

let uncompletedTasks = dv.pages("#daily")
    .file.tasks
    .where(t => {
        try {
            if (!t.path) return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return !t.completed &&
                   t.status !== "-" &&
                   taskDate >= weekStart &&
                   taskDate <= weekEnd;
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

// 按优先级分组
let priorityGroups = {
    "⏫ 最高优先级": [],
    "🔼 高优先级": [],
    "📝 普通优先级": [],
    "🔽 低优先级": []
};

for (let task of uncompletedTasks) {
    let taskText = task.text;
    if (taskText.includes("⏫")) {
        priorityGroups["⏫ 最高优先级"].push(taskText);
    } else if (taskText.includes("🔼")) {
        priorityGroups["🔼 高优先级"].push(taskText);
    } else if (taskText.includes("🔽") || taskText.includes("⏬")) {
        priorityGroups["🔽 低优先级"].push(taskText);
    } else {
        priorityGroups["📝 普通优先级"].push(taskText);
    }
}

// 显示分组结果
for (let [priority, tasks] of Object.entries(priorityGroups)) {
    if (tasks.length > 0) {
        dv.header(4, priority);
        dv.list(tasks);
    }
}
```

## 本周已放弃任务
```dataviewjs
// 获取当前周报的日期信息
let currentDate = dv.current().file.day || dv.date("today");

// 计算本周的日期范围（周一到周日）
let currentDayOfWeek = currentDate.weekday; // 1=周一, 7=周日
let mondayOffset = currentDayOfWeek - 1; // 距离周一的天数
let weekStart = currentDate.minus({days: mondayOffset}); // 本周周一
let weekEnd = weekStart.plus({days: 6}); // 本周周日

// 获取本周所有已放弃任务
let abandonedTasks = dv.pages("#daily")
    .file.tasks
    .where(t => {
        try {
            if (!t.path) return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return t.status === "-" &&
                   taskDate >= weekStart &&
                   taskDate <= weekEnd;
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

if (abandonedTasks.length > 0) {
    // 按文件分组显示
    let groupedAbandonedTasks = {};
    for (let task of abandonedTasks) {
        const page = dv.page(task.path);
        const fileName = page.file.name;
        if (!groupedAbandonedTasks[fileName]) {
            groupedAbandonedTasks[fileName] = [];
        }

        // 提取放弃原因
        let taskText = task.text;
        let reasonMatch = taskText.match(/\[reason::\s*([^\]]+)\]/);
        let reason = reasonMatch ? ` (原因: ${reasonMatch[1]})` : "";

        groupedAbandonedTasks[fileName].push(taskText + reason);
    }

    // 显示分组结果
    for (let [fileName, tasks] of Object.entries(groupedAbandonedTasks)) {
        dv.header(3, `[[${fileName}]]`);
        dv.list(tasks);
    }
} else {
    dv.paragraph("本周没有已放弃的任务。");
}
```

### 本周时间投入分析

#### 详细列表
```dataviewjs
// 获取本周所有包含时间跟踪的已完成任务
let currentDate = dv.current().file.day || dv.date("today");

// 计算本周的日期范围（周一到周日）
let currentDayOfWeek = currentDate.weekday; // 1=周一, 7=周日
let mondayOffset = currentDayOfWeek - 1; // 距离周一的天数
let weekStart = currentDate.minus({days: mondayOffset}); // 本周周一
let weekEnd = weekStart.plus({days: 6}); // 本周周日

let tasks = dv.pages("#daily")
    .file.tasks
    .where(t => t.completed && t.text.includes("[act::"))
    .where(t => {
        try {
            if (!t.path) return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return taskDate >= weekStart &&
                   taskDate <= weekEnd;
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

// 创建表格数据
let tableData = tasks.map(task => {
    let text = task.text;

    // 简单的文本清理
    let cleanText = text.replace(/\[est::[^\]]*\]/g, '')
                       .replace(/\[act::[^\]]*\]/g, '')
                       .replace(/✅.*$/g, '')
                       .trim();

    // 提取时间信息
    let actMatch = text.match(/\[act::\s*([^\]]+)\]/);
    let estMatch = text.match(/\[est::\s*([^\]]+)\]/);

    // 安全的日期处理 - 使用任务所在日记的日期
    let taskDate = "未知";
    try {
        if (task.path) {
            const page = dv.page(task.path);
            if (page && page.file.day) {
                taskDate = dv.date(page.file.day).toFormat("MM-dd");
            }
        }
    } catch (error) {
        console.error("日期格式化错误:", error);
        taskDate = "格式错误";
    }

    return [
        cleanText,
        actMatch ? actMatch[1] : "",
        estMatch ? estMatch[1] : "",
        taskDate
    ];
}).filter(row => row[0] && row[0].trim() !== ""); // 过滤空任务

// 安全的排序实现
tableData.sort((a, b) => {
    const dateA = a[3] || "";
    const dateB = b[3] || "";
    return dateB.toString().localeCompare(dateA.toString());
});

dv.table(["任务", "实际耗时", "预估耗时", "日期"], tableData);
```

#### 本周总结
```dataviewjs
// 获取本周已完成的带时间跟踪的任务
let currentDate = dv.current().file.day || dv.date("today");

// 计算本周的日期范围（周一到周日）
let currentDayOfWeek = currentDate.weekday; // 1=周一, 7=周日
let mondayOffset = currentDayOfWeek - 1; // 距离周一的天数
let weekStart = currentDate.minus({days: mondayOffset}); // 本周周一
let weekEnd = weekStart.plus({days: 6}); // 本周周日

let completedTasks = dv.pages("#daily")
    .file.tasks
    .where(t => t.completed && t.text.includes("[act::"))
    .where(t => {
        try {
            if (!t.path) return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return taskDate >= weekStart &&
                   taskDate <= weekEnd;
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

dv.paragraph(`**本周已完成任务数**: ${completedTasks.length}`);
```

#### 本周任务概览
```dataviewjs
// 获取本周所有任务
let currentDate = dv.current().file.day || dv.date("today");

// 计算本周的日期范围（周一到周日）
let currentDayOfWeek = currentDate.weekday; // 1=周一, 7=周日
let mondayOffset = currentDayOfWeek - 1; // 距离周一的天数
let weekStart = currentDate.minus({days: mondayOffset}); // 本周周一
let weekEnd = weekStart.plus({days: 6}); // 本周周日

let allTasks = dv.pages("#daily")
    .file.tasks
    .where(t => {
        try {
            if (!t.path) return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return taskDate >= weekStart &&
                   taskDate <= weekEnd;
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

// 统计任务状态
let completedCount = allTasks.where(t => t.completed).length;
let abandonedCount = allTasks.where(t => t.status === "-").length;
let uncompletedCount = allTasks.where(t => !t.completed && t.status !== "-").length;
let totalCount = allTasks.length;

// 计算完成率（不包括已放弃的任务）
let effectiveTotal = totalCount - abandonedCount;
let completionRate = effectiveTotal > 0 ? Math.round((completedCount / effectiveTotal) * 100) : 0;

// 创建统计表格
dv.table(["状态", "数量", "占比"], [
    ["已完成", completedCount, effectiveTotal > 0 ? `${Math.round((completedCount / effectiveTotal) * 100)}%` : "0%"],
    ["未完成", uncompletedCount, effectiveTotal > 0 ? `${Math.round((uncompletedCount / effectiveTotal) * 100)}%` : "0%"],
    ["已放弃", abandonedCount, totalCount > 0 ? `${Math.round((abandonedCount / totalCount) * 100)}%` : "0%"],
    ["总计", totalCount, "100%"],
    ["完成率", `${completionRate}%`, "(不含已放弃任务)"]
]);
```