# 本周回顾 - 2025年第26周

## 本周日记列表
```dataview
LIST
FROM #daily
WHERE file.day >= date(2025-06-23) AND file.day <= date(2025-06-29)
SORT file.day ASC
```

## 本周未完成任务
```dataview
TASK
FROM #daily
WHERE file.day >= date(2025-06-23) AND file.day <= date(2025-06-29) AND !completed
GROUP BY file.link
```

### 本周时间投入分析

#### 详细列表
```dataviewjs
// 安全查询包装器
function safeQuery(queryFn, fallbackMessage = "查询出错，请检查数据格式") {
    try {
        let result = queryFn();
        if (!result || (Array.isArray(result) && result.length === 0)) {
            dv.paragraph(`ℹ️ 暂无数据显示`);
            return null;
        }
        return result;
    } catch (error) {
        console.error("查询错误:", error);
        dv.paragraph(`⚠️ ${fallbackMessage}`);
        dv.paragraph(`错误详情: ${error.message}`);
        return null;
    }
}

// 安全的日期处理函数
function safeFormatDate(dateValue, format = "MM-dd", fallback = "未知") {
    if (!dateValue) return fallback;
    try {
        return dv.date(dateValue).toFormat(format);
    } catch (error) {
        console.error("日期格式化错误:", error, dateValue);
        return "格式错误";
    }
}

// 执行安全查询
safeQuery(() => {
    // 获取本周所有包含时间跟踪的已完成任务
    let tasks = dv.pages("#daily")
        .file.tasks
        .where(t => t.completed && t.text.includes("[act::") && t.completion)
        .where(t => {
            try {
                const completionDate = dv.date(t.completion);
                return completionDate >= dv.date("2025-06-23") &&
                       completionDate <= dv.date("2025-06-29");
            } catch (error) {
                console.error("日期解析错误:", error, t.completion);
                return false;
            }
        });

    if (tasks.length === 0) {
        throw new Error("未找到包含时间跟踪的已完成任务");
    }

    // 创建表格数据
    let tableData = tasks.map(task => {
        let text = task.text || "";

        // 安全的文本清理
        let cleanText = "";
        try {
            cleanText = text.replace(/\[est::[^\]]*\]/g, '')
                           .replace(/\[act::[^\]]*\]/g, '')
                           .replace(/✅.*$/g, '')
                           .trim();
        } catch (error) {
            console.error("文本清理错误:", error);
            cleanText = text;
        }

        // 安全的时间信息提取
        let actTime = "";
        let estTime = "";
        try {
            let actMatch = text.match(/\[act::\s*([^\]]+)\]/);
            let estMatch = text.match(/\[est::\s*([^\]]+)\]/);
            actTime = actMatch ? actMatch[1] : "";
            estTime = estMatch ? estMatch[1] : "";
        } catch (error) {
            console.error("时间信息提取错误:", error);
        }

        // 使用安全的日期处理函数
        let completionDate = safeFormatDate(task.completion);

        return [cleanText, actTime, estTime, completionDate];
    }).filter(row => row[0] && row[0].trim() !== ""); // 过滤空任务

    // 安全的排序实现
    try {
        tableData.sort((a, b) => {
            const dateA = (a[3] || "").toString();
            const dateB = (b[3] || "").toString();
            return dateB.localeCompare(dateA);
        });
    } catch (error) {
        console.error("排序错误:", error);
    }

    // 显示结果
    dv.table(["任务", "实际耗时", "预估耗时", "日期"], tableData);
    return tableData;
}, "时间投入分析查询失败");
```

#### 本周总结
```dataviewjs
// 获取本周已完成的带时间跟踪的任务
let completedTasks = dv.pages("#daily")
    .file.tasks
    .where(t => t.completed && t.text.includes("[act::") && t.completion)
    .where(t => {
        try {
            const completionDate = dv.date(t.completion);
            return completionDate >= dv.date("2025-06-23") &&
                   completionDate <= dv.date("2025-06-29");
        } catch (error) {
            console.error("日期解析错误:", error, t.completion);
            return false;
        }
    });

dv.paragraph(`**本周已完成任务数**: ${completedTasks.length}`);
```

#### 本周任务概览
```dataviewjs
// 获取本周所有任务
let allTasks = dv.pages("#daily")
    .file.tasks
    .where(t => {
        try {
            if (!t.path) return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return taskDate >= dv.date("2025-06-23") &&
                   taskDate <= dv.date("2025-06-29");
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

// 统计任务状态
let completedCount = allTasks.where(t => t.completed).length;
let uncompletedCount = allTasks.where(t => !t.completed).length;
let totalCount = allTasks.length;

// 创建统计表格
dv.table(["状态", "数量"], [
    ["已完成", completedCount],
    ["未完成", uncompletedCount],
    ["总计", totalCount]
]);
```