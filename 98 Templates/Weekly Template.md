# 本周回顾 {{date:YYYY-[W]ww}}

## 本周日记列表
```dataviewjs
// 获取当前周报的日期信息
let currentDate = dv.current().file.day || dv.date("today");

// 计算本周的日期范围（周一到周日）
let currentDayOfWeek = currentDate.weekday; // 1=周一, 7=周日
let mondayOffset = currentDayOfWeek - 1; // 距离周一的天数
let weekStart = currentDate.minus({days: mondayOffset}); // 本周周一
let weekEnd = weekStart.plus({days: 6}); // 本周周日

dv.paragraph(`**本周范围**: ${weekStart.toFormat("MM-dd")} (周一) 到 ${weekEnd.toFormat("MM-dd")} (周日)`);

// 获取本周的日记文件
let weeklyPages = dv.pages("#daily")
    .where(p => p.file.day &&
                dv.date(p.file.day) >= weekStart &&
                dv.date(p.file.day) <= weekEnd)
    .sort(p => p.file.day);

dv.list(weeklyPages.map(p => `[[${p.file.name}|${dv.date(p.file.day).toFormat("MM-dd")} (${dv.date(p.file.day).toFormat("ccc")})]]`));
```

## 本周未完成任务
```dataviewjs
// 获取当前周报的日期信息
let currentDate = dv.current().file.day || dv.date("today");

// 计算本周的日期范围（周一到周日）
let currentDayOfWeek = currentDate.weekday; // 1=周一, 7=周日
let mondayOffset = currentDayOfWeek - 1; // 距离周一的天数
let weekStart = currentDate.minus({days: mondayOffset}); // 本周周一
let weekEnd = weekStart.plus({days: 6}); // 本周周日

// 获取本周所有未完成任务
let uncompletedTasks = dv.pages("#daily")
    .file.tasks
    .where(t => {
        try {
            if (!t.path) return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return !t.completed &&
                   taskDate >= weekStart &&
                   taskDate <= weekEnd;
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

// 按文件分组显示
let groupedTasks = {};
for (let task of uncompletedTasks) {
    const page = dv.page(task.path);
    const fileName = page.file.name;
    if (!groupedTasks[fileName]) {
        groupedTasks[fileName] = [];
    }
    groupedTasks[fileName].push(task.text);
}

// 显示分组结果
for (let [fileName, tasks] of Object.entries(groupedTasks)) {
    dv.header(3, `[[${fileName}]]`);
    dv.list(tasks);
}
```

### 本周时间投入分析

#### 详细列表
```dataviewjs
// 获取本周所有包含时间跟踪的已完成任务
let currentDate = dv.current().file.day || dv.date("today");

// 计算本周的日期范围（周一到周日）
let currentDayOfWeek = currentDate.weekday; // 1=周一, 7=周日
let mondayOffset = currentDayOfWeek - 1; // 距离周一的天数
let weekStart = currentDate.minus({days: mondayOffset}); // 本周周一
let weekEnd = weekStart.plus({days: 6}); // 本周周日

let tasks = dv.pages("#daily")
    .file.tasks
    .where(t => t.completed && t.text.includes("[act::"))
    .where(t => {
        try {
            if (!t.path) return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return taskDate >= weekStart &&
                   taskDate <= weekEnd;
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

// 创建表格数据
let tableData = tasks.map(task => {
    let text = task.text;

    // 简单的文本清理
    let cleanText = text.replace(/\[est::[^\]]*\]/g, '')
                       .replace(/\[act::[^\]]*\]/g, '')
                       .replace(/✅.*$/g, '')
                       .trim();

    // 提取时间信息
    let actMatch = text.match(/\[act::\s*([^\]]+)\]/);
    let estMatch = text.match(/\[est::\s*([^\]]+)\]/);

    // 安全的日期处理 - 使用任务所在日记的日期
    let taskDate = "未知";
    try {
        if (task.path) {
            const page = dv.page(task.path);
            if (page && page.file.day) {
                taskDate = dv.date(page.file.day).toFormat("MM-dd");
            }
        }
    } catch (error) {
        console.error("日期格式化错误:", error);
        taskDate = "格式错误";
    }

    return [
        cleanText,
        actMatch ? actMatch[1] : "",
        estMatch ? estMatch[1] : "",
        taskDate
    ];
}).filter(row => row[0] && row[0].trim() !== ""); // 过滤空任务

// 安全的排序实现
tableData.sort((a, b) => {
    const dateA = a[3] || "";
    const dateB = b[3] || "";
    return dateB.toString().localeCompare(dateA.toString());
});

dv.table(["任务", "实际耗时", "预估耗时", "日期"], tableData);
```

#### 本周总结
```dataviewjs
// 获取本周已完成的带时间跟踪的任务
let currentDate = dv.current().file.day || dv.date("today");

// 计算本周的日期范围（周一到周日）
let currentDayOfWeek = currentDate.weekday; // 1=周一, 7=周日
let mondayOffset = currentDayOfWeek - 1; // 距离周一的天数
let weekStart = currentDate.minus({days: mondayOffset}); // 本周周一
let weekEnd = weekStart.plus({days: 6}); // 本周周日

let completedTasks = dv.pages("#daily")
    .file.tasks
    .where(t => t.completed && t.text.includes("[act::"))
    .where(t => {
        try {
            if (!t.path) return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return taskDate >= weekStart &&
                   taskDate <= weekEnd;
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

dv.paragraph(`**本周已完成任务数**: ${completedTasks.length}`);
```

#### 本周任务概览
```dataviewjs
// 获取本周所有任务
let currentDate = dv.current().file.day || dv.date("today");

// 计算本周的日期范围（周一到周日）
let currentDayOfWeek = currentDate.weekday; // 1=周一, 7=周日
let mondayOffset = currentDayOfWeek - 1; // 距离周一的天数
let weekStart = currentDate.minus({days: mondayOffset}); // 本周周一
let weekEnd = weekStart.plus({days: 6}); // 本周周日

let allTasks = dv.pages("#daily")
    .file.tasks
    .where(t => {
        try {
            if (!t.path) return false;
            const page = dv.page(t.path);
            if (!page || !page.file.day) return false;
            const taskDate = dv.date(page.file.day);
            return taskDate >= weekStart &&
                   taskDate <= weekEnd;
        } catch (error) {
            console.error("任务日期解析错误:", error, t.path);
            return false;
        }
    });

// 统计任务状态
let completedCount = allTasks.where(t => t.completed).length;
let uncompletedCount = allTasks.where(t => !t.completed).length;
let totalCount = allTasks.length;

// 创建统计表格
dv.table(["状态", "数量"], [
    ["已完成", completedCount],
    ["未完成", uncompletedCount],
    ["总计", totalCount]
]);
```