# 任务字段完整说明

## 📋 字段分类总览

### 🔧 **Tasks插件官方字段**
这些字段由Tasks插件原生支持，具有完整的查询和过滤功能：

| 字段 | Emoji | 语法示例 | dataviewjs属性 | 说明 |
|------|-------|----------|----------------|------|
| **截止日期** | 📅 | `📅 2025-07-20` | `t.due` | 任务必须完成的日期 |
| **开始日期** | 🛫 | `🛫 2025-07-18` | `t.start` | 任务可以开始的日期 |
| **安排日期** | ⏰ | `⏰ 2025-07-19` | `t.scheduled` | 计划处理任务的日期 |
| **完成日期** | ✅ | `✅ 2025-07-16` | `t.completion` | 任务实际完成的日期 |
| **创建日期** | ➕ | `➕ 2025-07-15` | `t.created` | 任务创建的日期 |
| **重复规则** | 🔁 | `🔁 every Friday` | `t.recurrence` | 任务重复模式 |
| **优先级** | ⏫🔼🔽⏬ | `⏫` `🔼` `🔽` `⏬` | `t.priority` | 任务优先级 |
| **任务ID** | 🆔 | `🆔 task-001` | `t.id` | 唯一标识符 |
| **依赖关系** | ⬅️ | `⬅️ task-001` | `t.dependsOn` | 依赖的任务ID |

### 🎯 **任务状态（插件定义）**
Tasks插件支持的任务状态符号：

| 状态 | 符号 | 类型 | dataviewjs属性 | 说明 |
|------|------|------|----------------|------|
| **待办** | `[ ]` | TODO | `t.status === " "` | 未开始的任务 |
| **完成** | `[x]` | DONE | `t.completed === true` | 已完成的任务 |
| **进行中** | `[/]` | IN_PROGRESS | `t.status === "/"` | 正在进行的任务 |
| **已取消** | `[-]` | CANCELLED | `t.status === "-"` | 已放弃的任务 |

### 👤 **用户自定义字段**
这些是你自己创建的元数据字段，需要通过正则表达式提取：

| 字段       | 语法示例                       | 正则表达式                          | 用途     |
| -------- | -------------------------- | ------------------------------ | ------ |
| **预估时间** | `[est:: 2h]`               | `/\[est::\s*([^\]]+)\]/`       | 任务预估耗时 |
| **实际时间** | `[act:: 1.5h]`             | `/\[act::\s*([^\]]+)\]/`       | 任务实际耗时 |
| **放弃日期** | `[abandoned:: 2025-07-16]` | `/\[abandoned::\s*([^\]]+)\]/` | 任务放弃日期 |
| **放弃原因** | `[reason:: 优先级调整]`         | `/\[reason::\s*([^\]]+)\]/`    | 任务放弃原因 |

## 🔍 **在dataviewjs中的使用**

### 插件字段查询示例
```javascript
// 查询逾期任务
let overdueTasks = dv.pages("#daily")
    .file.tasks
    .where(t => !t.completed && t.due && dv.date(t.due) < dv.date("today"));

// 查询高优先级任务
let highPriorityTasks = dv.pages("#daily")
    .file.tasks
    .where(t => !t.completed && (t.priority === "highest" || t.priority === "high"));

// 查询本周开始的任务
let thisWeekTasks = dv.pages("#daily")
    .file.tasks
    .where(t => t.start && dv.date(t.start) >= weekStart && dv.date(t.start) <= weekEnd);
```

### 自定义字段提取示例
```javascript
// 提取时间跟踪信息
let extractTimeData = (task) => {
    let estMatch = task.text.match(/\[est::\s*([^\]]+)\]/);
    let actMatch = task.text.match(/\[act::\s*([^\]]+)\]/);
    
    return {
        estimated: estMatch ? estMatch[1] : null,
        actual: actMatch ? actMatch[1] : null
    };
};

// 提取放弃任务信息
let extractAbandonedData = (task) => {
    let abandonedMatch = task.text.match(/\[abandoned::\s*([^\]]+)\]/);
    let reasonMatch = task.text.match(/\[reason::\s*([^\]]+)\]/);
    
    return {
        abandonedDate: abandonedMatch ? abandonedMatch[1] : null,
        reason: reasonMatch ? reasonMatch[1] : "未指定原因"
    };
};
```

## 🎨 **扩展状态系统**
除了基本的四种状态，Tasks插件还支持更多自定义状态：

| 状态 | 符号 | 含义 | 使用场景 |
|------|------|------|----------|
| **等待** | `[?]` | 等待他人反馈 | 需要他人输入的任务 |
| **重要** | `[!]` | 需要特别注意 | 关键任务标记 |
| **信息** | `[i]` | 信息/想法 | 记录想法或信息 |
| **位置** | `[l]` | 位置相关 | 需要在特定地点完成 |
| **预订** | `[b]` | 预订/约会 | 时间敏感的约会 |
| **部分完成** | `[p]` | 部分完成 | 大任务的部分进展 |

## 🚀 **推荐的字段组合**

### 基础任务
```markdown
- [ ] 任务描述 [est:: 2h] #tag
```

### 带截止日期的任务
```markdown
- [ ] 重要任务 [est:: 3h] 📅 2025-07-25 ⏫ #urgent
```

### 项目任务
```markdown
- [ ] 项目需求分析 [est:: 4h] 🛫 2025-07-20 📅 2025-07-25 🆔 req-001 #project-a
- [ ] 项目设计方案 [est:: 6h] 🛫 2025-07-26 📅 2025-07-30 ⬅️ req-001 #project-a
```

### 重复任务
```markdown
- [ ] 周报撰写 [est:: 1h] 🔁 every Friday 📅 2025-07-18 #review
```

### 已放弃任务
```markdown
- [-] 原任务描述 [est:: 2h] [act:: 30m] [abandoned:: 2025-07-16] [reason:: 优先级调整]
```

## 💡 **最佳实践建议**

1. **优先使用插件字段** - 获得完整的查询支持
2. **保持自定义字段简洁** - 避免过度复杂化
3. **统一命名规范** - 确保字段名称一致性
4. **合理组合字段** - 根据任务类型选择合适的字段组合
5. **定期清理** - 移除不再使用的自定义字段

## 🔄 **迁移建议**

如果你想将现有的自定义字段迁移到插件标准字段：

### 时间跟踪迁移
```markdown
# 当前格式
- [x] 任务 [est:: 2h] [act:: 1.5h] ✅ 2025-07-16

# 可选的插件格式（如果插件支持）
- [x] 任务 📅 2025-07-16 ✅ 2025-07-16
```

### 优先级迁移
```markdown
# 当前格式
- [ ] 重要任务 [est:: 2h]

# 插件格式
- [ ] 重要任务 [est:: 2h] ⏫
```

这样的字段体系既保持了与Tasks插件的兼容性，又保留了你现有的时间跟踪功能。
